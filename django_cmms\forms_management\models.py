"""
Forms and Documentation Management models for Naval CMMS application.
Based on analysis of NAVT ATTRIBUTES ENTITIES document - Entity 7: Forms and Documentation.
"""
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.conf import settings
from datetime import date


class FormType(models.Model):
    """
    Types of naval forms and documents.
    """
    name = models.CharField(
        max_length=100,
        help_text="Form type name"
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Form type code"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Form type description"
    )
    
    category = models.CharField(
        max_length=50,
        choices=[
            ('maintenance', 'Maintenance Forms'),
            ('request', 'Request Forms'),
            ('issue', 'Issue Forms'),
            ('due_out', 'Due Out Forms'),
            ('approval', 'Approval Forms'),
            ('inspection', 'Inspection Forms'),
            ('safety', 'Safety Forms'),
            ('administrative', 'Administrative Forms'),
        ],
        help_text="Form category"
    )
    
    requires_approval = models.BooleanField(
        default=False,
        help_text="Whether this form type requires approval"
    )
    
    approval_levels = models.PositiveIntegerField(
        default=1,
        help_text="Number of approval levels required"
    )
    
    template_file = models.FileField(
        upload_to='forms/templates/',
        blank=True,
        null=True,
        help_text="Form template file"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'forms_form_type'
        ordering = ['category', 'name']
        
    def __str__(self):
        return f"{self.code} - {self.name}"


class NavalForm(models.Model):
    """
    Naval forms and documents with workflow management.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('under_review', 'Under Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('archived', 'Archived'),
    ]
    
    PRIORITY_CHOICES = [
        (1, 'Emergency'),
        (2, 'Urgent'),
        (3, 'Normal'),
        (4, 'Low'),
    ]
    
    # Basic Information
    form_id = models.CharField(
        max_length=20,
        unique=True,
        help_text="Unique form identifier"
    )
    
    form_type = models.ForeignKey(
        FormType,
        on_delete=models.PROTECT,
        related_name='forms',
        help_text="Type of form"
    )
    
    title = models.CharField(
        max_length=200,
        help_text="Form title"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Form description"
    )
    
    # Personnel
    issued_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='issued_forms',
        help_text="Person who issued the form"
    )
    
    received_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='received_forms',
        help_text="Person who received the form"
    )
    
    current_approver = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='pending_approvals',
        help_text="Current approver"
    )
    
    # Dates
    date_issued = models.DateField(
        default=timezone.now,
        help_text="Date form was issued"
    )
    
    date_submitted = models.DateField(
        blank=True,
        null=True,
        help_text="Date form was submitted"
    )
    
    date_completed = models.DateField(
        blank=True,
        null=True,
        help_text="Date form was completed"
    )
    
    due_date = models.DateField(
        blank=True,
        null=True,
        help_text="Form due date"
    )
    
    # Status and Priority
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text="Form status"
    )
    
    priority = models.IntegerField(
        choices=PRIORITY_CHOICES,
        default=3,
        help_text="Form priority"
    )
    
    # Related Objects
    related_work_order = models.ForeignKey(
        'work_orders.WorkOrder',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='forms',
        help_text="Related work order"
    )
    
    related_request = models.ForeignKey(
        'inventory.StockRequest',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='forms',
        help_text="Related stock request"
    )
    
    related_equipment = models.ForeignKey(
        'equipment.Equipment',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='forms',
        help_text="Related equipment"
    )
    
    # Form Data
    form_data = models.JSONField(
        default=dict,
        blank=True,
        help_text="Form field data as JSON"
    )
    
    # Attachments
    attachments = models.FileField(
        upload_to='forms/attachments/',
        blank=True,
        null=True,
        help_text="Form attachments"
    )
    
    # Comments and Notes
    comments = models.TextField(
        blank=True,
        help_text="Comments and notes"
    )
    
    rejection_reason = models.TextField(
        blank=True,
        help_text="Reason for rejection"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'forms_naval_form'
        ordering = ['-date_issued']
        
    def __str__(self):
        return f"{self.form_id} - {self.title}"
    
    def get_absolute_url(self):
        return reverse('forms:form_detail', kwargs={'pk': self.pk})
    
    @property
    def is_overdue(self):
        """Check if form is overdue."""
        if not self.due_date or self.status in ['completed', 'cancelled', 'archived']:
            return False
        return self.due_date < date.today()
    
    @property
    def days_overdue(self):
        """Calculate days overdue."""
        if not self.is_overdue:
            return 0
        return (date.today() - self.due_date).days
    
    def generate_form_id(self):
        """Generate unique form ID."""
        from datetime import datetime
        year = datetime.now().year
        form_type_code = self.form_type.code
        
        last_form = NavalForm.objects.filter(
            form_id__startswith=f"{form_type_code}{year}"
        ).order_by('-form_id').first()
        
        if last_form:
            last_num = int(last_form.form_id.split('-')[-1])
            new_num = last_num + 1
        else:
            new_num = 1
        
        return f"{form_type_code}{year}-{new_num:04d}"


class FormApproval(models.Model):
    """
    Form approval workflow tracking.
    """
    APPROVAL_ACTIONS = [
        ('approve', 'Approve'),
        ('reject', 'Reject'),
        ('request_changes', 'Request Changes'),
        ('forward', 'Forward to Next Level'),
    ]
    
    form = models.ForeignKey(
        NavalForm,
        on_delete=models.CASCADE,
        related_name='approvals',
        help_text="Form being approved"
    )
    
    approver = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='form_approvals',
        help_text="Person providing approval"
    )
    
    approval_level = models.PositiveIntegerField(
        help_text="Approval level (1, 2, 3, etc.)"
    )
    
    action = models.CharField(
        max_length=20,
        choices=APPROVAL_ACTIONS,
        help_text="Approval action taken"
    )
    
    approval_date = models.DateTimeField(
        default=timezone.now,
        help_text="Date of approval action"
    )
    
    comments = models.TextField(
        blank=True,
        help_text="Approval comments"
    )
    
    signature = models.CharField(
        max_length=200,
        blank=True,
        help_text="Digital signature or approval code"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'forms_form_approval'
        ordering = ['approval_level', 'approval_date']
        unique_together = ['form', 'approval_level']
        
    def __str__(self):
        return f"{self.form.form_id} - Level {self.approval_level} - {self.action}"


class FormField(models.Model):
    """
    Dynamic form fields for different form types.
    """
    FIELD_TYPES = [
        ('text', 'Text Input'),
        ('textarea', 'Text Area'),
        ('number', 'Number'),
        ('date', 'Date'),
        ('datetime', 'Date and Time'),
        ('select', 'Select Dropdown'),
        ('checkbox', 'Checkbox'),
        ('radio', 'Radio Button'),
        ('file', 'File Upload'),
        ('signature', 'Digital Signature'),
    ]
    
    form_type = models.ForeignKey(
        FormType,
        on_delete=models.CASCADE,
        related_name='fields',
        help_text="Form type this field belongs to"
    )
    
    field_name = models.CharField(
        max_length=100,
        help_text="Field name/identifier"
    )
    
    field_label = models.CharField(
        max_length=200,
        help_text="Field label for display"
    )
    
    field_type = models.CharField(
        max_length=20,
        choices=FIELD_TYPES,
        help_text="Type of field"
    )
    
    is_required = models.BooleanField(
        default=False,
        help_text="Whether field is required"
    )
    
    field_order = models.PositiveIntegerField(
        default=1,
        help_text="Order of field in form"
    )
    
    help_text = models.TextField(
        blank=True,
        help_text="Help text for field"
    )
    
    default_value = models.TextField(
        blank=True,
        help_text="Default field value"
    )
    
    field_options = models.JSONField(
        default=dict,
        blank=True,
        help_text="Field options (for select, radio, etc.)"
    )
    
    validation_rules = models.JSONField(
        default=dict,
        blank=True,
        help_text="Validation rules as JSON"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'forms_form_field'
        ordering = ['form_type', 'field_order']
        unique_together = ['form_type', 'field_name']
        
    def __str__(self):
        return f"{self.form_type.code} - {self.field_label}"


class FormSubmission(models.Model):
    """
    Form submission tracking and audit trail.
    """
    form = models.ForeignKey(
        NavalForm,
        on_delete=models.CASCADE,
        related_name='submissions',
        help_text="Form being submitted"
    )
    
    submitted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='form_submissions',
        help_text="Person who submitted the form"
    )
    
    submission_date = models.DateTimeField(
        default=timezone.now,
        help_text="Date and time of submission"
    )
    
    submission_data = models.JSONField(
        default=dict,
        help_text="Submitted form data as JSON"
    )
    
    ip_address = models.GenericIPAddressField(
        blank=True,
        null=True,
        help_text="IP address of submitter"
    )
    
    user_agent = models.TextField(
        blank=True,
        help_text="User agent string"
    )
    
    submission_hash = models.CharField(
        max_length=64,
        blank=True,
        help_text="Hash of submission data for integrity"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'forms_form_submission'
        ordering = ['-submission_date']
        
    def __str__(self):
        return f"{self.form.form_id} - {self.submission_date}"


class FormTemplate(models.Model):
    """
    Form templates for standardized forms.
    """
    name = models.CharField(
        max_length=200,
        help_text="Template name"
    )
    
    form_type = models.ForeignKey(
        FormType,
        on_delete=models.CASCADE,
        related_name='templates',
        help_text="Form type this template is for"
    )
    
    template_data = models.JSONField(
        default=dict,
        help_text="Template structure and default values"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Template description"
    )
    
    version = models.CharField(
        max_length=20,
        default='1.0',
        help_text="Template version"
    )
    
    is_default = models.BooleanField(
        default=False,
        help_text="Whether this is the default template"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_templates',
        help_text="Template creator"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'forms_form_template'
        ordering = ['form_type', 'name']
        
    def __str__(self):
        return f"{self.form_type.code} - {self.name} v{self.version}"


class FormWorkflow(models.Model):
    """
    Form workflow definitions for approval processes.
    """
    form_type = models.ForeignKey(
        FormType,
        on_delete=models.CASCADE,
        related_name='workflows',
        help_text="Form type this workflow applies to"
    )
    
    name = models.CharField(
        max_length=200,
        help_text="Workflow name"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Workflow description"
    )
    
    workflow_steps = models.JSONField(
        default=list,
        help_text="Workflow steps as JSON array"
    )
    
    is_default = models.BooleanField(
        default=False,
        help_text="Whether this is the default workflow"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'forms_form_workflow'
        ordering = ['form_type', 'name']
        
    def __str__(self):
        return f"{self.form_type.code} - {self.name}"
