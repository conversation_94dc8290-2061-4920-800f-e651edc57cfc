"""
Forms and Documentation Management models for Naval CMMS application.
Based on analysis of NAVT ATTRIBUTES ENTITIES document - Entity 7: Forms and Documentation.
"""
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.conf import settings
from datetime import date


class FormType(models.Model):
    """
    Types of naval forms and documents.
    """
    name = models.CharField(
        max_length=100,
        help_text="Form type name"
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Form type code"
    )

    description = models.TextField(
        blank=True,
        help_text="Form type description"
    )

    category = models.CharField(
        max_length=50,
        choices=[
            ('maintenance', 'Maintenance Forms'),
            ('request', 'Request Forms'),
            ('issue', 'Issue Forms'),
            ('due_out', 'Due Out Forms'),
            ('approval', 'Approval Forms'),
            ('inspection', 'Inspection Forms'),
            ('safety', 'Safety Forms'),
            ('administrative', 'Administrative Forms'),
        ],
        help_text="Form category"
    )

    requires_approval = models.BooleanField(
        default=False,
        help_text="Whether this form type requires approval"
    )

    approval_levels = models.PositiveIntegerField(
        default=1,
        help_text="Number of approval levels required"
    )

    template_file = models.FileField(
        upload_to='forms/templates/',
        blank=True,
        null=True,
        help_text="Form template file"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'forms_form_type'
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.code} - {self.name}"


class NavalForm(models.Model):
    """
    Naval forms and documents with workflow management.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('under_review', 'Under Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('archived', 'Archived'),
    ]

    PRIORITY_CHOICES = [
        (1, 'Emergency'),
        (2, 'Urgent'),
        (3, 'Normal'),
        (4, 'Low'),
    ]

    # Basic Information
    form_id = models.CharField(
        max_length=20,
        unique=True,
        help_text="Unique form identifier"
    )

    form_type = models.ForeignKey(
        FormType,
        on_delete=models.PROTECT,
        related_name='forms',
        help_text="Type of form"
    )

    title = models.CharField(
        max_length=200,
        help_text="Form title"
    )

    description = models.TextField(
        blank=True,
        help_text="Form description"
    )

    # Personnel
    issued_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='issued_forms',
        help_text="Person who issued the form"
    )

    received_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='received_forms',
        help_text="Person who received the form"
    )

    current_approver = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='pending_approvals',
        help_text="Current approver"
    )

    # Dates
    date_issued = models.DateField(
        default=timezone.now,
        help_text="Date form was issued"
    )

    date_submitted = models.DateField(
        blank=True,
        null=True,
        help_text="Date form was submitted"
    )

    date_completed = models.DateField(
        blank=True,
        null=True,
        help_text="Date form was completed"
    )

    due_date = models.DateField(
        blank=True,
        null=True,
        help_text="Form due date"
    )

    # Status and Priority
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text="Form status"
    )

    priority = models.IntegerField(
        choices=PRIORITY_CHOICES,
        default=3,
        help_text="Form priority"
    )

    # Related Objects
    related_work_order = models.ForeignKey(
        'work_orders.WorkOrder',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='forms',
        help_text="Related work order"
    )

    related_request = models.ForeignKey(
        'inventory.StockRequest',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='forms',
        help_text="Related stock request"
    )

    related_equipment = models.ForeignKey(
        'equipment.Equipment',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='forms',
        help_text="Related equipment"
    )

    # Form Data
    form_data = models.JSONField(
        default=dict,
        blank=True,
        help_text="Form field data as JSON"
    )

    # Attachments
    attachments = models.FileField(
        upload_to='forms/attachments/',
        blank=True,
        null=True,
        help_text="Form attachments"
    )

    # Comments and Notes
    comments = models.TextField(
        blank=True,
        help_text="Comments and notes"
    )

    rejection_reason = models.TextField(
        blank=True,
        help_text="Reason for rejection"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'forms_naval_form'
        ordering = ['-date_issued']

    def __str__(self):
        return f"{self.form_id} - {self.title}"

    def get_absolute_url(self):
        return reverse('forms:form_detail', kwargs={'pk': self.pk})

    @property
    def is_overdue(self):
        """Check if form is overdue."""
        if not self.due_date or self.status in ['completed', 'cancelled', 'archived']:
            return False
        return self.due_date < date.today()

    @property
    def days_overdue(self):
        """Calculate days overdue."""
        if not self.is_overdue:
            return 0
        return (date.today() - self.due_date).days

    def generate_form_id(self):
        """Generate unique form ID."""
        from datetime import datetime
        year = datetime.now().year
        form_type_code = self.form_type.code

        last_form = NavalForm.objects.filter(
            form_id__startswith=f"{form_type_code}{year}"
        ).order_by('-form_id').first()

        if last_form:
            last_num = int(last_form.form_id.split('-')[-1])
            new_num = last_num + 1
        else:
            new_num = 1

        return f"{form_type_code}{year}-{new_num:04d}"

    def get_effective_priority(self):
        """Get effective priority considering ship status and form type."""
        try:
            priority_matrix = self.form_type.priority_matrix.first()
            if not priority_matrix:
                return self.priority, 'D'

            # Get ship if related to equipment or work order
            ship = None
            if self.related_equipment and hasattr(self.related_equipment, 'location'):
                ship = getattr(self.related_equipment.location, 'ship', None)
            elif self.related_work_order and hasattr(self.related_work_order, 'equipment'):
                equipment = self.related_work_order.equipment
                if equipment and hasattr(equipment, 'location'):
                    ship = getattr(equipment.location, 'ship', None)

            # Apply ship status modifiers
            if ship and priority_matrix.ship_status_modifiers:
                modifiers = priority_matrix.ship_status_modifiers.get(ship.status, {})
                opd_modifier = modifiers.get('opd_modifier', 0)
                und_modifier = modifiers.get('und_modifier', 0)

                effective_opd = max(1, min(4, priority_matrix.default_opd - opd_modifier))
                effective_und_index = max(0, min(3, ord(priority_matrix.default_und) - ord('A') - und_modifier))
                effective_und = chr(ord('A') + effective_und_index)

                return effective_opd, effective_und

            return priority_matrix.default_opd, priority_matrix.default_und

        except Exception:
            return self.priority, 'D'

    def validate_naval_hierarchy(self, approver):
        """Validate that approver has sufficient rank and command authority."""
        try:
            # Get approval matrix for this form type
            approval_matrices = self.form_type.approval_matrix.filter(is_active=True)
            if not approval_matrices.exists():
                return True, "No approval matrix defined"

            # Get current approval level
            current_level = self.approvals.count() + 1
            matrix = approval_matrices.filter(approval_level=current_level).first()

            if not matrix:
                return True, "No matrix for this approval level"

            # Check if approver has required rank
            approver_personnel = getattr(approver, 'naval_personnel', None)
            if not approver_personnel:
                return False, "Approver not found in naval personnel"

            # Check rank requirement
            if approver_personnel.current_rank.precedence < matrix.minimum_rank_required.precedence:
                return False, f"Minimum rank required: {matrix.minimum_rank_required.name}"

            # Check command level requirement
            if matrix.command_level_required == 'ship':
                # Must be from same ship or higher command
                if self.related_equipment:
                    ship = getattr(self.related_equipment.location, 'ship', None)
                    if ship and approver_personnel.current_unit != ship:
                        # Check if approver is in higher command
                        if not self._is_higher_command(approver_personnel, ship):
                            return False, "Must be from ship command or higher"

            return True, "Validation passed"

        except Exception as e:
            return False, f"Validation error: {str(e)}"

    def _is_higher_command(self, personnel, ship):
        """Check if personnel is in higher command than ship."""
        command_hierarchy = {
            'ship': 1,
            'squadron': 2,
            'command': 3,
            'hq': 4
        }

        personnel_level = command_hierarchy.get(personnel.command_structure.lower(), 0)
        ship_level = command_hierarchy.get(ship.command.lower(), 1)

        return personnel_level > ship_level

    def check_emergency_override(self, approver):
        """Check if approver can override normal approval process in emergency."""
        try:
            approver_personnel = getattr(approver, 'naval_personnel', None)
            if not approver_personnel:
                return False

            # Check if form is marked as emergency
            if self.priority != 1:  # Not emergency priority
                return False

            # Check approval matrices for emergency override ranks
            for matrix in self.form_type.approval_matrix.filter(is_active=True):
                if approver_personnel.current_rank in matrix.emergency_override_ranks.all():
                    return True

            # CO and XO can always override in emergencies
            co_ranks = ['commanding_officer', 'executive_officer']
            if approver_personnel.current_appointment and \
               approver_personnel.current_appointment.code.lower() in co_ranks:
                return True

            return False

        except Exception:
            return False

    def validate_sal_integration(self):
        """Validate form against ship's Shipboard Allowance List if required."""
        try:
            # Get ship configuration
            ship = self._get_related_ship()
            if not ship:
                return True, "No ship context"

            config = ShipFormConfiguration.objects.filter(
                ship=ship,
                form_type=self.form_type,
                sal_integration_required=True
            ).first()

            if not config:
                return True, "SAL integration not required"

            # Check if form involves stock items
            if self.related_request:
                for item in self.related_request.items.all():
                    sal_item = ship.allowance_list.items.filter(product=item.product).first()
                    if not sal_item:
                        return False, f"Item {item.product.name} not authorized in ship's SAL"

                    if item.quantity > sal_item.maximum_quantity:
                        return False, f"Requested quantity exceeds SAL maximum for {item.product.name}"

            return True, "SAL validation passed"

        except Exception as e:
            return False, f"SAL validation error: {str(e)}"

    def validate_sel_integration(self):
        """Validate form against ship's Ship Equipment List if required."""
        try:
            ship = self._get_related_ship()
            if not ship:
                return True, "No ship context"

            config = ShipFormConfiguration.objects.filter(
                ship=ship,
                form_type=self.form_type,
                sel_integration_required=True
            ).first()

            if not config:
                return True, "SEL integration not required"

            # Check if form involves equipment
            if self.related_equipment:
                sel_equipment = ship.equipment_list.equipment_items.filter(
                    equipment=self.related_equipment
                ).first()

                if not sel_equipment:
                    return False, f"Equipment {self.related_equipment.name} not found in ship's SEL"

                if sel_equipment.operational_status == 'decommissioned':
                    return False, f"Equipment {self.related_equipment.name} is decommissioned"

            return True, "SEL validation passed"

        except Exception as e:
            return False, f"SEL validation error: {str(e)}"

    def _get_related_ship(self):
        """Get the ship related to this form."""
        if self.related_equipment and hasattr(self.related_equipment, 'location'):
            return getattr(self.related_equipment.location, 'ship', None)
        elif self.related_work_order and hasattr(self.related_work_order, 'equipment'):
            equipment = self.related_work_order.equipment
            if equipment and hasattr(equipment, 'location'):
                return getattr(equipment.location, 'ship', None)
        return None

    def trigger_automatic_actions(self, trigger_condition):
        """Execute automatic actions based on form state changes."""
        try:
            triggers = self.form_type.action_triggers.filter(
                trigger_condition=trigger_condition,
                is_active=True
            ).order_by('priority_order')

            results = []
            for trigger in triggers:
                # Check condition filters
                if trigger.condition_filters:
                    if not self._check_condition_filters(trigger.condition_filters):
                        continue

                # Execute action
                result = self._execute_action(trigger)
                results.append(result)

            return results

        except Exception as e:
            return [{'error': f"Action trigger error: {str(e)}"}]

    def _check_condition_filters(self, filters):
        """Check if form meets condition filters."""
        for field, condition in filters.items():
            form_value = getattr(self, field, None)
            if isinstance(condition, dict):
                operator = condition.get('operator', 'eq')
                value = condition.get('value')

                if operator == 'eq' and form_value != value:
                    return False
                elif operator == 'in' and form_value not in value:
                    return False
                elif operator == 'gt' and form_value <= value:
                    return False
                # Add more operators as needed
            else:
                if form_value != condition:
                    return False

        return True

    def _execute_action(self, trigger):
        """Execute a specific action trigger."""
        try:
            if trigger.action_type == 'create_stock_request':
                return self._create_stock_request(trigger.action_parameters)
            elif trigger.action_type == 'create_work_order':
                return self._create_work_order(trigger.action_parameters)
            elif trigger.action_type == 'send_notification':
                return self._send_notification(trigger.action_parameters)
            # Add more action types as needed

            return {'success': True, 'action': trigger.action_type}

        except Exception as e:
            return {'error': f"Action execution error: {str(e)}"}

    def _create_stock_request(self, parameters):
        """Create a stock request based on form data."""
        # Implementation would create StockRequest based on parameters
        return {'success': True, 'action': 'stock_request_created'}

    def _create_work_order(self, parameters):
        """Create a work order based on form data."""
        # Implementation would create WorkOrder based on parameters
        return {'success': True, 'action': 'work_order_created'}

    def _send_notification(self, parameters):
        """Send notification based on form data."""
        # Implementation would send notifications based on parameters
        return {'success': True, 'action': 'notification_sent'}


class FormApproval(models.Model):
    """
    Form approval workflow tracking.
    """
    APPROVAL_ACTIONS = [
        ('approve', 'Approve'),
        ('reject', 'Reject'),
        ('request_changes', 'Request Changes'),
        ('forward', 'Forward to Next Level'),
    ]

    form = models.ForeignKey(
        NavalForm,
        on_delete=models.CASCADE,
        related_name='approvals',
        help_text="Form being approved"
    )

    approver = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='form_approvals',
        help_text="Person providing approval"
    )

    approval_level = models.PositiveIntegerField(
        help_text="Approval level (1, 2, 3, etc.)"
    )

    action = models.CharField(
        max_length=20,
        choices=APPROVAL_ACTIONS,
        help_text="Approval action taken"
    )

    approval_date = models.DateTimeField(
        default=timezone.now,
        help_text="Date of approval action"
    )

    comments = models.TextField(
        blank=True,
        help_text="Approval comments"
    )

    signature = models.CharField(
        max_length=200,
        blank=True,
        help_text="Digital signature or approval code"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'forms_form_approval'
        ordering = ['approval_level', 'approval_date']
        unique_together = ['form', 'approval_level']

    def __str__(self):
        return f"{self.form.form_id} - Level {self.approval_level} - {self.action}"


class FormField(models.Model):
    """
    Dynamic form fields for different form types.
    """
    FIELD_TYPES = [
        ('text', 'Text Input'),
        ('textarea', 'Text Area'),
        ('number', 'Number'),
        ('date', 'Date'),
        ('datetime', 'Date and Time'),
        ('select', 'Select Dropdown'),
        ('checkbox', 'Checkbox'),
        ('radio', 'Radio Button'),
        ('file', 'File Upload'),
        ('signature', 'Digital Signature'),
    ]

    form_type = models.ForeignKey(
        FormType,
        on_delete=models.CASCADE,
        related_name='fields',
        help_text="Form type this field belongs to"
    )

    field_name = models.CharField(
        max_length=100,
        help_text="Field name/identifier"
    )

    field_label = models.CharField(
        max_length=200,
        help_text="Field label for display"
    )

    field_type = models.CharField(
        max_length=20,
        choices=FIELD_TYPES,
        help_text="Type of field"
    )

    is_required = models.BooleanField(
        default=False,
        help_text="Whether field is required"
    )

    field_order = models.PositiveIntegerField(
        default=1,
        help_text="Order of field in form"
    )

    help_text = models.TextField(
        blank=True,
        help_text="Help text for field"
    )

    default_value = models.TextField(
        blank=True,
        help_text="Default field value"
    )

    field_options = models.JSONField(
        default=dict,
        blank=True,
        help_text="Field options (for select, radio, etc.)"
    )

    validation_rules = models.JSONField(
        default=dict,
        blank=True,
        help_text="Validation rules as JSON"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'forms_form_field'
        ordering = ['form_type', 'field_order']
        unique_together = ['form_type', 'field_name']

    def __str__(self):
        return f"{self.form_type.code} - {self.field_label}"


class FormSubmission(models.Model):
    """
    Form submission tracking and audit trail.
    """
    form = models.ForeignKey(
        NavalForm,
        on_delete=models.CASCADE,
        related_name='submissions',
        help_text="Form being submitted"
    )

    submitted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='form_submissions',
        help_text="Person who submitted the form"
    )

    submission_date = models.DateTimeField(
        default=timezone.now,
        help_text="Date and time of submission"
    )

    submission_data = models.JSONField(
        default=dict,
        help_text="Submitted form data as JSON"
    )

    ip_address = models.GenericIPAddressField(
        blank=True,
        null=True,
        help_text="IP address of submitter"
    )

    user_agent = models.TextField(
        blank=True,
        help_text="User agent string"
    )

    submission_hash = models.CharField(
        max_length=64,
        blank=True,
        help_text="Hash of submission data for integrity"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'forms_form_submission'
        ordering = ['-submission_date']

    def __str__(self):
        return f"{self.form.form_id} - {self.submission_date}"


class FormTemplate(models.Model):
    """
    Form templates for standardized forms.
    """
    name = models.CharField(
        max_length=200,
        help_text="Template name"
    )

    form_type = models.ForeignKey(
        FormType,
        on_delete=models.CASCADE,
        related_name='templates',
        help_text="Form type this template is for"
    )

    template_data = models.JSONField(
        default=dict,
        help_text="Template structure and default values"
    )

    description = models.TextField(
        blank=True,
        help_text="Template description"
    )

    version = models.CharField(
        max_length=20,
        default='1.0',
        help_text="Template version"
    )

    is_default = models.BooleanField(
        default=False,
        help_text="Whether this is the default template"
    )

    is_active = models.BooleanField(default=True)

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_templates',
        help_text="Template creator"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'forms_form_template'
        ordering = ['form_type', 'name']

    def __str__(self):
        return f"{self.form_type.code} - {self.name} v{self.version}"


class FormWorkflow(models.Model):
    """
    Form workflow definitions for approval processes.
    """
    form_type = models.ForeignKey(
        FormType,
        on_delete=models.CASCADE,
        related_name='workflows',
        help_text="Form type this workflow applies to"
    )

    name = models.CharField(
        max_length=200,
        help_text="Workflow name"
    )

    description = models.TextField(
        blank=True,
        help_text="Workflow description"
    )

    workflow_steps = models.JSONField(
        default=list,
        help_text="Workflow steps as JSON array"
    )

    is_default = models.BooleanField(
        default=False,
        help_text="Whether this is the default workflow"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'forms_form_workflow'
        ordering = ['form_type', 'name']

    def __str__(self):
        return f"{self.form_type.code} - {self.name}"


class NavalFormApprovalMatrix(models.Model):
    """
    Define approval requirements based on naval hierarchy and command structure.
    """
    COMMAND_LEVELS = [
        ('ship', 'Ship Level'),
        ('squadron', 'Squadron Level'),
        ('command', 'Command Level'),
        ('hq', 'Headquarters Level'),
    ]

    form_type = models.ForeignKey(
        FormType,
        on_delete=models.CASCADE,
        related_name='approval_matrix',
        help_text="Form type this matrix applies to"
    )

    minimum_rank_required = models.ForeignKey(
        'personnel.Rank',
        on_delete=models.PROTECT,
        related_name='required_for_forms',
        help_text="Minimum rank required for approval"
    )

    command_level_required = models.CharField(
        max_length=20,
        choices=COMMAND_LEVELS,
        help_text="Command level required for approval"
    )

    approval_level = models.PositiveIntegerField(
        help_text="Approval level (1=first, 2=second, etc.)"
    )

    can_delegate = models.BooleanField(
        default=False,
        help_text="Whether approval can be delegated"
    )

    emergency_override_ranks = models.ManyToManyField(
        'personnel.Rank',
        blank=True,
        related_name='emergency_override_forms',
        help_text="Ranks that can override this approval in emergencies"
    )

    requires_justification = models.BooleanField(
        default=False,
        help_text="Whether approval requires written justification"
    )

    max_processing_hours = models.PositiveIntegerField(
        default=24,
        help_text="Maximum hours allowed for this approval level"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'forms_naval_approval_matrix'
        ordering = ['form_type', 'approval_level']
        unique_together = ['form_type', 'approval_level']

    def __str__(self):
        return f"{self.form_type.code} - Level {self.approval_level} - {self.minimum_rank_required.name}"


class FormPriorityMatrix(models.Model):
    """
    Map form types to OPD/UND priorities and escalation rules.
    """
    OPD_CHOICES = [
        (1, 'Emergency (OPD-1)'),
        (2, 'Urgent (OPD-2)'),
        (3, 'Priority (OPD-3)'),
        (4, 'Routine (OPD-4)'),
    ]

    UND_CHOICES = [
        ('A', 'Immediate (UND-A)'),
        ('B', 'Priority (UND-B)'),
        ('C', 'Standard (UND-C)'),
        ('D', 'Routine (UND-D)'),
    ]

    form_type = models.ForeignKey(
        FormType,
        on_delete=models.CASCADE,
        related_name='priority_matrix',
        help_text="Form type this priority matrix applies to"
    )

    default_opd = models.IntegerField(
        choices=OPD_CHOICES,
        default=4,
        help_text="Default Operational Priority Designator"
    )

    default_und = models.CharField(
        max_length=1,
        choices=UND_CHOICES,
        default='D',
        help_text="Default Urgency of Need Designator"
    )

    # Escalation Rules
    escalation_triggers = models.JSONField(
        default=dict,
        help_text="Conditions that trigger priority escalation"
    )

    bypass_conditions = models.JSONField(
        default=dict,
        help_text="Conditions that allow workflow bypass"
    )

    emergency_processing_minutes = models.PositiveIntegerField(
        default=60,
        help_text="Maximum minutes for emergency processing"
    )

    auto_escalate_after_hours = models.PositiveIntegerField(
        default=24,
        help_text="Hours after which to auto-escalate"
    )

    # Ship Status Integration
    ship_status_modifiers = models.JSONField(
        default=dict,
        help_text="Priority modifiers based on ship operational status"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'forms_priority_matrix'
        ordering = ['form_type']

    def __str__(self):
        return f"{self.form_type.code} - OPD{self.default_opd}/UND{self.default_und}"


class FormActionTrigger(models.Model):
    """
    Define automatic actions when forms are completed or reach certain states.
    """
    TRIGGER_CONDITIONS = [
        ('on_submit', 'On Form Submission'),
        ('on_approve', 'On Form Approval'),
        ('on_complete', 'On Form Completion'),
        ('on_reject', 'On Form Rejection'),
        ('on_escalate', 'On Priority Escalation'),
    ]

    ACTION_TYPES = [
        ('create_stock_request', 'Create Stock Request'),
        ('create_work_order', 'Create Work Order'),
        ('update_inventory', 'Update Inventory'),
        ('send_notification', 'Send Notification'),
        ('schedule_maintenance', 'Schedule Maintenance'),
        ('update_ship_status', 'Update Ship Status'),
        ('generate_report', 'Generate Report'),
    ]

    form_type = models.ForeignKey(
        FormType,
        on_delete=models.CASCADE,
        related_name='action_triggers',
        help_text="Form type this trigger applies to"
    )

    trigger_condition = models.CharField(
        max_length=20,
        choices=TRIGGER_CONDITIONS,
        help_text="Condition that triggers the action"
    )

    action_type = models.CharField(
        max_length=30,
        choices=ACTION_TYPES,
        help_text="Type of action to perform"
    )

    target_model = models.CharField(
        max_length=100,
        help_text="Target model for the action (e.g., 'inventory.StockRequest')"
    )

    action_parameters = models.JSONField(
        default=dict,
        help_text="Parameters for the action as JSON"
    )

    condition_filters = models.JSONField(
        default=dict,
        help_text="Additional conditions that must be met"
    )

    is_active = models.BooleanField(default=True)

    priority_order = models.PositiveIntegerField(
        default=1,
        help_text="Order of execution when multiple triggers apply"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'forms_action_trigger'
        ordering = ['form_type', 'priority_order']

    def __str__(self):
        return f"{self.form_type.code} - {self.trigger_condition} → {self.action_type}"


class ShipFormConfiguration(models.Model):
    """
    Ship-specific form configurations and customizations.
    """
    ship = models.ForeignKey(
        'fleet.ShipUnit',
        on_delete=models.CASCADE,
        related_name='form_configurations',
        help_text="Ship this configuration applies to"
    )

    form_type = models.ForeignKey(
        FormType,
        on_delete=models.CASCADE,
        related_name='ship_configurations',
        help_text="Form type being configured"
    )

    is_enabled = models.BooleanField(
        default=True,
        help_text="Whether this form type is enabled for this ship"
    )

    custom_workflow = models.ForeignKey(
        FormWorkflow,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='ship_configurations',
        help_text="Custom workflow for this ship"
    )

    # Integration Requirements
    sal_integration_required = models.BooleanField(
        default=False,
        help_text="Whether form must validate against ship's SAL"
    )

    sel_integration_required = models.BooleanField(
        default=False,
        help_text="Whether form must validate against ship's SEL"
    )

    equipment_validation_required = models.BooleanField(
        default=False,
        help_text="Whether form must validate equipment exists on ship"
    )

    personnel_qualification_check = models.BooleanField(
        default=False,
        help_text="Whether to check personnel qualifications"
    )

    # Ship-Specific Settings
    custom_approval_matrix = models.JSONField(
        default=dict,
        help_text="Ship-specific approval requirements"
    )

    custom_field_defaults = models.JSONField(
        default=dict,
        help_text="Ship-specific default field values"
    )

    operational_status_restrictions = models.JSONField(
        default=dict,
        help_text="Restrictions based on ship operational status"
    )

    # Notification Settings
    notification_recipients = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name='ship_form_notifications',
        help_text="Additional personnel to notify for this form type"
    )

    auto_cc_command = models.BooleanField(
        default=False,
        help_text="Whether to automatically CC ship command"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'forms_ship_configuration'
        ordering = ['ship', 'form_type']
        unique_together = ['ship', 'form_type']

    def __str__(self):
        return f"{self.ship.name} - {self.form_type.code}"
