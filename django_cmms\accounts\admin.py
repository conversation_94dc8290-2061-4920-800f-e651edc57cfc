"""
Admin configuration for accounts app.
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import User, UserProfile, UserSession


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Enhanced User admin with CMMS-specific fields."""
    
    list_display = [
        'username', 'email', 'first_name', 'last_name', 
        'role', 'department', 'is_available', 'is_active', 'last_login'
    ]
    
    list_filter = [
        'role', 'craft', 'department', 'is_available', 
        'is_active', 'is_staff', 'date_joined'
    ]
    
    search_fields = [
        'username', 'first_name', 'last_name', 'email', 
        'employee_id', 'department'
    ]
    
    ordering = ['last_name', 'first_name']
    
    fieldsets = BaseUserAdmin.fieldsets + (
        ('CMMS Information', {
            'fields': (
                'role', 'employee_id', 'phone', 'department', 
                'craft', 'is_available'
            )
        }),
        ('System Information', {
            'fields': ('last_login_ip',),
            'classes': ('collapse',)
        }),
    )
    
    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('CMMS Information', {
            'fields': (
                'first_name', 'last_name', 'email', 'role', 
                'employee_id', 'department', 'craft'
            )
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('profile')


class UserProfileInline(admin.StackedInline):
    """Inline admin for UserProfile."""
    model = UserProfile
    can_delete = False
    verbose_name_plural = 'Profile Information'
    
    fields = [
        'avatar', 'bio', 'certifications', 'skills',
        'emergency_contact_name', 'emergency_contact_phone',
        'hire_date', 'supervisor'
    ]


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """Admin for UserProfile model."""
    
    list_display = [
        'user', 'hire_date', 'supervisor', 'has_avatar', 'updated_at'
    ]
    
    list_filter = ['hire_date', 'supervisor']
    
    search_fields = [
        'user__username', 'user__first_name', 'user__last_name',
        'certifications', 'skills'
    ]
    
    raw_id_fields = ['user', 'supervisor']
    
    fieldsets = [
        ('Basic Information', {
            'fields': ['user', 'avatar', 'bio']
        }),
        ('Professional Information', {
            'fields': ['certifications', 'skills', 'hire_date', 'supervisor']
        }),
        ('Emergency Contact', {
            'fields': ['emergency_contact_name', 'emergency_contact_phone'],
            'classes': ['collapse']
        }),
    ]
    
    def has_avatar(self, obj):
        """Check if user has an avatar."""
        if obj.avatar:
            return format_html(
                '<img src="{}" width="30" height="30" style="border-radius: 50%;" />',
                obj.avatar.url
            )
        return "No Avatar"
    has_avatar.short_description = "Avatar"


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    """Admin for UserSession model."""
    
    list_display = [
        'user', 'ip_address', 'login_time', 'last_activity', 
        'is_active', 'session_duration'
    ]
    
    list_filter = [
        'is_active', 'login_time', 'last_activity'
    ]
    
    search_fields = [
        'user__username', 'user__first_name', 'user__last_name',
        'ip_address', 'session_key'
    ]
    
    readonly_fields = [
        'session_key', 'login_time', 'last_activity', 
        'logout_time', 'session_duration'
    ]
    
    date_hierarchy = 'login_time'
    
    def session_duration(self, obj):
        """Display session duration."""
        duration = obj.duration
        hours, remainder = divmod(duration.total_seconds(), 3600)
        minutes, _ = divmod(remainder, 60)
        return f"{int(hours)}h {int(minutes)}m"
    session_duration.short_description = "Duration"
    
    def has_add_permission(self, request):
        """Disable adding sessions manually."""
        return False


# Customize admin site
admin.site.site_header = "CMMS Administration"
admin.site.site_title = "CMMS Admin"
admin.site.index_title = "Welcome to CMMS Administration"
