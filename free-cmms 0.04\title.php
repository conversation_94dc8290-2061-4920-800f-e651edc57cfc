<?PHP 
include("./config.inc.php");
session_save_path($session_save_path);
session_start(); ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>
<head>

    <meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
  
    <title>Submit a Work Order</title>
 
<!-- Include style sheet to make web form similar to paper form --> 

<?PHP 

include("./libraries/browser.inc.php"); //detect the browser
include("./styles/dynamic_css.php");    //set up font sizes, etc per browser
css_site("title.css");               //applice this to the style sheet

?>

</head>

<body bgcolor="#F5F5F5">

    <div class="logo">
        <img src="./images/logo.png">
    </div>

    <div class="tabs">

<?PHP

/*
 * Make the tabs across the top of the page
 */
$html = tab_html('work_request' , 'Work Request') .
        tab_html('work_orders'  , 'Work Orders') .
        tab_html('trouble_calls', 'Trouble Calls');


if($_SESSION['group'] == 'lead' || $_SESSION['group'] == 'manager')
{
  $html .= tab_html('pm'   , 'P.M.');
}

if($_SESSION['group'] == 'manager')
{
 
  $html .= tab_html('admin', 'Admin');
}

$html .= "<form style=\"display:inline\" action=\"search.php\" target=\"maintmain\" method=\"post\" name=\"search_form\"><input size=\"10\" type=\"text\" name=\"search_string\"><input type=\"submit\" Value=\"Search\"></form>";

echo $html;

?>


    </div>



<div class="personalBar">
<div class="location"> <?PHP echo str_replace("_", " ", $_SESSION['nav']) ?> >> 

<?PHP 
if(isset($_GET['title']))
    {
      echo $_GET['title'];
    }
   else
    {
      echo "home";
    }
?> 
</div>

<div class="auth">
<?PHP

if(!empty($_SESSION['user']))
   {
     echo $_SESSION['user'] . " is logged in :: <a href=\"auth.php\" target=\"maintmain\">logout</a>";
   }
   else
    {
      echo "You are not logged in :: <a href=\"auth.php\" target=\"maintmain\">login</a>";
    }
?>
</div>
</div>

<?PHP
echo "<h1>NN CMMS</h1>";
?>

</body>
</html>

<?PHP

/*
 * sets the class of the tab
 */
function link_class($name, $nav)
{
  if($nav == $name)
    {
      $class = 'selected';
    } 
  else
    {
      $class = 'plain';
    }
  return $class;

} //end link_class();

/*
 *Makes html for each navigation tab
 *
 * @param    string nav variable for url
 *           string text to be displayed in tab
 */

function tab_html($nav, $text)
{

  $nav_html = "<a href=\"index.php?nav=$nav\"  target=\"_top\" class=\"" . link_class("$nav", $_SESSION['nav']) . "\">
	    $text
	</a>";

  return $nav_html;
}
?>