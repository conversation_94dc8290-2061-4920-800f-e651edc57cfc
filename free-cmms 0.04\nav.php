<?PHP 
include('config.inc.php');
session_save_path($session_save_path);
session_start(); 
/*
 * The file is organized as follows
 * -bail if user is not logged in
 * -setup a database connection
 * -load the menu data from menu.inc.php
 * -based on the user's group, build their menu and store in variable $menu
 * -exit php and make the html document head. Include javascript and style files
 * -insert the users menu into the html body
 * -finish html and viola
 */


/*
 *If the user is not logged in, exit without displaying any content
 */
if(empty($_SESSION['user']))
{
  exit;
}


$connection=mysql_connect($hostName,$userName,$password) or die('Could not connect to the database server');
$db = mysql_select_db($databaseName, $connection) or die ("Unable to select database.");


include("./menu.inc.php"); //all of the menu data
include("./libraries/FC_Menu.php");           //menu class file


$menu = new Menu(); //creat a new menu object

switch($_SESSION['nav'])
{
 case 'trouble_calls':
   $menu->addCategory('Trouble Calls');
   $menu->addLink('HJ_RECENT');
   $menu->addLink('HJ_ALL');
   $menu->addLink('hot_job');
   break;

 case 'work_request':
   $menu->addCategory('Work Request');
   $menu->addLink('WR_ALL');
   $menu->addLink('new_wr');
   break;

 case 'work_orders':
   $menu->addCategory('Backlog');
       $menu->addlink('WO_BACKLOG');
   
   $menu->addCategory('Assigned');
       $menu->addLink('WO_PENDING');
       $menu->addLink('WO_SUSPENDED');
      
       $menu->addCategory('Finished');
       $menu->addLink('WO_FINISHED');

   $menu->addCategory('Manage');
      $menu->addLink('WO_ALL');
      $menu->addLink('WO_RECENT');
      $menu->addLink('WO_RECENT_CLOSED');
      $menu->addLink('WO_AUDIT');
      $menu->addLink('NEW_TO_YOU');
      $menu->addLink('glance');

      $menu->addCategory('Create & Edit');
      $menu->addLink('new_wo');
      $menu->addLink('number');
   break;

 case 'admin':
   $menu->addCategory('Administration');
       $menu->addSimpleLink('Equipment', './equipment.php', 'maintmain');
       $menu->addSimpleLink('Personnel', './personnel.php', 'maintmain');
       $menu->addSimpleLink('Access'   , './access.php'   , 'maintmain');
       $menu->addSimpleLink('Configure'    , './configure.php'    , 'maintmain');
   break;

}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Frameset//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-frameset.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en" dir="ltr">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">

    <title>NIGERIAN NAVY CMMS</title>

    <!-- Use the 'Input' style sheet @TODO use dynamic style sheet customized  per browser via PHP-->
    <script src="./libraries/functions.js" type="text/javascript" language="javascript"></script>

<style type="text/css">
    <?PHP
        require("./libraries/browser.inc.php");
        require("./styles/dynamic_css.php");
        css_site("nav.css");
    ?>
</style>  

</head>

<body>
<?php echo($menu->toHtml()); ?>

<footer>
    <p style="text-align: center; font-size: small; margin-top: 20px;">
        Powered by Headquarters Logistics Command (HQ LC)
    </p>
</footer>

</body>

</html>

<?php ?>
