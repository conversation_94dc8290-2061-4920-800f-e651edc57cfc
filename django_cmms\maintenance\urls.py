"""
URL configuration for maintenance app.
"""
from django.urls import path
from . import views

app_name = 'maintenance'

urlpatterns = [
    # Maintenance Schedule URLs
    path('schedule/', views.MaintenanceScheduleView.as_view(), name='schedule'),
    path('tasks/', views.MaintenanceTaskListView.as_view(), name='task_list'),
    path('tasks/create/', views.MaintenanceTaskCreateView.as_view(), name='task_create'),
    path('tasks/<int:pk>/', views.MaintenanceTaskDetailView.as_view(), name='task_detail'),
    path('tasks/<int:pk>/edit/', views.MaintenanceTaskUpdateView.as_view(), name='task_edit'),
    path('tasks/<int:pk>/complete/', views.CompleteMaintenanceTaskView.as_view(), name='task_complete'),
    
    # Trouble Call URLs
    path('trouble-calls/', views.TroubleCallListView.as_view(), name='trouble_calls'),
    path('trouble-calls/create/', views.TroubleCallCreateView.as_view(), name='create_trouble_call'),
    path('trouble-calls/<int:pk>/', views.TroubleCallDetailView.as_view(), name='trouble_call_detail'),
    path('trouble-calls/<int:pk>/edit/', views.TroubleCallUpdateView.as_view(), name='trouble_call_edit'),
    path('trouble-calls/<int:pk>/acknowledge/', views.AcknowledgeTroubleCallView.as_view(), name='acknowledge_trouble_call'),
    path('trouble-calls/<int:pk>/resolve/', views.ResolveTroubleCallView.as_view(), name='resolve_trouble_call'),
    path('trouble-calls/<int:pk>/close/', views.CloseTroubleCallView.as_view(), name='close_trouble_call'),
    
    # Calendar and Schedule Views
    path('calendar/', views.MaintenanceCalendarView.as_view(), name='calendar'),
    path('overdue/', views.OverdueMaintenanceView.as_view(), name='overdue'),
    
    # AJAX URLs
    path('ajax/calendar-events/', views.calendar_events_ajax, name='calendar_events_ajax'),
    path('ajax/maintenance-stats/', views.maintenance_stats_ajax, name='maintenance_stats_ajax'),
]
