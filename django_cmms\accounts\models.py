"""
User and Profile models for CMMS application.
"""
from django.contrib.auth.models import AbstractUser
from django.core.exceptions import ValidationError
from django.db import models
from django.urls import reverse
from django.utils import timezone


class User(AbstractUser):
    """
    Enhanced User model with CMMS-specific fields.
    """
    ROLE_CHOICES = [
        ('clerk', 'Clerk'),
        ('mechanic', 'Mechanic'),
        ('lead', 'Lead Mechanic'),
        ('supervisor', 'Supervisor'),
        ('manager', 'Manager'),
        ('admin', 'Administrator'),
    ]

    CRAFT_CHOICES = [
        ('mechanical', 'Mechanical'),
        ('electrical', 'Electrical'),
        ('plumbing', 'Plumbing'),
        ('hvac', 'HVAC'),
        ('general', 'General Maintenance'),
        ('operator', 'Operator'),
        ('other', 'Other'),
    ]

    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='clerk',
        help_text="User's role in the organization"
    )

    employee_id = models.CharField(
        max_length=20,
        unique=True,
        blank=True,
        null=True,
        help_text="Employee ID number"
    )

    phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Contact phone number"
    )

    department = models.CharField(
        max_length=50,
        blank=True,
        help_text="Department or work area"
    )

    craft = models.CharField(
        max_length=20,
        choices=CRAFT_CHOICES,
        blank=True,
        help_text="Primary craft or skill area"
    )

    is_available = models.BooleanField(
        default=True,
        help_text="Whether the user is available for work assignments"
    )

    last_login_ip = models.GenericIPAddressField(
        blank=True,
        null=True,
        help_text="IP address of last login"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'accounts_user'
        ordering = ['last_name', 'first_name']

    def __str__(self):
        return f"{self.get_full_name()} ({self.username})"

    def get_absolute_url(self):
        return reverse('accounts:profile', kwargs={'pk': self.pk})

    def get_full_name(self):
        """Return the full name of the user."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.username

    def get_display_name(self):
        """Return a display-friendly name."""
        full_name = self.get_full_name()
        if full_name != self.username:
            return f"{full_name} ({self.username})"
        return self.username

    @property
    def is_mechanic(self):
        """Check if user is a mechanic or higher."""
        return self.role in ['mechanic', 'lead', 'supervisor', 'manager', 'admin']

    @property
    def is_lead_or_higher(self):
        """Check if user is a lead mechanic or higher."""
        return self.role in ['lead', 'supervisor', 'manager', 'admin']

    @property
    def is_supervisor_or_higher(self):
        """Check if user is a supervisor or higher."""
        return self.role in ['supervisor', 'manager', 'admin']

    @property
    def can_approve_work_orders(self):
        """Check if user can approve work orders."""
        return self.role in ['lead', 'supervisor', 'manager', 'admin']

    @property
    def can_assign_work_orders(self):
        """Check if user can assign work orders."""
        return self.role in ['supervisor', 'manager', 'admin']


class UserProfile(models.Model):
    """
    Extended profile information for users.
    """
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile'
    )

    avatar = models.ImageField(
        upload_to='avatars/',
        blank=True,
        null=True,
        help_text="Profile picture"
    )

    bio = models.TextField(
        blank=True,
        help_text="Brief biography or description"
    )

    certifications = models.TextField(
        blank=True,
        help_text="Professional certifications (one per line)"
    )

    skills = models.TextField(
        blank=True,
        help_text="Technical skills and competencies"
    )

    emergency_contact_name = models.CharField(
        max_length=100,
        blank=True,
        help_text="Emergency contact person"
    )

    emergency_contact_phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Emergency contact phone number"
    )

    hire_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of hire"
    )

    supervisor = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='supervised_users',
        help_text="Direct supervisor"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'accounts_userprofile'

    def __str__(self):
        return f"Profile for {self.user.get_full_name()}"


class UserSession(models.Model):
    """
    Track user sessions for security and analytics.
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sessions'
    )

    session_key = models.CharField(max_length=40, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)

    login_time = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    logout_time = models.DateTimeField(blank=True, null=True)

    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'accounts_usersession'
        ordering = ['-login_time']

    def __str__(self):
        return f"{self.user.username} - {self.login_time}"

    @property
    def duration(self):
        """Calculate session duration."""
        end_time = self.logout_time or timezone.now()
        return end_time - self.login_time
