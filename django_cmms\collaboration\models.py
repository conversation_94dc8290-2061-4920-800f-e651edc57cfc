"""
Collaboration and Communication models for Naval CMMS application.
Based on analysis of rough file requirements for team collaboration, document sharing, and communication.
"""
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.conf import settings


class DocumentCategory(models.Model):
    """
    Categories for organizing documents.
    """
    name = models.CharField(
        max_length=100,
        help_text="Category name"
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Category code"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Category description"
    )
    
    color = models.Char<PERSON>ield(
        max_length=7,
        default='#007bff',
        help_text="Color for visual identification"
    )
    
    requires_approval = models.BooleanField(
        default=False,
        help_text="Whether documents in this category require approval"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'collaboration_document_category'
        ordering = ['name']
        verbose_name_plural = 'Document Categories'
        
    def __str__(self):
        return f"{self.code} - {self.name}"


class Document(models.Model):
    """
    Shared documents, manuals, procedures, etc.
    """
    DOCUMENT_TYPES = [
        ('manual', 'Manual'),
        ('procedure', 'Procedure'),
        ('checklist', 'Checklist'),
        ('report', 'Report'),
        ('drawing', 'Technical Drawing'),
        ('photo', 'Photograph'),
        ('video', 'Video'),
        ('certificate', 'Certificate'),
        ('specification', 'Specification'),
        ('other', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending_review', 'Pending Review'),
        ('approved', 'Approved'),
        ('published', 'Published'),
        ('archived', 'Archived'),
        ('rejected', 'Rejected'),
    ]
    
    # Basic Information
    title = models.CharField(
        max_length=200,
        help_text="Document title"
    )
    
    document_number = models.CharField(
        max_length=50,
        unique=True,
        help_text="Document number/identifier"
    )
    
    document_type = models.CharField(
        max_length=20,
        choices=DOCUMENT_TYPES,
        help_text="Type of document"
    )
    
    category = models.ForeignKey(
        DocumentCategory,
        on_delete=models.PROTECT,
        related_name='documents',
        help_text="Document category"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Document description"
    )
    
    # File and Version Management
    file = models.FileField(
        upload_to='documents/',
        help_text="Document file"
    )
    
    version = models.CharField(
        max_length=20,
        default='1.0',
        help_text="Document version"
    )
    
    file_size = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="File size in bytes"
    )
    
    # Status and Approval
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text="Document status"
    )
    
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='authored_documents',
        help_text="Document author"
    )
    
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='reviewed_documents',
        help_text="Person who reviewed the document"
    )
    
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='approved_documents',
        help_text="Person who approved the document"
    )
    
    # Dates
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    review_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of review"
    )
    
    approval_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of approval"
    )
    
    expiry_date = models.DateField(
        blank=True,
        null=True,
        help_text="Document expiry date"
    )
    
    # Access Control
    is_public = models.BooleanField(
        default=False,
        help_text="Whether document is publicly accessible"
    )
    
    access_groups = models.ManyToManyField(
        'auth.Group',
        blank=True,
        related_name='accessible_documents',
        help_text="Groups with access to this document"
    )
    
    # Related Objects
    related_equipment = models.ManyToManyField(
        'equipment.Equipment',
        blank=True,
        related_name='documents',
        help_text="Related equipment"
    )
    
    related_work_orders = models.ManyToManyField(
        'work_orders.WorkOrder',
        blank=True,
        related_name='documents',
        help_text="Related work orders"
    )
    
    # Metadata
    tags = models.CharField(
        max_length=500,
        blank=True,
        help_text="Comma-separated tags for searching"
    )
    
    keywords = models.TextField(
        blank=True,
        help_text="Keywords for search indexing"
    )
    
    download_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of times downloaded"
    )
    
    class Meta:
        db_table = 'collaboration_document'
        ordering = ['-updated_at']
        
    def __str__(self):
        return f"{self.document_number} - {self.title}"
    
    def get_absolute_url(self):
        return reverse('collaboration:document_detail', kwargs={'pk': self.pk})
    
    @property
    def is_expired(self):
        """Check if document is expired."""
        if not self.expiry_date:
            return False
        return self.expiry_date < timezone.now().date()
    
    @property
    def file_extension(self):
        """Get file extension."""
        if self.file:
            return self.file.name.split('.')[-1].lower()
        return None
    
    def save(self, *args, **kwargs):
        """Override save to set file size."""
        if self.file and not self.file_size:
            self.file_size = self.file.size
        super().save(*args, **kwargs)


class DocumentVersion(models.Model):
    """
    Version history for documents.
    """
    document = models.ForeignKey(
        Document,
        on_delete=models.CASCADE,
        related_name='versions',
        help_text="Parent document"
    )
    
    version = models.CharField(
        max_length=20,
        help_text="Version number"
    )
    
    file = models.FileField(
        upload_to='documents/versions/',
        help_text="Version file"
    )
    
    change_notes = models.TextField(
        help_text="Notes about changes in this version"
    )
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='document_versions',
        help_text="Person who created this version"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'collaboration_document_version'
        ordering = ['-created_at']
        unique_together = ['document', 'version']
        
    def __str__(self):
        return f"{self.document.title} v{self.version}"


class Discussion(models.Model):
    """
    Discussion threads for collaboration.
    """
    DISCUSSION_TYPES = [
        ('general', 'General Discussion'),
        ('work_order', 'Work Order Discussion'),
        ('equipment', 'Equipment Discussion'),
        ('procedure', 'Procedure Discussion'),
        ('issue', 'Issue/Problem'),
        ('suggestion', 'Suggestion'),
    ]
    
    title = models.CharField(
        max_length=200,
        help_text="Discussion title"
    )
    
    discussion_type = models.CharField(
        max_length=20,
        choices=DISCUSSION_TYPES,
        default='general',
        help_text="Type of discussion"
    )
    
    description = models.TextField(
        help_text="Discussion description"
    )
    
    started_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='started_discussions',
        help_text="Person who started the discussion"
    )
    
    # Related Objects
    related_work_order = models.ForeignKey(
        'work_orders.WorkOrder',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='discussions',
        help_text="Related work order"
    )
    
    related_equipment = models.ForeignKey(
        'equipment.Equipment',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='discussions',
        help_text="Related equipment"
    )
    
    # Status
    is_closed = models.BooleanField(
        default=False,
        help_text="Whether discussion is closed"
    )
    
    is_pinned = models.BooleanField(
        default=False,
        help_text="Whether discussion is pinned"
    )
    
    # Participants
    participants = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name='participated_discussions',
        help_text="Discussion participants"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'collaboration_discussion'
        ordering = ['-updated_at']
        
    def __str__(self):
        return self.title
    
    def get_absolute_url(self):
        return reverse('collaboration:discussion_detail', kwargs={'pk': self.pk})
    
    @property
    def message_count(self):
        """Get total number of messages in discussion."""
        return self.messages.count()
    
    @property
    def last_message(self):
        """Get the last message in the discussion."""
        return self.messages.order_by('-created_at').first()


class DiscussionMessage(models.Model):
    """
    Individual messages in discussions.
    """
    discussion = models.ForeignKey(
        Discussion,
        on_delete=models.CASCADE,
        related_name='messages',
        help_text="Discussion thread"
    )
    
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='discussion_messages',
        help_text="Message author"
    )
    
    content = models.TextField(
        help_text="Message content"
    )
    
    # Attachments
    attachment = models.FileField(
        upload_to='discussions/attachments/',
        blank=True,
        null=True,
        help_text="File attachment"
    )
    
    # Threading
    parent_message = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='replies',
        help_text="Parent message for replies"
    )
    
    # Status
    is_edited = models.BooleanField(
        default=False,
        help_text="Whether message has been edited"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'collaboration_discussion_message'
        ordering = ['created_at']
        
    def __str__(self):
        return f"{self.author.username}: {self.content[:50]}..."
    
    @property
    def reply_count(self):
        """Get number of replies to this message."""
        return self.replies.count()


class Notification(models.Model):
    """
    System notifications for users.
    """
    NOTIFICATION_TYPES = [
        ('work_order', 'Work Order'),
        ('maintenance', 'Maintenance'),
        ('inventory', 'Inventory'),
        ('document', 'Document'),
        ('discussion', 'Discussion'),
        ('system', 'System'),
        ('reminder', 'Reminder'),
    ]
    
    recipient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='notifications',
        help_text="Notification recipient"
    )
    
    notification_type = models.CharField(
        max_length=20,
        choices=NOTIFICATION_TYPES,
        help_text="Type of notification"
    )
    
    title = models.CharField(
        max_length=200,
        help_text="Notification title"
    )
    
    message = models.TextField(
        help_text="Notification message"
    )
    
    # Related Objects (generic foreign key would be better, but keeping simple)
    related_object_id = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="ID of related object"
    )
    
    related_object_type = models.CharField(
        max_length=50,
        blank=True,
        help_text="Type of related object"
    )
    
    # Status
    is_read = models.BooleanField(
        default=False,
        help_text="Whether notification has been read"
    )
    
    is_email_sent = models.BooleanField(
        default=False,
        help_text="Whether email notification was sent"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When notification was read"
    )
    
    class Meta:
        db_table = 'collaboration_notification'
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.recipient.username}: {self.title}"
    
    def mark_as_read(self):
        """Mark notification as read."""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()


class ActivityLog(models.Model):
    """
    Activity log for tracking user actions.
    """
    ACTION_TYPES = [
        ('create', 'Created'),
        ('update', 'Updated'),
        ('delete', 'Deleted'),
        ('view', 'Viewed'),
        ('download', 'Downloaded'),
        ('approve', 'Approved'),
        ('reject', 'Rejected'),
        ('assign', 'Assigned'),
        ('complete', 'Completed'),
        ('login', 'Logged In'),
        ('logout', 'Logged Out'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='activity_logs',
        help_text="User who performed the action"
    )
    
    action_type = models.CharField(
        max_length=20,
        choices=ACTION_TYPES,
        help_text="Type of action"
    )
    
    description = models.CharField(
        max_length=500,
        help_text="Action description"
    )
    
    # Related Objects
    object_type = models.CharField(
        max_length=50,
        blank=True,
        help_text="Type of object acted upon"
    )
    
    object_id = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="ID of object acted upon"
    )
    
    object_name = models.CharField(
        max_length=200,
        blank=True,
        help_text="Name of object acted upon"
    )
    
    # Metadata
    ip_address = models.GenericIPAddressField(
        blank=True,
        null=True,
        help_text="IP address of user"
    )
    
    user_agent = models.TextField(
        blank=True,
        help_text="User agent string"
    )
    
    additional_data = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional data as JSON"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'collaboration_activity_log'
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.user.username} {self.action_type} {self.object_type}"
