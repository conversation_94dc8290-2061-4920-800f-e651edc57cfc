"""
Supply Chain Management models for Naval CMMS application.
Based on analysis of NAVT ATTRIBUTES ENTITIES document - Entity 4: Depots and Entity 5: Supply Control Center.
"""
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.conf import settings
from datetime import date, timedelta
from decimal import Decimal


class SupplyControlCenter(models.Model):
    """
    Supply Control Center (SCC) for managing naval supply chain operations.
    """
    scc_id = models.Char<PERSON>ield(
        max_length=20,
        unique=True,
        help_text="SCC unique identifier"
    )
    
    name = models.Char<PERSON><PERSON>(
        max_length=200,
        help_text="SCC name"
    )
    
    location = models.Char<PERSON>ield(
        max_length=200,
        help_text="SCC location"
    )
    
    # Command Authority
    command_authority = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='commanded_sccs',
        help_text="Officer in charge of SCC operations"
    )
    
    # Area of Responsibility
    area_of_responsibility = models.TextField(
        help_text="Geographic area and units served"
    )
    
    supported_commands = models.<PERSON><PERSON><PERSON><PERSON>(
        max_length=500,
        help_text="Naval commands supported by this SCC"
    )
    
    # Operational Parameters
    priority_system = models.JSONField(
        default=dict,
        help_text="Priority system configuration (OPD, UND)"
    )
    
    approval_chain = models.JSONField(
        default=list,
        help_text="Approval process configuration"
    )
    
    # Performance Metrics
    average_processing_time_hours = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=0,
        help_text="Average request processing time in hours"
    )
    
    requests_processed_monthly = models.PositiveIntegerField(
        default=0,
        help_text="Number of requests processed per month"
    )
    
    # Contact Information
    contact_info = models.JSONField(
        default=dict,
        help_text="Contact information as JSON"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'supply_chain_scc'
        ordering = ['name']
        
    def __str__(self):
        return f"{self.scc_id} - {self.name}"
    
    def get_absolute_url(self):
        return reverse('supply_chain:scc_detail', kwargs={'pk': self.pk})


class NavalDepot(models.Model):
    """
    Naval depots for storing and distributing supplies.
    """
    DEPOT_TYPES = [
        ('LLD', 'Lagos Logistics Depot'),
        ('PHLD', 'Port Harcourt Logistics Depot'),
        ('SAPLD', 'Sapele Logistics Depot'),
        ('CALLD', 'Calabar Logistics Depot'),
        ('REGIONAL', 'Regional Depot'),
        ('FORWARD', 'Forward Operating Depot'),
    ]
    
    # Basic Information
    depot_id = models.CharField(
        max_length=20,
        unique=True,
        help_text="Depot identifier (e.g., LLD, PHLD)"
    )
    
    name = models.CharField(
        max_length=200,
        help_text="Depot name"
    )
    
    depot_type = models.CharField(
        max_length=20,
        choices=DEPOT_TYPES,
        help_text="Type of depot"
    )
    
    location = models.CharField(
        max_length=200,
        help_text="Geographic location"
    )
    
    # Command Structure
    scc = models.ForeignKey(
        SupplyControlCenter,
        on_delete=models.PROTECT,
        related_name='depots',
        help_text="Controlling SCC"
    )
    
    commanding_officer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='commanded_depots',
        help_text="CO of the depot"
    )
    
    # Area of Responsibility
    supported_fleet = models.TextField(
        help_text="Fleet units within depot's AOR"
    )
    
    area_of_responsibility = models.TextField(
        help_text="Geographic area served"
    )
    
    # Capacity and Infrastructure
    total_storage_capacity = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Total storage capacity (cubic meters)"
    )
    
    current_utilization = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        help_text="Current storage utilization percentage"
    )
    
    warehouse_count = models.PositiveIntegerField(
        default=1,
        help_text="Number of warehouses"
    )
    
    cold_storage_capacity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Cold storage capacity (cubic meters)"
    )
    
    hazmat_storage_capacity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Hazardous materials storage capacity"
    )
    
    # Transportation
    transport_modes = models.JSONField(
        default=list,
        help_text="Available transport modes (sea, air, land)"
    )
    
    average_lead_time_days = models.PositiveIntegerField(
        default=7,
        help_text="Average lead time to deliver to ships (days)"
    )
    
    # Operational Parameters
    replenishment_schedule = models.CharField(
        max_length=100,
        help_text="Replenishment frequency and schedule"
    )
    
    minimum_stock_days = models.PositiveIntegerField(
        default=30,
        help_text="Minimum stock level in days of supply"
    )
    
    maximum_stock_days = models.PositiveIntegerField(
        default=90,
        help_text="Maximum stock level in days of supply"
    )
    
    # Performance Metrics
    inventory_accuracy_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=95.0,
        help_text="Inventory accuracy percentage"
    )
    
    fill_rate_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=90.0,
        help_text="Order fill rate percentage"
    )
    
    last_audit_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of last inventory audit"
    )
    
    next_audit_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of next scheduled audit"
    )
    
    # Contact and Communication
    contact_info = models.JSONField(
        default=dict,
        help_text="Contact information as JSON"
    )
    
    communication_systems = models.JSONField(
        default=list,
        help_text="Available communication systems"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'supply_chain_naval_depot'
        ordering = ['depot_id']
        
    def __str__(self):
        return f"{self.depot_id} - {self.name}"
    
    def get_absolute_url(self):
        return reverse('supply_chain:depot_detail', kwargs={'pk': self.pk})
    
    @property
    def is_audit_due(self):
        """Check if inventory audit is due."""
        if not self.next_audit_date:
            return False
        return self.next_audit_date <= date.today()
    
    @property
    def storage_utilization_status(self):
        """Get storage utilization status."""
        if self.current_utilization >= 90:
            return "Critical"
        elif self.current_utilization >= 80:
            return "High"
        elif self.current_utilization >= 60:
            return "Normal"
        else:
            return "Low"


class RequestProcessingQueue(models.Model):
    """
    SCC request processing queue with priority management.
    """
    PRIORITY_LEVELS = [
        (1, 'Emergency (OPD-1)'),
        (2, 'Urgent (OPD-2)'),
        (3, 'Priority (OPD-3)'),
        (4, 'Routine (OPD-4)'),
    ]
    
    URGENCY_LEVELS = [
        ('A', 'Immediate (UND-A)'),
        ('B', 'Priority (UND-B)'),
        ('C', 'Standard (UND-C)'),
        ('D', 'Routine (UND-D)'),
    ]
    
    STATUS_CHOICES = [
        ('queued', 'Queued'),
        ('processing', 'Processing'),
        ('verification', 'Under Verification'),
        ('approval', 'Awaiting Approval'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
    ]
    
    scc = models.ForeignKey(
        SupplyControlCenter,
        on_delete=models.CASCADE,
        related_name='processing_queue',
        help_text="SCC processing this request"
    )
    
    request = models.ForeignKey(
        'inventory.StockRequest',
        on_delete=models.CASCADE,
        related_name='processing_queue',
        help_text="Stock request being processed"
    )
    
    # Priority and Urgency
    operational_priority = models.IntegerField(
        choices=PRIORITY_LEVELS,
        help_text="Operational Priority Designator (OPD)"
    )
    
    urgency_designator = models.CharField(
        max_length=1,
        choices=URGENCY_LEVELS,
        help_text="Urgency of Need Designator (UND)"
    )
    
    # Processing Information
    queue_position = models.PositiveIntegerField(
        help_text="Position in processing queue"
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='queued',
        help_text="Processing status"
    )
    
    assigned_processor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='assigned_requests',
        help_text="Personnel assigned to process request"
    )
    
    # Timing
    queued_at = models.DateTimeField(
        default=timezone.now,
        help_text="When request was queued"
    )
    
    processing_started_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When processing started"
    )
    
    completed_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When processing completed"
    )
    
    estimated_completion = models.DateTimeField(
        blank=True,
        null=True,
        help_text="Estimated completion time"
    )
    
    # Verification and Approval
    verification_notes = models.TextField(
        blank=True,
        help_text="Verification notes"
    )
    
    approval_notes = models.TextField(
        blank=True,
        help_text="Approval notes"
    )
    
    rejection_reason = models.TextField(
        blank=True,
        help_text="Reason for rejection"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'supply_chain_request_queue'
        ordering = ['operational_priority', 'urgency_designator', 'queued_at']
        
    def __str__(self):
        return f"{self.scc.scc_id} - {self.request.request_id} - OPD{self.operational_priority}/UND{self.urgency_designator}"
    
    @property
    def processing_time(self):
        """Calculate processing time."""
        if self.processing_started_at and self.completed_at:
            return self.completed_at - self.processing_started_at
        elif self.processing_started_at:
            return timezone.now() - self.processing_started_at
        return None
    
    @property
    def queue_time(self):
        """Calculate time in queue."""
        start_time = self.processing_started_at or timezone.now()
        return start_time - self.queued_at


class ReplenishmentOrder(models.Model):
    """
    Replenishment orders issued by SCC to depots.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('issued', 'Issued'),
        ('acknowledged', 'Acknowledged'),
        ('in_transit', 'In Transit'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    ]
    
    order_id = models.CharField(
        max_length=20,
        unique=True,
        help_text="Replenishment order ID"
    )
    
    scc = models.ForeignKey(
        SupplyControlCenter,
        on_delete=models.PROTECT,
        related_name='replenishment_orders',
        help_text="Issuing SCC"
    )
    
    depot = models.ForeignKey(
        NavalDepot,
        on_delete=models.PROTECT,
        related_name='replenishment_orders',
        help_text="Receiving depot"
    )
    
    # Order Details
    order_date = models.DateField(
        default=timezone.now,
        help_text="Order date"
    )
    
    required_delivery_date = models.DateField(
        help_text="Required delivery date"
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text="Order status"
    )
    
    # Personnel
    ordered_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='issued_replenishment_orders',
        help_text="Person who issued the order"
    )
    
    acknowledged_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='acknowledged_replenishment_orders',
        help_text="Person who acknowledged the order"
    )
    
    # Delivery Information
    delivery_method = models.CharField(
        max_length=50,
        choices=[
            ('sea', 'Sea Transport'),
            ('air', 'Air Transport'),
            ('land', 'Land Transport'),
            ('rail', 'Rail Transport'),
            ('multimodal', 'Multimodal Transport'),
        ],
        help_text="Delivery method"
    )
    
    estimated_delivery_date = models.DateField(
        blank=True,
        null=True,
        help_text="Estimated delivery date"
    )
    
    actual_delivery_date = models.DateField(
        blank=True,
        null=True,
        help_text="Actual delivery date"
    )
    
    # Additional Information
    special_instructions = models.TextField(
        blank=True,
        help_text="Special handling or delivery instructions"
    )
    
    remarks = models.TextField(
        blank=True,
        help_text="Additional remarks"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'supply_chain_replenishment_order'
        ordering = ['-order_date']
        
    def __str__(self):
        return f"{self.order_id} - {self.depot.depot_id}"
    
    def generate_order_id(self):
        """Generate unique replenishment order ID."""
        from datetime import datetime
        year = datetime.now().year
        last_order = ReplenishmentOrder.objects.filter(
            order_id__startswith=f"RO{year}"
        ).order_by('-order_id').first()
        
        if last_order:
            last_num = int(last_order.order_id.split('-')[-1])
            new_num = last_num + 1
        else:
            new_num = 1
        
        return f"RO{year}-{new_num:04d}"
    
    @property
    def is_overdue(self):
        """Check if delivery is overdue."""
        if self.status in ['delivered', 'cancelled']:
            return False
        return self.required_delivery_date < date.today()


class BackorderManagement(models.Model):
    """
    Management of backordered items and follow-up.
    """
    STATUS_CHOICES = [
        ('active', 'Active Backorder'),
        ('partial_fill', 'Partial Fill'),
        ('fulfilled', 'Fulfilled'),
        ('cancelled', 'Cancelled'),
        ('substituted', 'Substituted'),
    ]
    
    backorder_id = models.CharField(
        max_length=20,
        unique=True,
        help_text="Backorder ID"
    )
    
    original_request = models.ForeignKey(
        'inventory.StockRequest',
        on_delete=models.CASCADE,
        related_name='backorders',
        help_text="Original stock request"
    )
    
    depot = models.ForeignKey(
        NavalDepot,
        on_delete=models.PROTECT,
        related_name='backorders',
        help_text="Depot managing the backorder"
    )
    
    product = models.ForeignKey(
        'inventory.Product',
        on_delete=models.PROTECT,
        related_name='backorders',
        help_text="Backordered product"
    )
    
    # Quantities
    quantity_backordered = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Quantity on backorder"
    )
    
    quantity_filled = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Quantity filled so far"
    )
    
    # Status and Timing
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        help_text="Backorder status"
    )
    
    backorder_date = models.DateField(
        default=timezone.now,
        help_text="Date item was backordered"
    )
    
    expected_availability_date = models.DateField(
        blank=True,
        null=True,
        help_text="Expected availability date"
    )
    
    # Supplier Information
    supplier = models.ForeignKey(
        'inventory.Vendor',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='backorders',
        help_text="Supplier for backordered item"
    )
    
    supplier_reference = models.CharField(
        max_length=100,
        blank=True,
        help_text="Supplier reference number"
    )
    
    # Follow-up
    last_follow_up_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of last follow-up"
    )
    
    next_follow_up_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of next follow-up"
    )
    
    follow_up_notes = models.TextField(
        blank=True,
        help_text="Follow-up notes and communications"
    )
    
    # Alternative Actions
    substitute_product = models.ForeignKey(
        'inventory.Product',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='substitute_backorders',
        help_text="Substitute product if available"
    )
    
    alternative_source = models.CharField(
        max_length=200,
        blank=True,
        help_text="Alternative source for the item"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'supply_chain_backorder_management'
        ordering = ['-backorder_date']
        
    def __str__(self):
        return f"{self.backorder_id} - {self.product.name}"
    
    @property
    def quantity_remaining(self):
        """Calculate remaining quantity to be filled."""
        return self.quantity_backordered - self.quantity_filled
    
    @property
    def fill_percentage(self):
        """Calculate fill percentage."""
        if self.quantity_backordered == 0:
            return 100
        return (self.quantity_filled / self.quantity_backordered) * 100
    
    @property
    def days_on_backorder(self):
        """Calculate days on backorder."""
        return (date.today() - self.backorder_date).days
