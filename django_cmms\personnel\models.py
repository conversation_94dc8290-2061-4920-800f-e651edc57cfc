"""
Personnel Management models for Naval CMMS application.
Based on analysis of NAVT ATTRIBUTES ENTITIES document.
"""
from django.db import models
from django.urls import reverse
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.conf import settings
from datetime import date, timedelta
from decimal import Decimal


class Rank(models.Model):
    """
    Naval ranks and rates with service limits and age ceilings.
    """
    COMMISSION_TYPES = [
        ('officer', 'Officer'),
        ('rating', 'Rating'),
        ('civilian', 'Civilian'),
    ]

    name = models.CharField(
        max_length=100,
        help_text="Rank/Rate name"
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Rank/Rate code"
    )

    commission_type = models.CharField(
        max_length=20,
        choices=COMMISSION_TYPES,
        help_text="Type of commission"
    )

    pay_grade = models.CharField(
        max_length=10,
        blank=True,
        help_text="Pay grade level"
    )

    age_ceiling = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Maximum age allowed for this rank"
    )

    max_service_years = models.PositiveIntegerField(
        default=35,
        help_text="Maximum years of service allowed"
    )

    minimum_time_in_rank = models.PositiveIntegerField(
        default=2,
        help_text="Minimum years required in this rank for promotion"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'personnel_rank'
        ordering = ['commission_type', 'pay_grade']

    def __str__(self):
        return f"{self.code} - {self.name}"


class Appointment(models.Model):
    """
    Naval appointments and positions.
    """
    APPOINTMENT_TYPES = [
        ('command', 'Command Position'),
        ('executive', 'Executive Position'),
        ('department', 'Department Head'),
        ('division', 'Division Officer'),
        ('specialist', 'Specialist'),
        ('technical', 'Technical'),
        ('administrative', 'Administrative'),
    ]

    title = models.CharField(
        max_length=200,
        help_text="Appointment title"
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Appointment code"
    )

    appointment_type = models.CharField(
        max_length=20,
        choices=APPOINTMENT_TYPES,
        help_text="Type of appointment"
    )

    description = models.TextField(
        blank=True,
        help_text="Appointment description and responsibilities"
    )

    required_rank = models.ForeignKey(
        Rank,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='appointments',
        help_text="Minimum rank required for this appointment"
    )

    required_qualifications = models.TextField(
        blank=True,
        help_text="Required qualifications and certifications"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'personnel_appointment'
        ordering = ['title']

    def __str__(self):
        return f"{self.code} - {self.title}"


class NavalPersonnel(models.Model):
    """
    Naval personnel with comprehensive service tracking.
    """
    # Basic Information
    official_number = models.CharField(
        max_length=20,
        unique=True,
        help_text="Unique official service number"
    )

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='naval_personnel',
        help_text="Associated user account"
    )

    # Service Information
    current_rank = models.ForeignKey(
        Rank,
        on_delete=models.PROTECT,
        related_name='personnel',
        help_text="Current rank/rate"
    )

    current_appointment = models.ForeignKey(
        Appointment,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='current_personnel',
        help_text="Current appointment/position"
    )

    current_unit = models.ForeignKey(
        'fleet.ShipUnit',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='current_personnel',
        help_text="Current ship/unit assignment"
    )

    # Naval-Specific Service Information
    commission_type = models.CharField(
        max_length=20,
        choices=[
            ('RC', 'Regular Combatant'),
            ('DSSC', 'Direct Short Service Commission'),
            ('SD', 'Special Duties'),
            ('SSC', 'Short Service Commission'),
            ('NNDC', 'Nigerian Naval Defence College'),
        ],
        help_text="Type of commission"
    )

    department = models.CharField(
        max_length=100,
        choices=[
            ('marine_engineering', 'Marine Engineering'),
            ('weapon_electrical', 'Weapon Electrical'),
            ('navigation', 'Navigation'),
            ('communications', 'Communications'),
            ('logistics', 'Logistics'),
            ('administration', 'Administration'),
            ('medical', 'Medical'),
            ('intelligence', 'Intelligence'),
        ],
        blank=True,
        help_text="Department assignment"
    )

    subspecialization = models.CharField(
        max_length=200,
        blank=True,
        help_text="Subspecialization within primary field"
    )

    command_structure = models.CharField(
        max_length=100,
        choices=[
            ('HQ_LOC', 'HQ Logistics Command'),
            ('WESTERN_COMMAND', 'Western Naval Command'),
            ('EASTERN_COMMAND', 'Eastern Naval Command'),
            ('CENTRAL_COMMAND', 'Central Naval Command'),
            ('TRAINING_COMMAND', 'Training Command'),
        ],
        blank=True,
        help_text="Command structure"
    )

    # Service Dates
    date_of_commission = models.DateField(
        help_text="Date of commission/enlistment"
    )

    date_joined_current_unit = models.DateField(
        blank=True,
        null=True,
        help_text="Date joined current ship/unit"
    )

    date_of_last_promotion = models.DateField(
        blank=True,
        null=True,
        help_text="Date of last promotion"
    )

    run_out_date = models.DateField(
        blank=True,
        null=True,
        help_text="Maximum service end date (ROD)"
    )

    # Personal Information
    date_of_birth = models.DateField(
        help_text="Date of birth"
    )

    place_of_birth = models.CharField(
        max_length=100,
        blank=True,
        help_text="Place of birth"
    )

    next_of_kin = models.CharField(
        max_length=200,
        blank=True,
        help_text="Next of kin information"
    )

    emergency_contact = models.TextField(
        blank=True,
        help_text="Emergency contact details"
    )

    # Professional Information
    specialization = models.CharField(
        max_length=100,
        blank=True,
        help_text="Primary specialization/branch"
    )

    skills = models.TextField(
        blank=True,
        help_text="Technical skills and competencies"
    )

    security_clearance = models.CharField(
        max_length=50,
        blank=True,
        help_text="Security clearance level"
    )

    # Status
    service_status = models.CharField(
        max_length=20,
        choices=[
            ('active', 'Active Service'),
            ('reserve', 'Reserve'),
            ('retired', 'Retired'),
            ('discharged', 'Discharged'),
            ('deceased', 'Deceased'),
        ],
        default='active',
        help_text="Current service status"
    )

    is_available = models.BooleanField(
        default=True,
        help_text="Available for assignments"
    )

    # Medical Information
    medical_category = models.CharField(
        max_length=10,
        blank=True,
        help_text="Medical fitness category"
    )

    medical_restrictions = models.TextField(
        blank=True,
        help_text="Any medical restrictions"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'personnel_naval_personnel'
        ordering = ['current_rank', 'user__last_name', 'user__first_name']
        verbose_name_plural = 'Naval Personnel'

    def __str__(self):
        return f"{self.official_number} - {self.user.get_full_name()}"

    def get_absolute_url(self):
        return reverse('personnel:detail', kwargs={'pk': self.pk})

    @property
    def current_age(self):
        """Calculate current age."""
        today = date.today()
        return today.year - self.date_of_birth.year - (
            (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
        )

    @property
    def years_in_service(self):
        """Calculate total years in service."""
        today = date.today()
        return today.year - self.date_of_commission.year - (
            (today.month, today.day) < (self.date_of_commission.month, self.date_of_commission.day)
        )

    @property
    def seniority_in_rank(self):
        """Calculate years in current rank."""
        if not self.date_of_last_promotion:
            return self.years_in_service

        today = date.today()
        return today.year - self.date_of_last_promotion.year - (
            (today.month, today.day) < (self.date_of_last_promotion.month, self.date_of_last_promotion.day)
        )

    @property
    def time_on_current_unit(self):
        """Calculate time on current ship/unit in days."""
        if not self.date_joined_current_unit:
            return None
        return (date.today() - self.date_joined_current_unit).days

    @property
    def eligible_to_remain_in_service(self):
        """Check if personnel is eligible to remain in service."""
        # Check age ceiling
        if self.current_rank.age_ceiling and self.current_age > self.current_rank.age_ceiling:
            return False

        # Check service years
        if self.years_in_service >= self.current_rank.max_service_years:
            return False

        # Check run out date
        if self.run_out_date and date.today() > self.run_out_date:
            return False

        return True

    @property
    def eligible_for_promotion(self):
        """Check if personnel is eligible for promotion consideration."""
        # Check minimum time in rank
        if self.seniority_in_rank < self.current_rank.minimum_time_in_rank:
            return False

        # Check if still eligible to serve
        if not self.eligible_to_remain_in_service:
            return False

        return True

    def calculate_run_out_date(self):
        """Calculate run out date based on commission date and rank limits."""
        max_service_date = self.date_of_commission + timedelta(
            days=self.current_rank.max_service_years * 365
        )

        if self.current_rank.age_ceiling:
            max_age_date = date(
                self.date_of_birth.year + self.current_rank.age_ceiling,
                self.date_of_birth.month,
                self.date_of_birth.day
            )
            return min(max_service_date, max_age_date)

        return max_service_date


class PersonnelAppointmentHistory(models.Model):
    """
    History of appointments held by personnel.
    """
    personnel = models.ForeignKey(
        NavalPersonnel,
        on_delete=models.CASCADE,
        related_name='appointment_history',
        help_text="Personnel member"
    )

    appointment = models.ForeignKey(
        Appointment,
        on_delete=models.PROTECT,
        related_name='appointment_history',
        help_text="Appointment held"
    )

    unit = models.ForeignKey(
        'fleet.ShipUnit',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='appointment_history',
        help_text="Ship/unit where appointment was held"
    )

    start_date = models.DateField(
        help_text="Start date of appointment"
    )

    end_date = models.DateField(
        blank=True,
        null=True,
        help_text="End date of appointment"
    )

    is_current = models.BooleanField(
        default=False,
        help_text="Whether this is the current appointment"
    )

    performance_rating = models.CharField(
        max_length=20,
        choices=[
            ('outstanding', 'Outstanding'),
            ('excellent', 'Excellent'),
            ('satisfactory', 'Satisfactory'),
            ('needs_improvement', 'Needs Improvement'),
            ('unsatisfactory', 'Unsatisfactory'),
        ],
        blank=True,
        help_text="Performance rating in this appointment"
    )

    remarks = models.TextField(
        blank=True,
        help_text="Remarks about performance in this appointment"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'personnel_appointment_history'
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.personnel.user.get_full_name()} - {self.appointment.title}"

    @property
    def duration_days(self):
        """Calculate duration of appointment in days."""
        end_date = self.end_date or date.today()
        return (end_date - self.start_date).days


class PromotionHistory(models.Model):
    """
    History of promotions for personnel.
    """
    personnel = models.ForeignKey(
        NavalPersonnel,
        on_delete=models.CASCADE,
        related_name='promotion_history',
        help_text="Personnel member"
    )

    from_rank = models.ForeignKey(
        Rank,
        on_delete=models.PROTECT,
        related_name='promoted_from',
        help_text="Previous rank"
    )

    to_rank = models.ForeignKey(
        Rank,
        on_delete=models.PROTECT,
        related_name='promoted_to',
        help_text="New rank"
    )

    promotion_date = models.DateField(
        help_text="Date of promotion"
    )

    promotion_type = models.CharField(
        max_length=20,
        choices=[
            ('regular', 'Regular Promotion'),
            ('accelerated', 'Accelerated Promotion'),
            ('acting', 'Acting Promotion'),
            ('temporary', 'Temporary Promotion'),
            ('substantive', 'Substantive Promotion'),
        ],
        default='regular',
        help_text="Type of promotion"
    )

    authority = models.CharField(
        max_length=200,
        blank=True,
        help_text="Promoting authority"
    )

    remarks = models.TextField(
        blank=True,
        help_text="Remarks about the promotion"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'personnel_promotion_history'
        ordering = ['-promotion_date']

    def __str__(self):
        return f"{self.personnel.user.get_full_name()}: {self.from_rank.name} → {self.to_rank.name}"


class Award(models.Model):
    """
    Military awards, decorations, and honors.
    """
    AWARD_TYPES = [
        ('medal', 'Medal'),
        ('decoration', 'Decoration'),
        ('commendation', 'Commendation'),
        ('ribbon', 'Ribbon'),
        ('badge', 'Badge'),
        ('certificate', 'Certificate'),
    ]

    name = models.CharField(
        max_length=200,
        help_text="Award name"
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Award code"
    )

    award_type = models.CharField(
        max_length=20,
        choices=AWARD_TYPES,
        help_text="Type of award"
    )

    description = models.TextField(
        blank=True,
        help_text="Award description and criteria"
    )

    precedence = models.PositiveIntegerField(
        default=100,
        help_text="Order of precedence for display"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'personnel_award'
        ordering = ['precedence', 'name']

    def __str__(self):
        return f"{self.code} - {self.name}"


class PersonnelAward(models.Model):
    """
    Awards received by personnel.
    """
    personnel = models.ForeignKey(
        NavalPersonnel,
        on_delete=models.CASCADE,
        related_name='awards',
        help_text="Personnel member"
    )

    award = models.ForeignKey(
        Award,
        on_delete=models.PROTECT,
        related_name='recipients',
        help_text="Award received"
    )

    date_awarded = models.DateField(
        help_text="Date award was received"
    )

    awarding_authority = models.CharField(
        max_length=200,
        blank=True,
        help_text="Authority that awarded the honor"
    )

    citation = models.TextField(
        blank=True,
        help_text="Citation or reason for award"
    )

    certificate_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Certificate or order number"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'personnel_award_received'
        ordering = ['-date_awarded']
        unique_together = ['personnel', 'award', 'date_awarded']

    def __str__(self):
        return f"{self.personnel.user.get_full_name()} - {self.award.name}"


class ProfessionalMembership(models.Model):
    """
    Professional body memberships for personnel.
    """
    MEMBERSHIP_TYPES = [
        ('member', 'Member'),
        ('associate', 'Associate Member'),
        ('fellow', 'Fellow'),
        ('chartered', 'Chartered Member'),
        ('honorary', 'Honorary Member'),
    ]

    personnel = models.ForeignKey(
        NavalPersonnel,
        on_delete=models.CASCADE,
        related_name='professional_memberships',
        help_text="Personnel member"
    )

    organization_name = models.CharField(
        max_length=200,
        help_text="Professional organization name"
    )

    membership_type = models.CharField(
        max_length=20,
        choices=MEMBERSHIP_TYPES,
        help_text="Type of membership"
    )

    membership_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Membership number"
    )

    date_joined = models.DateField(
        help_text="Date joined organization"
    )

    date_expired = models.DateField(
        blank=True,
        null=True,
        help_text="Membership expiry date"
    )

    is_active = models.BooleanField(
        default=True,
        help_text="Whether membership is active"
    )

    achievements = models.TextField(
        blank=True,
        help_text="Achievements within the organization"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'personnel_professional_membership'
        ordering = ['-date_joined']

    def __str__(self):
        return f"{self.personnel.user.get_full_name()} - {self.organization_name}"

    @property
    def is_expired(self):
        """Check if membership is expired."""
        if not self.date_expired:
            return False
        return self.date_expired < date.today()
