<?php
//
// SourceForge: Breaking Down the Barriers to Open Source Development
// Copyright 1999-2000 (c) The SourceForge Crew
// http://sourceforge.net
//
// $Id: browser.inc.php,v 1.1.1.1 2003/08/26 14:07:50 mechtonia Exp $



function browser_get_agent () {
    global $BROWSER_AGENT;
    return $BROWSER_AGENT;
}

function browser_get_version() {
    global $BROWSER_VER;
    return $BROWSER_VER;
}

function browser_get_platform() {
    global $BROWSER_PLATFORM;
    return $BROWSER_PLATFORM;
}

function browser_is_mac() {
    return browser_get_platform() == 'Mac';
}

function browser_is_windows() {
    return browser_get_platform() == 'Win';
}

function browser_is_netscape() {
    return browser_get_agent() == 'MOZILLA';
}

function browser_is_ie() {
    return browser_get_agent() == 'IE';
}

/*
    Determine browser and version
*/


if (preg_match('/MSIE ([0-9]+\.[0-9]{1,2})/', $_SERVER['HTTP_USER_AGENT'], $log_version)) {
    $BROWSER_VER = $log_version[1];
    $BROWSER_AGENT = 'IE';
} elseif (preg_match('/Opera ([0-9]+\.[0-9]{1,2})/', $_SERVER['HTTP_USER_AGENT'], $log_version)) {
    $BROWSER_VER = $log_version[1];
    $BROWSER_AGENT = 'OPERA';
} elseif (preg_match('/Mozilla\/([0-9]+\.[0-9]{1,2})/', $_SERVER['HTTP_USER_AGENT'], $log_version)) {
    $BROWSER_VER = $log_version[1];
    $BROWSER_AGENT = 'MOZILLA';
} else {
    $BROWSER_VER = 0;
    $BROWSER_AGENT = 'OTHER';
}

/*
    Determine platform
*/
if (preg_match('/MSIE ([0-9]+\.[0-9]{1,2})/', $_SERVER['HTTP_USER_AGENT'], $log_version)) {
} else if (strstr($_SERVER['HTTP_USER_AGENT'],'Mac')) {
    $BROWSER_AGENT = 'IE';
} else if (strstr($_SERVER['HTTP_USER_AGENT'],'Linux')) {
    $BROWSER_VER = $log_version[1];
} else if (strstr($_SERVER['HTTP_USER_AGENT'],'Unix')) {
} elseif (preg_match('/Mozilla\/([0-9]+\.[0-9]{1,2})/', $_SERVER['HTTP_USER_AGENT'], $log_version)) {
    $BROWSER_VER = $log_version[1];
    $BROWSER_AGENT = 'MOZILLA';
} else {
    $BROWSER_VER = 0;
    $BROWSER_AGENT = 'OTHER';
}

/*
    Determine platform
*/

if (strstr($_SERVER['HTTP_USER_AGENT'],'Win')) {
    $BROWSER_PLATFORM='Win';
} else if (strstr($_SERVER['HTTP_USER_AGENT'],'Mac')) {
    $BROWSER_PLATFORM='Mac';
} else if (strstr($_SERVER['HTTP_USER_AGENT'],'Linux')) {
    $BROWSER_PLATFORM='Linux';
} else if (strstr($_SERVER['HTTP_USER_AGENT'],'Unix')) {
    $BROWSER_PLATFORM='Unix';
} else {
    $BROWSER_PLATFORM='Other';
}
