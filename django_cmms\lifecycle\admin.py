from django.contrib import admin
from .models import (
    LifecycleStage, EquipmentLifecycle, MaintenanceHistory,
    ComponentReplacement, DisposalPlan
)


@admin.register(LifecycleStage)
class LifecycleStageAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'percentage_of_life', 'requires_action', 'is_active']
    list_filter = ['requires_action', 'is_active']
    search_fields = ['name', 'code']
    ordering = ['percentage_of_life']


@admin.register(EquipmentLifecycle)
class EquipmentLifecycleAdmin(admin.ModelAdmin):
    list_display = ['equipment', 'date_of_installation', 'expected_life_years', 'current_lifecycle_stage', 'operational_status']
    list_filter = ['current_lifecycle_stage', 'operational_status', 'date_of_installation']
    search_fields = ['equipment__name']
    readonly_fields = ['age_in_years', 'lifecycle_percentage', 'is_nearing_end_of_life', 'warranty_status']


@admin.register(MaintenanceHistory)
class MaintenanceHistoryAdmin(admin.ModelAdmin):
    list_display = ['equipment_lifecycle', 'maintenance_type', 'date_performed', 'performed_by', 'duration_hours']
    list_filter = ['maintenance_type', 'date_performed']
    search_fields = ['equipment_lifecycle__equipment__name']


@admin.register(ComponentReplacement)
class ComponentReplacementAdmin(admin.ModelAdmin):
    list_display = ['equipment_lifecycle', 'component_name', 'replacement_date', 'new_part_number', 'replaced_by']
    list_filter = ['replacement_date', 'supplier']
    search_fields = ['component_name', 'new_part_number']
    readonly_fields = ['is_under_warranty']


@admin.register(DisposalPlan)
class DisposalPlanAdmin(admin.ModelAdmin):
    list_display = ['equipment_lifecycle', 'planned_disposal_date', 'disposal_method', 'status', 'planned_by']
    list_filter = ['disposal_method', 'status', 'planned_disposal_date']
    search_fields = ['equipment_lifecycle__equipment__name']
    readonly_fields = ['net_disposal_value', 'is_overdue']
