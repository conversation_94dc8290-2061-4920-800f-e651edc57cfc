from django.contrib import admin
from .models import (
    FormType, NavalForm, FormApproval, FormField,
    FormSubmission, FormTemplate, FormWorkflow
)


@admin.register(FormType)
class FormTypeAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'category', 'requires_approval', 'approval_levels', 'is_active']
    list_filter = ['category', 'requires_approval', 'is_active']
    search_fields = ['name', 'code']


@admin.register(NavalForm)
class NavalFormAdmin(admin.ModelAdmin):
    list_display = ['form_id', 'title', 'form_type', 'status', 'priority', 'issued_by', 'date_issued']
    list_filter = ['form_type', 'status', 'priority', 'date_issued']
    search_fields = ['form_id', 'title']
    readonly_fields = ['is_overdue', 'days_overdue']


@admin.register(FormApproval)
class FormApprovalAdmin(admin.ModelAdmin):
    list_display = ['form', 'approver', 'approval_level', 'action', 'approval_date']
    list_filter = ['action', 'approval_level', 'approval_date']
    search_fields = ['form__form_id', 'approver__username']


@admin.register(FormField)
class FormFieldAdmin(admin.ModelAdmin):
    list_display = ['form_type', 'field_name', 'field_label', 'field_type', 'is_required', 'field_order']
    list_filter = ['form_type', 'field_type', 'is_required']
    search_fields = ['field_name', 'field_label']
    ordering = ['form_type', 'field_order']


@admin.register(FormSubmission)
class FormSubmissionAdmin(admin.ModelAdmin):
    list_display = ['form', 'submitted_by', 'submission_date']
    list_filter = ['submission_date']
    search_fields = ['form__form_id', 'submitted_by__username']


@admin.register(FormTemplate)
class FormTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'form_type', 'version', 'is_default', 'is_active', 'created_by']
    list_filter = ['form_type', 'is_default', 'is_active']
    search_fields = ['name']


@admin.register(FormWorkflow)
class FormWorkflowAdmin(admin.ModelAdmin):
    list_display = ['form_type', 'name', 'is_default', 'is_active']
    list_filter = ['form_type', 'is_default', 'is_active']
    search_fields = ['name']
