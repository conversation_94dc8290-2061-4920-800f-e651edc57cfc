<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CMMS - Computerized Maintenance Management System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard:home' %}">
                <i class="fas fa-tools"></i> CMMS
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'dashboard:home' %}">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-clipboard-list"></i> Work Orders
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'work_orders:list' %}">All Work Orders</a></li>
                                <li><a class="dropdown-item" href="{% url 'work_orders:my_work_orders' %}">My Work Orders</a></li>
                                <li><a class="dropdown-item" href="{% url 'work_orders:create' %}">Create Work Order</a></li>
                                {% if user.is_supervisor_or_higher %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'work_orders:pending_approval' %}">Pending Approval</a></li>
                                {% endif %}
                            </ul>
                        </li>
                        
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cogs"></i> Equipment
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'equipment:list' %}">Equipment List</a></li>
                                <li><a class="dropdown-item" href="{% url 'equipment:tree' %}">Equipment Tree</a></li>
                                {% if user.is_supervisor_or_higher %}
                                    <li><a class="dropdown-item" href="{% url 'equipment:create' %}">Add Equipment</a></li>
                                {% endif %}
                            </ul>
                        </li>
                        
                        {% if user.is_mechanic %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-wrench"></i> Maintenance
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'maintenance:schedule' %}">Maintenance Schedule</a></li>
                                    <li><a class="dropdown-item" href="{% url 'maintenance:trouble_calls' %}">Trouble Calls</a></li>
                                    <li><a class="dropdown-item" href="{% url 'maintenance:create_trouble_call' %}">Report Issue</a></li>
                                </ul>
                            </li>
                        {% endif %}
                        
                        {% if user.is_supervisor_or_higher %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-chart-bar"></i> Reports
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'reports:dashboard' %}">Analytics Dashboard</a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:work_order_report' %}">Work Order Reports</a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:equipment_report' %}">Equipment Reports</a></li>
                                    <li><a class="dropdown-item" href="{% url 'reports:cost_analysis' %}">Cost Analysis</a></li>
                                </ul>
                            </li>
                        {% endif %}
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <!-- Notifications -->
                        <li class="nav-item dropdown">
                            <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-bell"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    3
                                </span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><h6 class="dropdown-header">Notifications</h6></li>
                                <li><a class="dropdown-item" href="#">Work Order #2024-001 overdue</a></li>
                                <li><a class="dropdown-item" href="#">Equipment PM due tomorrow</a></li>
                                <li><a class="dropdown-item" href="#">New work order assigned</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'notifications:all' %}">View All</a></li>
                            </ul>
                        </li>
                        
                        <!-- User Menu -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ user.get_full_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                    <i class="fas fa-user-circle"></i> Profile
                                </a></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:password_change' %}">
                                    <i class="fas fa-key"></i> Change Password
                                </a></li>
                                {% if user.is_supervisor_or_higher %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'accounts:user_list' %}">
                                        <i class="fas fa-users"></i> Manage Users
                                    </a></li>
                                    <li><a class="dropdown-item" href="/admin/">
                                        <i class="fas fa-cog"></i> Admin Panel
                                    </a></li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:login' %}">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="container-fluid mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 CMMS - Computerized Maintenance Management System</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">Version 2.0 - Built with Django</p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
