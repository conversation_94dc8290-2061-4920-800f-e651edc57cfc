"""
Quality Control and Testing models for Naval CMMS application.
Based on analysis of NAVT ATTRIBUTES ENTITIES document - test procedures for fuel, lubricants, etc.
"""
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.conf import settings
from datetime import date, timedelta
from decimal import Decimal


class TestProcedure(models.Model):
    """
    Test procedures for different types of products (fuel, lubricants, etc.).
    """
    TEST_TYPES = [
        ('fuel_quality', 'Fuel Quality Test'),
        ('lubricant_analysis', 'Lubricant Analysis'),
        ('water_quality', 'Water Quality Test'),
        ('material_strength', 'Material Strength Test'),
        ('electrical_safety', 'Electrical Safety Test'),
        ('pressure_test', 'Pressure Test'),
        ('vibration_test', 'Vibration Test'),
        ('temperature_test', 'Temperature Test'),
        ('chemical_analysis', 'Chemical Analysis'),
        ('contamination_check', 'Contamination Check'),
    ]
    
    FREQUENCY_TYPES = [
        ('on_receipt', 'On Receipt'),
        ('before_use', 'Before Use'),
        ('periodic', 'Periodic'),
        ('random', 'Random Sampling'),
        ('on_demand', 'On Demand'),
        ('after_incident', 'After Incident'),
    ]
    
    name = models.CharField(
        max_length=200,
        help_text="Test procedure name"
    )
    
    code = models.CharField(
        max_length=50,
        unique=True,
        help_text="Test procedure code"
    )
    
    test_type = models.CharField(
        max_length=30,
        choices=TEST_TYPES,
        help_text="Type of test"
    )
    
    description = models.TextField(
        help_text="Detailed test procedure description"
    )
    
    # Applicable Products
    applicable_products = models.ManyToManyField(
        'inventory.Product',
        related_name='test_procedures',
        help_text="Products this test applies to"
    )
    
    applicable_categories = models.ManyToManyField(
        'inventory.ProductCategory',
        blank=True,
        related_name='test_procedures',
        help_text="Product categories this test applies to"
    )
    
    # Test Requirements
    frequency = models.CharField(
        max_length=20,
        choices=FREQUENCY_TYPES,
        help_text="Test frequency"
    )
    
    frequency_days = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Test frequency in days (for periodic tests)"
    )
    
    sample_size = models.CharField(
        max_length=100,
        help_text="Required sample size"
    )
    
    sampling_method = models.TextField(
        help_text="Sampling method and instructions"
    )
    
    # Test Standards and Specifications
    test_standard = models.CharField(
        max_length=100,
        blank=True,
        help_text="Applicable test standard (e.g., ASTM, ISO)"
    )
    
    acceptance_criteria = models.TextField(
        help_text="Acceptance criteria and limits"
    )
    
    rejection_criteria = models.TextField(
        help_text="Rejection criteria and limits"
    )
    
    # Equipment and Resources
    required_equipment = models.TextField(
        help_text="Required test equipment and instruments"
    )
    
    required_reagents = models.TextField(
        blank=True,
        help_text="Required chemicals and reagents"
    )
    
    estimated_duration_minutes = models.PositiveIntegerField(
        help_text="Estimated test duration in minutes"
    )
    
    # Personnel Requirements
    required_qualifications = models.TextField(
        blank=True,
        help_text="Required personnel qualifications"
    )
    
    safety_requirements = models.TextField(
        blank=True,
        help_text="Safety requirements and precautions"
    )
    
    # Documentation
    test_form_template = models.FileField(
        upload_to='quality/test_forms/',
        blank=True,
        null=True,
        help_text="Test form template"
    )
    
    procedure_document = models.FileField(
        upload_to='quality/procedures/',
        blank=True,
        null=True,
        help_text="Detailed procedure document"
    )
    
    is_mandatory = models.BooleanField(
        default=True,
        help_text="Whether this test is mandatory"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'quality_test_procedure'
        ordering = ['name']
        
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def get_absolute_url(self):
        return reverse('quality:procedure_detail', kwargs={'pk': self.pk})


class TestParameter(models.Model):
    """
    Individual test parameters within a test procedure.
    """
    PARAMETER_TYPES = [
        ('numeric', 'Numeric Value'),
        ('range', 'Range'),
        ('pass_fail', 'Pass/Fail'),
        ('text', 'Text Result'),
        ('selection', 'Selection from Options'),
    ]
    
    test_procedure = models.ForeignKey(
        TestProcedure,
        on_delete=models.CASCADE,
        related_name='parameters',
        help_text="Test procedure"
    )
    
    name = models.CharField(
        max_length=200,
        help_text="Parameter name"
    )
    
    code = models.CharField(
        max_length=50,
        help_text="Parameter code"
    )
    
    parameter_type = models.CharField(
        max_length=20,
        choices=PARAMETER_TYPES,
        help_text="Type of parameter"
    )
    
    unit_of_measure = models.CharField(
        max_length=50,
        blank=True,
        help_text="Unit of measurement"
    )
    
    # Acceptance Limits
    min_value = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        blank=True,
        null=True,
        help_text="Minimum acceptable value"
    )
    
    max_value = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        blank=True,
        null=True,
        help_text="Maximum acceptable value"
    )
    
    target_value = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        blank=True,
        null=True,
        help_text="Target value"
    )
    
    # For selection type parameters
    valid_options = models.TextField(
        blank=True,
        help_text="Valid options (comma-separated)"
    )
    
    # Test Method
    test_method = models.TextField(
        help_text="Specific test method for this parameter"
    )
    
    is_critical = models.BooleanField(
        default=False,
        help_text="Whether this is a critical parameter"
    )
    
    sequence_order = models.PositiveIntegerField(
        default=1,
        help_text="Order in which parameter should be tested"
    )
    
    class Meta:
        db_table = 'quality_test_parameter'
        ordering = ['test_procedure', 'sequence_order']
        unique_together = ['test_procedure', 'code']
        
    def __str__(self):
        return f"{self.test_procedure.code} - {self.name}"


class TestRequest(models.Model):
    """
    Requests for testing products before use.
    """
    STATUS_CHOICES = [
        ('requested', 'Requested'),
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    PRIORITY_CHOICES = [
        (1, 'Emergency'),
        (2, 'Urgent'),
        (3, 'Normal'),
        (4, 'Low'),
    ]
    
    request_id = models.CharField(
        max_length=20,
        unique=True,
        help_text="Test request ID"
    )
    
    product = models.ForeignKey(
        'inventory.Product',
        on_delete=models.PROTECT,
        related_name='test_requests',
        help_text="Product to be tested"
    )
    
    stock_item = models.ForeignKey(
        'inventory.StockItem',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='test_requests',
        help_text="Specific stock item to be tested"
    )
    
    test_procedure = models.ForeignKey(
        TestProcedure,
        on_delete=models.PROTECT,
        related_name='test_requests',
        help_text="Test procedure to be performed"
    )
    
    # Request Information
    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='test_requests',
        help_text="Person requesting the test"
    )
    
    requesting_unit = models.ForeignKey(
        'fleet.ShipUnit',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='test_requests',
        help_text="Requesting unit"
    )
    
    date_requested = models.DateField(
        default=timezone.now,
        help_text="Date test was requested"
    )
    
    date_required = models.DateField(
        help_text="Date test results are required"
    )
    
    priority = models.IntegerField(
        choices=PRIORITY_CHOICES,
        default=3,
        help_text="Test priority"
    )
    
    # Sample Information
    batch_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Batch or lot number of sample"
    )
    
    sample_quantity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Quantity of sample"
    )
    
    sample_unit = models.CharField(
        max_length=20,
        help_text="Unit of sample quantity"
    )
    
    sampling_date = models.DateField(
        help_text="Date sample was taken"
    )
    
    sampling_location = models.CharField(
        max_length=200,
        help_text="Location where sample was taken"
    )
    
    sampled_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='sampled_tests',
        help_text="Person who took the sample"
    )
    
    # Test Execution
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='requested',
        help_text="Test status"
    )
    
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='assigned_tests',
        help_text="Person assigned to perform test"
    )
    
    scheduled_date = models.DateField(
        blank=True,
        null=True,
        help_text="Scheduled test date"
    )
    
    actual_test_date = models.DateField(
        blank=True,
        null=True,
        help_text="Actual test date"
    )
    
    # Purpose and Context
    test_purpose = models.TextField(
        help_text="Purpose of the test"
    )
    
    related_work_order = models.ForeignKey(
        'work_orders.WorkOrder',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='test_requests',
        help_text="Related work order"
    )
    
    special_instructions = models.TextField(
        blank=True,
        help_text="Special instructions for testing"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'quality_test_request'
        ordering = ['-date_requested']
        
    def __str__(self):
        return f"{self.request_id} - {self.product.name}"
    
    def get_absolute_url(self):
        return reverse('quality:test_request_detail', kwargs={'pk': self.pk})
    
    @property
    def is_overdue(self):
        """Check if test is overdue."""
        if self.status in ['completed', 'cancelled']:
            return False
        return self.date_required < date.today()
    
    @property
    def days_overdue(self):
        """Calculate days overdue."""
        if not self.is_overdue:
            return 0
        return (date.today() - self.date_required).days
    
    def generate_request_id(self):
        """Generate unique test request ID."""
        from datetime import datetime
        year = datetime.now().year
        last_request = TestRequest.objects.filter(
            request_id__startswith=f"TR{year}"
        ).order_by('-request_id').first()
        
        if last_request:
            last_num = int(last_request.request_id.split('-')[-1])
            new_num = last_num + 1
        else:
            new_num = 1
        
        return f"TR{year}-{new_num:04d}"


class TestResult(models.Model):
    """
    Test results and findings.
    """
    OVERALL_RESULTS = [
        ('pass', 'Pass'),
        ('fail', 'Fail'),
        ('conditional_pass', 'Conditional Pass'),
        ('retest_required', 'Retest Required'),
        ('inconclusive', 'Inconclusive'),
    ]
    
    test_request = models.OneToOneField(
        TestRequest,
        on_delete=models.CASCADE,
        related_name='result',
        help_text="Test request"
    )
    
    # Test Execution Details
    tested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='performed_tests',
        help_text="Person who performed the test"
    )
    
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='reviewed_tests',
        help_text="Person who reviewed the results"
    )
    
    test_start_time = models.DateTimeField(
        help_text="Test start time"
    )
    
    test_end_time = models.DateTimeField(
        help_text="Test end time"
    )
    
    # Environmental Conditions
    temperature = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Temperature during test (°C)"
    )
    
    humidity = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Humidity during test (%)"
    )
    
    atmospheric_pressure = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Atmospheric pressure (kPa)"
    )
    
    # Overall Result
    overall_result = models.CharField(
        max_length=20,
        choices=OVERALL_RESULTS,
        help_text="Overall test result"
    )
    
    # Equipment Used
    test_equipment_used = models.TextField(
        help_text="Test equipment and instruments used"
    )
    
    calibration_status = models.TextField(
        blank=True,
        help_text="Calibration status of test equipment"
    )
    
    # Observations and Comments
    observations = models.TextField(
        blank=True,
        help_text="Test observations and notes"
    )
    
    deviations = models.TextField(
        blank=True,
        help_text="Any deviations from standard procedure"
    )
    
    recommendations = models.TextField(
        blank=True,
        help_text="Recommendations based on results"
    )
    
    # Quality Control
    qc_checked_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='qc_checked_tests',
        help_text="QC checker"
    )
    
    qc_check_date = models.DateField(
        blank=True,
        null=True,
        help_text="QC check date"
    )
    
    qc_comments = models.TextField(
        blank=True,
        help_text="QC comments"
    )
    
    # Documentation
    test_report = models.FileField(
        upload_to='quality/test_reports/',
        blank=True,
        null=True,
        help_text="Detailed test report"
    )
    
    certificate_issued = models.BooleanField(
        default=False,
        help_text="Whether test certificate was issued"
    )
    
    certificate_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Test certificate number"
    )
    
    certificate_file = models.FileField(
        upload_to='quality/certificates/',
        blank=True,
        null=True,
        help_text="Test certificate file"
    )
    
    # Validity
    result_valid_until = models.DateField(
        blank=True,
        null=True,
        help_text="Date until which result is valid"
    )
    
    retest_required_by = models.DateField(
        blank=True,
        null=True,
        help_text="Date by which retest is required"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'quality_test_result'
        
    def __str__(self):
        return f"{self.test_request.request_id} - {self.overall_result}"
    
    def get_absolute_url(self):
        return reverse('quality:test_result_detail', kwargs={'pk': self.pk})
    
    @property
    def test_duration(self):
        """Calculate test duration."""
        return self.test_end_time - self.test_start_time
    
    @property
    def is_result_expired(self):
        """Check if test result is expired."""
        if not self.result_valid_until:
            return False
        return date.today() > self.result_valid_until
    
    @property
    def days_until_expiry(self):
        """Calculate days until result expiry."""
        if not self.result_valid_until:
            return None
        return (self.result_valid_until - date.today()).days


class TestParameterResult(models.Model):
    """
    Individual parameter results within a test.
    """
    test_result = models.ForeignKey(
        TestResult,
        on_delete=models.CASCADE,
        related_name='parameter_results',
        help_text="Test result"
    )
    
    test_parameter = models.ForeignKey(
        TestParameter,
        on_delete=models.PROTECT,
        related_name='results',
        help_text="Test parameter"
    )
    
    # Result Values
    numeric_value = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        blank=True,
        null=True,
        help_text="Numeric result value"
    )
    
    text_value = models.TextField(
        blank=True,
        help_text="Text result value"
    )
    
    pass_fail_result = models.BooleanField(
        blank=True,
        null=True,
        help_text="Pass/Fail result"
    )
    
    # Analysis
    within_limits = models.BooleanField(
        blank=True,
        null=True,
        help_text="Whether result is within acceptable limits"
    )
    
    deviation_from_target = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        blank=True,
        null=True,
        help_text="Deviation from target value"
    )
    
    # Additional Information
    method_used = models.CharField(
        max_length=200,
        blank=True,
        help_text="Specific method used for this parameter"
    )
    
    equipment_used = models.CharField(
        max_length=200,
        blank=True,
        help_text="Equipment used for this parameter"
    )
    
    remarks = models.TextField(
        blank=True,
        help_text="Remarks about this parameter result"
    )
    
    class Meta:
        db_table = 'quality_test_parameter_result'
        unique_together = ['test_result', 'test_parameter']
        ordering = ['test_parameter__sequence_order']
        
    def __str__(self):
        return f"{self.test_parameter.name}: {self.get_display_value()}"
    
    def get_display_value(self):
        """Get display value based on parameter type."""
        if self.numeric_value is not None:
            unit = self.test_parameter.unit_of_measure
            return f"{self.numeric_value} {unit}".strip()
        elif self.pass_fail_result is not None:
            return "Pass" if self.pass_fail_result else "Fail"
        elif self.text_value:
            return self.text_value
        return "No result"


class QualityAlert(models.Model):
    """
    Quality alerts for failed tests or concerning trends.
    """
    ALERT_TYPES = [
        ('test_failure', 'Test Failure'),
        ('trend_concern', 'Concerning Trend'),
        ('equipment_issue', 'Test Equipment Issue'),
        ('procedure_deviation', 'Procedure Deviation'),
        ('expiry_warning', 'Result Expiry Warning'),
        ('retest_due', 'Retest Due'),
    ]
    
    SEVERITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    alert_type = models.CharField(
        max_length=20,
        choices=ALERT_TYPES,
        help_text="Type of alert"
    )
    
    severity = models.CharField(
        max_length=10,
        choices=SEVERITY_LEVELS,
        help_text="Alert severity"
    )
    
    title = models.CharField(
        max_length=200,
        help_text="Alert title"
    )
    
    description = models.TextField(
        help_text="Alert description"
    )
    
    # Related Objects
    test_result = models.ForeignKey(
        TestResult,
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='quality_alerts',
        help_text="Related test result"
    )
    
    product = models.ForeignKey(
        'inventory.Product',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='quality_alerts',
        help_text="Related product"
    )
    
    # Alert Management
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_quality_alerts',
        help_text="Person who created the alert"
    )
    
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='assigned_quality_alerts',
        help_text="Person assigned to handle the alert"
    )
    
    is_resolved = models.BooleanField(
        default=False,
        help_text="Whether alert is resolved"
    )
    
    resolved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='resolved_quality_alerts',
        help_text="Person who resolved the alert"
    )
    
    resolved_date = models.DateTimeField(
        blank=True,
        null=True,
        help_text="Date alert was resolved"
    )
    
    resolution_notes = models.TextField(
        blank=True,
        help_text="Resolution notes"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'quality_alert'
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.alert_type} - {self.title}"
