"""
Maintenance models for CMMS application.
"""
from django.db import models
from django.urls import reverse
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.conf import settings
from decimal import Decimal


class MaintenanceType(models.Model):
    """
    Types of maintenance (Preventive, Corrective, Predictive, etc.).
    """
    name = models.Char<PERSON>ield(
        max_length=50,
        unique=True,
        help_text="Maintenance type name"
    )
    
    code = models.CharField(
        max_length=10,
        unique=True,
        help_text="Short code for the maintenance type"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Description of this maintenance type"
    )
    
    color = models.CharField(
        max_length=7,
        default='#007bff',
        help_text="Color for visual identification"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'maintenance_type'
        ordering = ['name']
        
    def __str__(self):
        return f"{self.code} - {self.name}"


class MaintenanceTask(models.Model):
    """
    Scheduled maintenance tasks.
    """
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('overdue', 'Overdue'),
    ]
    
    FREQUENCY_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('semi_annual', 'Semi-Annual'),
        ('annual', 'Annual'),
        ('custom', 'Custom'),
    ]
    
    # Basic Information
    task_id = models.CharField(
        max_length=20,
        unique=True,
        help_text="Maintenance task ID"
    )
    
    title = models.CharField(
        max_length=200,
        help_text="Task title"
    )
    
    description = models.TextField(
        help_text="Detailed task description"
    )
    
    maintenance_type = models.ForeignKey(
        MaintenanceType,
        on_delete=models.PROTECT,
        related_name='tasks',
        help_text="Type of maintenance"
    )
    
    # Equipment and Location
    equipment = models.ForeignKey(
        'equipment.Equipment',
        on_delete=models.CASCADE,
        related_name='maintenance_tasks',
        help_text="Equipment this task is for"
    )
    
    # Scheduling
    frequency = models.CharField(
        max_length=20,
        choices=FREQUENCY_CHOICES,
        help_text="Maintenance frequency"
    )
    
    frequency_days = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Custom frequency in days"
    )
    
    scheduled_date = models.DateTimeField(
        help_text="Scheduled date and time"
    )
    
    estimated_duration = models.DurationField(
        blank=True,
        null=True,
        help_text="Estimated duration to complete"
    )
    
    # Assignment
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='assigned_maintenance_tasks',
        help_text="Person assigned to perform the task"
    )
    
    # Status and Completion
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='scheduled',
        help_text="Current status"
    )
    
    started_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When task was started"
    )
    
    completed_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When task was completed"
    )
    
    actual_duration = models.DurationField(
        blank=True,
        null=True,
        help_text="Actual time taken to complete"
    )
    
    # Work Details
    work_performed = models.TextField(
        blank=True,
        help_text="Description of work performed"
    )
    
    parts_used = models.TextField(
        blank=True,
        help_text="Parts and materials used"
    )
    
    notes = models.TextField(
        blank=True,
        help_text="Additional notes"
    )
    
    # Next Occurrence
    next_due_date = models.DateTimeField(
        blank=True,
        null=True,
        help_text="Next scheduled occurrence"
    )
    
    is_recurring = models.BooleanField(
        default=True,
        help_text="Whether this task recurs"
    )
    
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this task is active"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'maintenance_task'
        ordering = ['scheduled_date']
        
    def __str__(self):
        return f"{self.task_id} - {self.title}"
    
    def get_absolute_url(self):
        return reverse('maintenance:task_detail', kwargs={'pk': self.pk})
    
    @property
    def is_overdue(self):
        """Check if task is overdue."""
        if self.status in ['completed', 'cancelled']:
            return False
        return self.scheduled_date < timezone.now()
    
    @property
    def days_overdue(self):
        """Calculate days overdue."""
        if not self.is_overdue:
            return 0
        return (timezone.now() - self.scheduled_date).days
    
    def calculate_next_due_date(self):
        """Calculate next due date based on frequency."""
        if not self.is_recurring:
            return None
        
        from datetime import timedelta
        
        if self.frequency == 'daily':
            return self.scheduled_date + timedelta(days=1)
        elif self.frequency == 'weekly':
            return self.scheduled_date + timedelta(weeks=1)
        elif self.frequency == 'monthly':
            return self.scheduled_date + timedelta(days=30)
        elif self.frequency == 'quarterly':
            return self.scheduled_date + timedelta(days=90)
        elif self.frequency == 'semi_annual':
            return self.scheduled_date + timedelta(days=180)
        elif self.frequency == 'annual':
            return self.scheduled_date + timedelta(days=365)
        elif self.frequency == 'custom' and self.frequency_days:
            return self.scheduled_date + timedelta(days=self.frequency_days)
        
        return None


class TroubleCall(models.Model):
    """
    Emergency maintenance requests (Hot Jobs).
    """
    STATUS_CHOICES = [
        ('reported', 'Reported'),
        ('acknowledged', 'Acknowledged'),
        ('in_progress', 'In Progress'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    ]
    
    SEVERITY_CHOICES = [
        (1, 'Critical - Production Down'),
        (2, 'High - Major Impact'),
        (3, 'Medium - Minor Impact'),
        (4, 'Low - No Impact'),
    ]
    
    # Basic Information
    call_id = models.CharField(
        max_length=20,
        unique=True,
        help_text="Trouble call ID"
    )
    
    title = models.CharField(
        max_length=200,
        help_text="Brief description of the issue"
    )
    
    description = models.TextField(
        help_text="Detailed description of the problem"
    )
    
    severity = models.IntegerField(
        choices=SEVERITY_CHOICES,
        default=3,
        help_text="Severity level of the issue"
    )
    
    # Equipment and Location
    equipment = models.ForeignKey(
        'equipment.Equipment',
        on_delete=models.PROTECT,
        related_name='trouble_calls',
        blank=True,
        null=True,
        help_text="Affected equipment"
    )
    
    location = models.ForeignKey(
        'equipment.Location',
        on_delete=models.PROTECT,
        related_name='trouble_calls',
        blank=True,
        null=True,
        help_text="Location of the issue"
    )
    
    # People
    reported_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='reported_trouble_calls',
        help_text="Person who reported the issue"
    )
    
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='assigned_trouble_calls',
        help_text="Person assigned to resolve the issue"
    )
    
    # Status and Timing
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='reported',
        help_text="Current status"
    )
    
    reported_at = models.DateTimeField(auto_now_add=True)
    acknowledged_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the call was acknowledged"
    )
    
    started_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When work started"
    )
    
    resolved_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the issue was resolved"
    )
    
    closed_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the call was closed"
    )
    
    # Work Details
    work_performed = models.TextField(
        blank=True,
        help_text="Description of work performed"
    )
    
    parts_used = models.TextField(
        blank=True,
        help_text="Parts and materials used"
    )
    
    root_cause = models.TextField(
        blank=True,
        help_text="Root cause analysis"
    )
    
    resolution_notes = models.TextField(
        blank=True,
        help_text="Resolution notes"
    )
    
    # Time Tracking
    response_time = models.DurationField(
        blank=True,
        null=True,
        help_text="Time from report to acknowledgment"
    )
    
    resolution_time = models.DurationField(
        blank=True,
        null=True,
        help_text="Time from report to resolution"
    )
    
    downtime_duration = models.DurationField(
        blank=True,
        null=True,
        help_text="Equipment downtime duration"
    )
    
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'maintenance_trouble_call'
        ordering = ['-reported_at']
        
    def __str__(self):
        return f"{self.call_id} - {self.title}"
    
    def get_absolute_url(self):
        return reverse('maintenance:trouble_call_detail', kwargs={'pk': self.pk})
    
    @property
    def is_critical(self):
        """Check if this is a critical issue."""
        return self.severity == 1
    
    @property
    def is_overdue_response(self):
        """Check if response is overdue based on severity."""
        if self.status != 'reported':
            return False
        
        response_time_limits = {
            1: 1,   # Critical: 1 hour
            2: 4,   # High: 4 hours
            3: 24,  # Medium: 24 hours
            4: 72,  # Low: 72 hours
        }
        
        limit_hours = response_time_limits.get(self.severity, 24)
        from datetime import timedelta
        deadline = self.reported_at + timedelta(hours=limit_hours)
        
        return timezone.now() > deadline
    
    def save(self, *args, **kwargs):
        """Override save to handle status transitions and time tracking."""
        if not self.call_id:
            self.call_id = self.generate_call_id()
        
        # Calculate response and resolution times
        if self.acknowledged_at and not self.response_time:
            self.response_time = self.acknowledged_at - self.reported_at
        
        if self.resolved_at and not self.resolution_time:
            self.resolution_time = self.resolved_at - self.reported_at
        
        super().save(*args, **kwargs)
    
    def generate_call_id(self):
        """Generate a unique trouble call ID."""
        from datetime import datetime
        year = datetime.now().year
        # Get the last call number for this year
        last_call = TroubleCall.objects.filter(
            call_id__startswith=f"TC{year}"
        ).order_by('-call_id').first()
        
        if last_call:
            last_num = int(last_call.call_id.split('-')[-1])
            new_num = last_num + 1
        else:
            new_num = 1
        
        return f"TC{year}-{new_num:04d}"
