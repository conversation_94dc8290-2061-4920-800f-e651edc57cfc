-- Database freecmms running on mysql.sourceforge.net

# phpMyAdmin MySQL-Dump
# version 2.5.2-dev
# http://www.phpmyadmin.net/ (download page)
#
# Host: mysql.sourceforge.net
# Generation Time: Aug 20, 2003 at 03:17 PM
# Server version: 3.23.54
# PHP Version: 4.1.2
# Database : `freecmms`
# --------------------------------------------------------

#
# Table structure for table `equipment`
#
# Creation: Aug 17, 2003 at 11:26 AM
# Last update: Aug 17, 2003 at 01:40 PM
#

CREATE TABLE `equipment` (
  `id` int(11) NOT NULL auto_increment,
  `parent_id` int(11) NOT NULL default '0',
  `description` varchar(20) NOT NULL default '',
  KEY `id` (`id`)
) ENGINE=InnoDB COMMENT='Equipment' AUTO_INCREMENT=201 ;

#
# Dumping data for table `equipment`
#

INSERT INTO `equipment` VALUES (1, 0, 'Poly 1');
INSERT INTO `equipment` VALUES (187, 1, 'Left Bagger');
INSERT INTO `equipment` VALUES (188, 1, 'Right Bagger');
INSERT INTO `equipment` VALUES (189, 1, 'Dump Station');
INSERT INTO `equipment` VALUES (190, 1, 'Product Shaker');
INSERT INTO `equipment` VALUES (191, 1, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (192, 1, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (193, 1, 'Pants Legs');
INSERT INTO `equipment` VALUES (194, 1, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (195, 1, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (197, 1, 'Left Scale');
INSERT INTO `equipment` VALUES (199, 1, 'Right Scale');
INSERT INTO `equipment` VALUES (200, 1, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (14, 1, 'Metal Detector');
INSERT INTO `equipment` VALUES (15, 1, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (16, 1, 'Seal Check');
INSERT INTO `equipment` VALUES (17, 1, 'Smart Belts');
INSERT INTO `equipment` VALUES (18, 1, 'Case Packer');
INSERT INTO `equipment` VALUES (19, 1, 'Box Former');
INSERT INTO `equipment` VALUES (20, 1, 'Case Conveyor');
INSERT INTO `equipment` VALUES (21, 1, 'Horseshoe');
INSERT INTO `equipment` VALUES (22, 1, 'Tape Machine');
INSERT INTO `equipment` VALUES (23, 1, 'Case Printer');
INSERT INTO `equipment` VALUES (24, 1, 'Other');
INSERT INTO `equipment` VALUES (2, 0, 'Poly 2');
INSERT INTO `equipment` VALUES (25, 2, 'Left Bagger');
INSERT INTO `equipment` VALUES (26, 2, 'Right Bagger');
INSERT INTO `equipment` VALUES (27, 2, 'Dump Satation');
INSERT INTO `equipment` VALUES (28, 2, 'Product Shaker');
INSERT INTO `equipment` VALUES (29, 2, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (30, 2, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (31, 2, 'Pants Legs');
INSERT INTO `equipment` VALUES (32, 2, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (33, 2, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (34, 2, 'Left Scale');
INSERT INTO `equipment` VALUES (35, 2, 'Right Scale');
INSERT INTO `equipment` VALUES (36, 2, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (37, 2, 'Metal Detector');
INSERT INTO `equipment` VALUES (38, 2, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (39, 2, 'Seal Check');
INSERT INTO `equipment` VALUES (40, 2, 'Smart Belts');
INSERT INTO `equipment` VALUES (41, 2, 'Case Packer');
INSERT INTO `equipment` VALUES (42, 2, 'Box Former');
INSERT INTO `equipment` VALUES (43, 2, 'Case Conveyor');
INSERT INTO `equipment` VALUES (44, 2, 'Horseshoe');
INSERT INTO `equipment` VALUES (45, 2, 'Tape Machine');
INSERT INTO `equipment` VALUES (46, 2, 'Case Printer');
INSERT INTO `equipment` VALUES (47, 2, 'Other');
INSERT INTO `equipment` VALUES (3, 0, 'Poly 3');
INSERT INTO `equipment` VALUES (48, 3, 'Left Bagger');
INSERT INTO `equipment` VALUES (49, 3, 'Right Bagger');
INSERT INTO `equipment` VALUES (50, 3, 'Dump Satation');
INSERT INTO `equipment` VALUES (51, 3, 'Product Shaker');
INSERT INTO `equipment` VALUES (52, 3, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (53, 3, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (54, 3, 'Pants Legs');
INSERT INTO `equipment` VALUES (55, 3, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (56, 3, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (57, 3, 'Left Scale');
INSERT INTO `equipment` VALUES (58, 3, 'Right Scale');
INSERT INTO `equipment` VALUES (59, 3, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (60, 3, 'Metal Detector');
INSERT INTO `equipment` VALUES (61, 3, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (62, 3, 'Seal Check');
INSERT INTO `equipment` VALUES (63, 3, 'Smart Belts');
INSERT INTO `equipment` VALUES (64, 3, 'Case Packer');
INSERT INTO `equipment` VALUES (65, 3, 'Box Former');
INSERT INTO `equipment` VALUES (66, 3, 'Case Conveyor');
INSERT INTO `equipment` VALUES (67, 3, 'Horseshoe');
INSERT INTO `equipment` VALUES (68, 3, 'Tape Machine');
INSERT INTO `equipment` VALUES (69, 3, 'Case Printer');
INSERT INTO `equipment` VALUES (70, 3, 'Other');
INSERT INTO `equipment` VALUES (4, 0, 'Poly 4');
INSERT INTO `equipment` VALUES (71, 4, 'Left Bagger');
INSERT INTO `equipment` VALUES (72, 4, 'Right Bagger');
INSERT INTO `equipment` VALUES (73, 4, 'Dump Satation');
INSERT INTO `equipment` VALUES (74, 4, 'Product Shaker');
INSERT INTO `equipment` VALUES (75, 4, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (76, 4, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (77, 4, 'Pants Legs');
INSERT INTO `equipment` VALUES (78, 4, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (79, 4, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (80, 4, 'Left Scale');
INSERT INTO `equipment` VALUES (81, 4, 'Right Scale');
INSERT INTO `equipment` VALUES (82, 4, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (83, 4, 'Metal Detector');
INSERT INTO `equipment` VALUES (84, 4, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (85, 4, 'Seal Check');
INSERT INTO `equipment` VALUES (86, 4, 'Smart Belts');
INSERT INTO `equipment` VALUES (87, 4, 'Case Packer');
INSERT INTO `equipment` VALUES (88, 4, 'Box Former');
INSERT INTO `equipment` VALUES (89, 4, 'Case Conveyor');
INSERT INTO `equipment` VALUES (90, 4, 'Horseshoe');
INSERT INTO `equipment` VALUES (91, 4, 'Tape Machine');
INSERT INTO `equipment` VALUES (92, 4, 'Case Printer');
INSERT INTO `equipment` VALUES (93, 4, 'Other');
INSERT INTO `equipment` VALUES (5, 0, 'Poly 5');
INSERT INTO `equipment` VALUES (94, 5, 'Left Bagger');
INSERT INTO `equipment` VALUES (95, 5, 'Right Bagger');
INSERT INTO `equipment` VALUES (96, 5, 'Dump Satation');
INSERT INTO `equipment` VALUES (97, 5, 'Product Shaker');
INSERT INTO `equipment` VALUES (98, 5, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (99, 5, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (100, 5, 'Pants Legs');
INSERT INTO `equipment` VALUES (101, 5, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (102, 5, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (103, 5, 'Left Scale');
INSERT INTO `equipment` VALUES (104, 5, 'Right Scale');
INSERT INTO `equipment` VALUES (105, 5, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (106, 5, 'Metal Detector');
INSERT INTO `equipment` VALUES (107, 5, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (108, 5, 'Seal Check');
INSERT INTO `equipment` VALUES (109, 5, 'Smart Belts');
INSERT INTO `equipment` VALUES (110, 5, 'Case Packer');
INSERT INTO `equipment` VALUES (111, 5, 'Box Former');
INSERT INTO `equipment` VALUES (112, 5, 'Case Conveyor');
INSERT INTO `equipment` VALUES (113, 5, 'Horseshoe');
INSERT INTO `equipment` VALUES (114, 5, 'Tape Machine');
INSERT INTO `equipment` VALUES (115, 5, 'Case Printer');
INSERT INTO `equipment` VALUES (116, 5, 'Other');
INSERT INTO `equipment` VALUES (6, 0, 'Poly 6');
INSERT INTO `equipment` VALUES (117, 6, 'Left Bagger');
INSERT INTO `equipment` VALUES (118, 6, 'Right Bagger');
INSERT INTO `equipment` VALUES (119, 6, 'Dump Satation');
INSERT INTO `equipment` VALUES (120, 6, 'Product Shaker');
INSERT INTO `equipment` VALUES (121, 6, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (122, 6, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (123, 6, 'Pants Legs');
INSERT INTO `equipment` VALUES (124, 6, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (125, 6, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (126, 6, 'Left Scale');
INSERT INTO `equipment` VALUES (127, 6, 'Right Scale');
INSERT INTO `equipment` VALUES (128, 6, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (129, 6, 'Metal Detector');
INSERT INTO `equipment` VALUES (130, 6, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (131, 6, 'Seal Check');
INSERT INTO `equipment` VALUES (132, 6, 'Smart Belts');
INSERT INTO `equipment` VALUES (133, 6, 'Case Packer');
INSERT INTO `equipment` VALUES (134, 6, 'Box Former');
INSERT INTO `equipment` VALUES (135, 6, 'Case Conveyor');
INSERT INTO `equipment` VALUES (136, 6, 'Horseshoe');
INSERT INTO `equipment` VALUES (137, 6, 'Tape Machine');
INSERT INTO `equipment` VALUES (138, 6, 'Case Printer');
INSERT INTO `equipment` VALUES (139, 6, 'Other');
INSERT INTO `equipment` VALUES (7, 0, 'Poly 7');
INSERT INTO `equipment` VALUES (140, 7, 'Left Bagger');
INSERT INTO `equipment` VALUES (141, 7, 'Right Bagger');
INSERT INTO `equipment` VALUES (142, 7, 'Dump Satation');
INSERT INTO `equipment` VALUES (143, 7, 'Product Shaker');
INSERT INTO `equipment` VALUES (144, 7, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (145, 7, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (146, 7, 'Pants Legs');
INSERT INTO `equipment` VALUES (147, 7, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (148, 7, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (149, 7, 'Left Scale');
INSERT INTO `equipment` VALUES (150, 7, 'Right Scale');
INSERT INTO `equipment` VALUES (151, 7, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (152, 7, 'Metal Detector');
INSERT INTO `equipment` VALUES (153, 7, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (154, 7, 'Seal Check');
INSERT INTO `equipment` VALUES (155, 7, 'Smart Belts');
INSERT INTO `equipment` VALUES (156, 7, 'Case Packer');
INSERT INTO `equipment` VALUES (157, 7, 'Box Former');
INSERT INTO `equipment` VALUES (158, 7, 'Case Conveyor');
INSERT INTO `equipment` VALUES (159, 7, 'Horseshoe');
INSERT INTO `equipment` VALUES (160, 7, 'Tape Machine');
INSERT INTO `equipment` VALUES (161, 7, 'Case Printer');
INSERT INTO `equipment` VALUES (162, 7, 'Other');
INSERT INTO `equipment` VALUES (8, 0, 'Poly 0');
INSERT INTO `equipment` VALUES (163, 8, 'Left Bagger');
INSERT INTO `equipment` VALUES (164, 8, 'Right Bagger');
INSERT INTO `equipment` VALUES (165, 8, 'Dump Satation');
INSERT INTO `equipment` VALUES (166, 8, 'Product Shaker');
INSERT INTO `equipment` VALUES (167, 8, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (168, 8, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (169, 8, 'Pants Legs');
INSERT INTO `equipment` VALUES (170, 8, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (171, 8, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (172, 8, 'Left Scale');
INSERT INTO `equipment` VALUES (173, 8, 'Right Scale');
INSERT INTO `equipment` VALUES (174, 8, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (175, 8, 'Metal Detector');
INSERT INTO `equipment` VALUES (176, 8, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (177, 8, 'Seal Check');
INSERT INTO `equipment` VALUES (178, 8, 'Smart Belts');
INSERT INTO `equipment` VALUES (179, 8, 'Case Packer');
INSERT INTO `equipment` VALUES (180, 8, 'Box Former');
INSERT INTO `equipment` VALUES (181, 8, 'Case Conveyor');
INSERT INTO `equipment` VALUES (182, 8, 'Horseshoe');
INSERT INTO `equipment` VALUES (183, 8, 'Tape Machine');
INSERT INTO `equipment` VALUES (184, 8, 'Case Printer');
INSERT INTO `equipment` VALUES (185, 8, 'Other');
INSERT INTO `equipment` VALUES (9, 0, 'Doboy');
INSERT INTO `equipment` VALUES (196, 0, 'Top Load Carton');
INSERT INTO `equipment` VALUES (198, 0, 'Adco Carton');
INSERT INTO `equipment` VALUES (186, 0, 'Other');
INSERT INTO `equipment` VALUES (1, 0, 'Poly 1');
INSERT INTO `equipment` VALUES (187, 1, 'Left Bagger');
INSERT INTO `equipment` VALUES (188, 1, 'Right Bagger');
INSERT INTO `equipment` VALUES (189, 1, 'Dump Station');
INSERT INTO `equipment` VALUES (190, 1, 'Product Shaker');
INSERT INTO `equipment` VALUES (191, 1, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (192, 1, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (193, 1, 'Pants Legs');
INSERT INTO `equipment` VALUES (194, 1, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (195, 1, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (197, 1, 'Left Scale');
INSERT INTO `equipment` VALUES (199, 1, 'Right Scale');
INSERT INTO `equipment` VALUES (200, 1, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (14, 1, 'Metal Detector');
INSERT INTO `equipment` VALUES (15, 1, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (16, 1, 'Seal Check');
INSERT INTO `equipment` VALUES (17, 1, 'Smart Belts');
INSERT INTO `equipment` VALUES (18, 1, 'Case Packer');
INSERT INTO `equipment` VALUES (19, 1, 'Box Former');
INSERT INTO `equipment` VALUES (20, 1, 'Case Conveyor');
INSERT INTO `equipment` VALUES (21, 1, 'Horseshoe');
INSERT INTO `equipment` VALUES (22, 1, 'Tape Machine');
INSERT INTO `equipment` VALUES (23, 1, 'Case Printer');
INSERT INTO `equipment` VALUES (24, 1, 'Other');
INSERT INTO `equipment` VALUES (2, 0, 'Poly 2');
INSERT INTO `equipment` VALUES (25, 2, 'Left Bagger');
INSERT INTO `equipment` VALUES (26, 2, 'Right Bagger');
INSERT INTO `equipment` VALUES (27, 2, 'Dump Satation');
INSERT INTO `equipment` VALUES (28, 2, 'Product Shaker');
INSERT INTO `equipment` VALUES (29, 2, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (30, 2, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (31, 2, 'Pants Legs');
INSERT INTO `equipment` VALUES (32, 2, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (33, 2, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (34, 2, 'Left Scale');
INSERT INTO `equipment` VALUES (35, 2, 'Right Scale');
INSERT INTO `equipment` VALUES (36, 2, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (37, 2, 'Metal Detector');
INSERT INTO `equipment` VALUES (38, 2, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (39, 2, 'Seal Check');
INSERT INTO `equipment` VALUES (40, 2, 'Smart Belts');
INSERT INTO `equipment` VALUES (41, 2, 'Case Packer');
INSERT INTO `equipment` VALUES (42, 2, 'Box Former');
INSERT INTO `equipment` VALUES (43, 2, 'Case Conveyor');
INSERT INTO `equipment` VALUES (44, 2, 'Horseshoe');
INSERT INTO `equipment` VALUES (45, 2, 'Tape Machine');
INSERT INTO `equipment` VALUES (46, 2, 'Case Printer');
INSERT INTO `equipment` VALUES (47, 2, 'Other');
INSERT INTO `equipment` VALUES (3, 0, 'Poly 3');
INSERT INTO `equipment` VALUES (48, 3, 'Left Bagger');
INSERT INTO `equipment` VALUES (49, 3, 'Right Bagger');
INSERT INTO `equipment` VALUES (50, 3, 'Dump Satation');
INSERT INTO `equipment` VALUES (51, 3, 'Product Shaker');
INSERT INTO `equipment` VALUES (52, 3, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (53, 3, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (54, 3, 'Pants Legs');
INSERT INTO `equipment` VALUES (55, 3, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (56, 3, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (57, 3, 'Left Scale');
INSERT INTO `equipment` VALUES (58, 3, 'Right Scale');
INSERT INTO `equipment` VALUES (59, 3, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (60, 3, 'Metal Detector');
INSERT INTO `equipment` VALUES (61, 3, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (62, 3, 'Seal Check');
INSERT INTO `equipment` VALUES (63, 3, 'Smart Belts');
INSERT INTO `equipment` VALUES (64, 3, 'Case Packer');
INSERT INTO `equipment` VALUES (65, 3, 'Box Former');
INSERT INTO `equipment` VALUES (66, 3, 'Case Conveyor');
INSERT INTO `equipment` VALUES (67, 3, 'Horseshoe');
INSERT INTO `equipment` VALUES (68, 3, 'Tape Machine');
INSERT INTO `equipment` VALUES (69, 3, 'Case Printer');
INSERT INTO `equipment` VALUES (70, 3, 'Other');
INSERT INTO `equipment` VALUES (4, 0, 'Poly 4');
INSERT INTO `equipment` VALUES (71, 4, 'Left Bagger');
INSERT INTO `equipment` VALUES (72, 4, 'Right Bagger');
INSERT INTO `equipment` VALUES (73, 4, 'Dump Satation');
INSERT INTO `equipment` VALUES (74, 4, 'Product Shaker');
INSERT INTO `equipment` VALUES (75, 4, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (76, 4, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (77, 4, 'Pants Legs');
INSERT INTO `equipment` VALUES (78, 4, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (79, 4, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (80, 4, 'Left Scale');
INSERT INTO `equipment` VALUES (81, 4, 'Right Scale');
INSERT INTO `equipment` VALUES (82, 4, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (83, 4, 'Metal Detector');
INSERT INTO `equipment` VALUES (84, 4, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (85, 4, 'Seal Check');
INSERT INTO `equipment` VALUES (86, 4, 'Smart Belts');
INSERT INTO `equipment` VALUES (87, 4, 'Case Packer');
INSERT INTO `equipment` VALUES (88, 4, 'Box Former');
INSERT INTO `equipment` VALUES (89, 4, 'Case Conveyor');
INSERT INTO `equipment` VALUES (90, 4, 'Horseshoe');
INSERT INTO `equipment` VALUES (91, 4, 'Tape Machine');
INSERT INTO `equipment` VALUES (92, 4, 'Case Printer');
INSERT INTO `equipment` VALUES (93, 4, 'Other');
INSERT INTO `equipment` VALUES (5, 0, 'Poly 5');
INSERT INTO `equipment` VALUES (94, 5, 'Left Bagger');
INSERT INTO `equipment` VALUES (95, 5, 'Right Bagger');
INSERT INTO `equipment` VALUES (96, 5, 'Dump Satation');
INSERT INTO `equipment` VALUES (97, 5, 'Product Shaker');
INSERT INTO `equipment` VALUES (98, 5, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (99, 5, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (100, 5, 'Pants Legs');
INSERT INTO `equipment` VALUES (101, 5, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (102, 5, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (103, 5, 'Left Scale');
INSERT INTO `equipment` VALUES (104, 5, 'Right Scale');
INSERT INTO `equipment` VALUES (105, 5, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (106, 5, 'Metal Detector');
INSERT INTO `equipment` VALUES (107, 5, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (108, 5, 'Seal Check');
INSERT INTO `equipment` VALUES (109, 5, 'Smart Belts');
INSERT INTO `equipment` VALUES (110, 5, 'Case Packer');
INSERT INTO `equipment` VALUES (111, 5, 'Box Former');
INSERT INTO `equipment` VALUES (112, 5, 'Case Conveyor');
INSERT INTO `equipment` VALUES (113, 5, 'Horseshoe');
INSERT INTO `equipment` VALUES (114, 5, 'Tape Machine');
INSERT INTO `equipment` VALUES (115, 5, 'Case Printer');
INSERT INTO `equipment` VALUES (116, 5, 'Other');
INSERT INTO `equipment` VALUES (6, 0, 'Poly 6');
INSERT INTO `equipment` VALUES (117, 6, 'Left Bagger');
INSERT INTO `equipment` VALUES (118, 6, 'Right Bagger');
INSERT INTO `equipment` VALUES (119, 6, 'Dump Satation');
INSERT INTO `equipment` VALUES (120, 6, 'Product Shaker');
INSERT INTO `equipment` VALUES (121, 6, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (122, 6, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (123, 6, 'Pants Legs');
INSERT INTO `equipment` VALUES (124, 6, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (125, 6, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (126, 6, 'Left Scale');
INSERT INTO `equipment` VALUES (127, 6, 'Right Scale');
INSERT INTO `equipment` VALUES (128, 6, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (129, 6, 'Metal Detector');
INSERT INTO `equipment` VALUES (130, 6, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (131, 6, 'Seal Check');
INSERT INTO `equipment` VALUES (132, 6, 'Smart Belts');
INSERT INTO `equipment` VALUES (133, 6, 'Case Packer');
INSERT INTO `equipment` VALUES (134, 6, 'Box Former');
INSERT INTO `equipment` VALUES (135, 6, 'Case Conveyor');
INSERT INTO `equipment` VALUES (136, 6, 'Horseshoe');
INSERT INTO `equipment` VALUES (137, 6, 'Tape Machine');
INSERT INTO `equipment` VALUES (138, 6, 'Case Printer');
INSERT INTO `equipment` VALUES (139, 6, 'Other');
INSERT INTO `equipment` VALUES (7, 0, 'Poly 7');
INSERT INTO `equipment` VALUES (140, 7, 'Left Bagger');
INSERT INTO `equipment` VALUES (141, 7, 'Right Bagger');
INSERT INTO `equipment` VALUES (142, 7, 'Dump Satation');
INSERT INTO `equipment` VALUES (143, 7, 'Product Shaker');
INSERT INTO `equipment` VALUES (144, 7, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (145, 7, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (146, 7, 'Pants Legs');
INSERT INTO `equipment` VALUES (147, 7, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (148, 7, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (149, 7, 'Left Scale');
INSERT INTO `equipment` VALUES (150, 7, 'Right Scale');
INSERT INTO `equipment` VALUES (151, 7, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (152, 7, 'Metal Detector');
INSERT INTO `equipment` VALUES (153, 7, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (154, 7, 'Seal Check');
INSERT INTO `equipment` VALUES (155, 7, 'Smart Belts');
INSERT INTO `equipment` VALUES (156, 7, 'Case Packer');
INSERT INTO `equipment` VALUES (157, 7, 'Box Former');
INSERT INTO `equipment` VALUES (158, 7, 'Case Conveyor');
INSERT INTO `equipment` VALUES (159, 7, 'Horseshoe');
INSERT INTO `equipment` VALUES (160, 7, 'Tape Machine');
INSERT INTO `equipment` VALUES (161, 7, 'Case Printer');
INSERT INTO `equipment` VALUES (162, 7, 'Other');
INSERT INTO `equipment` VALUES (8, 0, 'Poly 0');
INSERT INTO `equipment` VALUES (163, 8, 'Left Bagger');
INSERT INTO `equipment` VALUES (164, 8, 'Right Bagger');
INSERT INTO `equipment` VALUES (165, 8, 'Dump Satation');
INSERT INTO `equipment` VALUES (166, 8, 'Product Shaker');
INSERT INTO `equipment` VALUES (167, 8, 'Pocket Elevator');
INSERT INTO `equipment` VALUES (168, 8, 'Diverter Bucket');
INSERT INTO `equipment` VALUES (169, 8, 'Pants Legs');
INSERT INTO `equipment` VALUES (170, 8, 'Left Infeed Vibrator');
INSERT INTO `equipment` VALUES (171, 8, 'Right Infeed Vibrato');
INSERT INTO `equipment` VALUES (172, 8, 'Left Scale');
INSERT INTO `equipment` VALUES (173, 8, 'Right Scale');
INSERT INTO `equipment` VALUES (174, 8, 'Take-Away Conveyor');
INSERT INTO `equipment` VALUES (175, 8, 'Metal Detector');
INSERT INTO `equipment` VALUES (176, 8, 'Metal Detector Conve');
INSERT INTO `equipment` VALUES (177, 8, 'Seal Check');
INSERT INTO `equipment` VALUES (178, 8, 'Smart Belts');
INSERT INTO `equipment` VALUES (179, 8, 'Case Packer');
INSERT INTO `equipment` VALUES (180, 8, 'Box Former');
INSERT INTO `equipment` VALUES (181, 8, 'Case Conveyor');
INSERT INTO `equipment` VALUES (182, 8, 'Horseshoe');
INSERT INTO `equipment` VALUES (183, 8, 'Tape Machine');
INSERT INTO `equipment` VALUES (184, 8, 'Case Printer');
INSERT INTO `equipment` VALUES (185, 8, 'Other');
INSERT INTO `equipment` VALUES (9, 0, 'Doboy');
INSERT INTO `equipment` VALUES (196, 0, 'Top Load Carton');
INSERT INTO `equipment` VALUES (198, 0, 'Adco Carton');
INSERT INTO `equipment` VALUES (186, 0, 'Other');
# --------------------------------------------------------

#
# Table structure for table `groups`
#
# Creation: Aug 17, 2003 at 11:26 AM
# Last update: Aug 20, 2003 at 02:34 PM
#

CREATE TABLE `groups` (
  `uname` varchar(20) NOT NULL default '',
  `passwd` varchar(12) NOT NULL default '',
  `grp` set('clerk','mechanic','lead','supervisor','manager') NOT NULL default '',
  `last_login` timestamp(14) NOT NULL
) TYPE=MyISAM COMMENT='Table of groups of users';

#
# Dumping data for table `groups`
#

INSERT INTO `groups` VALUES ('spongebob', 'squarepants', 'manager', 20030820143453);
# --------------------------------------------------------

#
# Table structure for table `hot_jobs`
#
# Creation: Aug 17, 2003 at 11:26 AM
# Last update: Aug 17, 2003 at 01:10 PM
#

CREATE TABLE `hot_jobs` (
  `hj_id` int(11) NOT NULL default '0',
  `date` date NOT NULL default '0000-00-00',
  `short_description` varchar(50) NOT NULL default '',
  `description` text NOT NULL,
  `parts` text NOT NULL,
  `comments` text NOT NULL,
  `equipment_id` int(11) NOT NULL default '0',
  `mechanic_id` int(4) NOT NULL default '0',
  `operator_id` int(4) NOT NULL default '0',
  `hours` tinyint(4) NOT NULL default '0',
  PRIMARY KEY  (`hj_id`)
) TYPE=MyISAM COMMENT='Production-down jobs';

#
# Dumping data for table `hot_jobs`
#

INSERT INTO `hot_jobs` VALUES (3, '2003-07-07', 'Widget line down for broken conveyor', 'The widget line had a broken conveyor where a wizard had cast a spell on the dirve motor.', 'Baldor 2HP 3-phase 460V motor', '', 0, 1, 0, 1);
# --------------------------------------------------------

#
# Table structure for table `mechanics`
#
# Creation: Aug 17, 2003 at 11:26 AM
# Last update: Aug 17, 2003 at 11:50 AM
#

CREATE TABLE `mechanics` (
  `id` int(11) NOT NULL auto_increment,
  `lname` char(20) NOT NULL default '',
  `fname` char(20) NOT NULL default '',
  `craft` set('op','mech','other') NOT NULL default '',
  `busy` tinyint(4) NOT NULL default '0',
  PRIMARY KEY  (`id`,`id`)
) TYPE=MyISAM COMMENT='List of repack mechanics' AUTO_INCREMENT=2 ;

#
# Dumping data for table `mechanics`
#

INSERT INTO `mechanics` VALUES (1, 'morris', 'chris', 'mech', 0);
# --------------------------------------------------------

#
# Table structure for table `next_wo`
#
# Creation: Aug 17, 2003 at 11:26 AM
# Last update: Aug 19, 2003 at 02:38 PM
#

CREATE TABLE `next_wo` (
  `id` int(11) NOT NULL default '0'
) TYPE=MyISAM COMMENT='The next work order number to use';

#
# Dumping data for table `next_wo`
#

INSERT INTO `next_wo` VALUES (16);
# --------------------------------------------------------

#
# Table structure for table `play`
#
# Creation: Aug 17, 2003 at 11:26 AM
# Last update: Aug 17, 2003 at 11:26 AM
#

CREATE TABLE `play` (
  `foo` tinyint(4) NOT NULL default '0',
  `bar` tinyint(4) NOT NULL default '0',
  `zonk` tinyint(4) NOT NULL default '0'
) TYPE=MyISAM;

#
# Dumping data for table `play`
#

# --------------------------------------------------------

#
# Table structure for table `priority`
#
# Creation: Aug 17, 2003 at 11:26 AM
# Last update: Aug 17, 2003 at 11:26 AM
#

CREATE TABLE `priority` (
  `priority` tinyint(11) NOT NULL default '0',
  `description` text NOT NULL,
  PRIMARY KEY  (`priority`)
) TYPE=MyISAM;

#
# Dumping data for table `priority`
#

INSERT INTO `priority` VALUES (1, '1 Immediate: Safety /Production Down');
INSERT INTO `priority` VALUES (2, '2 ASAP');
INSERT INTO `priority` VALUES (3, '3 Before specific date');
INSERT INTO `priority` VALUES (4, '4 No priority');
INSERT INTO `priority` VALUES (5, '5 Standing work order');
# --------------------------------------------------------

#
# Table structure for table `queries`
#
# Creation: Aug 17, 2003 at 11:26 AM
# Last update: Aug 20, 2003 at 02:39 PM
#

CREATE TABLE `queries` (
  `name` varchar(25) NOT NULL default '',
  `mode` set('work_order','hot_job','equipment') NOT NULL default '',
  `caption` varchar(25) NOT NULL default '',
  `title` varchar(50) NOT NULL default '',
  `groups` text NOT NULL,
  `sql` text NOT NULL,
  `col_attributes` text,
  PRIMARY KEY  (`name`),
  FULLTEXT KEY `col_attributes` (`col_attributes`),
  FULLTEXT KEY `col_attributes_2` (`col_attributes`)
) TYPE=MyISAM COMMENT='SQL query store';

#
# Dumping data for table `queries`
#

INSERT INTO `queries` VALUES ('WR_ALL', 'work_order', 'All Work Request', 'Work Request Pending Approval', 'manager,lead', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.description AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE wo_status = \'Pending Approval\'', 'a:8:{i:0;a:3:{s:5:"style";s:21:"text-align: center;  ";s:5:"width";s:2:"5%";s:6:"nowrap";s:6:"nowrap";}i:1;a:1:{s:5:"width";s:2:"7%";}i:2;a:1:{s:5:"width";s:2:"5%";}i:3;a:1:{s:5:"width";s:3:"25%";}i:4;a:1:{s:5:"width";s:2:"5%";}i:5;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:6;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:7;a:1:{s:5:"width";s:2:"5%";}}');
INSERT INTO `queries` VALUES ('WO_BACKLOG', 'work_order', 'Pending Assignment', 'Work Order Backlog', 'manager,lead', 'SELECT wo.wo_id AS \'W.O.\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Desciption\', wo.wo_status AS \'Status\', wo.submit_date AS \'Submit Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE wo_status LIKE \'Approved\' AND mechanic_id=0', 'a:6:{i:0;a:2:{s:5:"style";s:21:"text-align: center;  ";s:5:"width";s:3:"10%";}i:1;a:1:{s:5:"width";s:2:"7%";}i:2;a:1:{s:5:"width";s:3:"25%";}i:3;a:1:{s:5:"width";s:2:"5%";}i:4;a:1:{s:5:"width";s:2:"5%";}i:5;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}}');
INSERT INTO `queries` VALUES ('WO_PENDING', 'work_order', 'Active', 'Work Orders Pending Completion', 'manager,lead,mechanic', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.description AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE wo_status LIKE \'assigned\' AND complete_date IS NULL', 'a:8:{i:0;a:3:{s:5:"style";s:21:"text-align: center;  ";s:5:"width";s:2:"5%";s:6:"nowrap";s:6:"nowrap";}i:1;a:1:{s:5:"width";s:2:"7%";}i:2;a:1:{s:5:"width";s:2:"5%";}i:3;a:1:{s:5:"width";s:3:"25%";}i:4;a:1:{s:5:"width";s:2:"5%";}i:5;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:6;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:7;a:1:{s:5:"width";s:2:"5%";}}');
INSERT INTO `queries` VALUES ('WO_SUSPENDED', 'work_order', 'Suspended', 'Suspended Work Orders', 'manager,lead', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.description AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE wo_status LIKE \'Suspended\'', 'a:8:{i:0;a:3:{s:5:"style";s:21:"text-align: center;  ";s:5:"width";s:2:"5%";s:6:"nowrap";s:6:"nowrap";}i:1;a:1:{s:5:"width";s:2:"7%";}i:2;a:1:{s:5:"width";s:2:"5%";}i:3;a:1:{s:5:"width";s:3:"25%";}i:4;a:1:{s:5:"width";s:2:"5%";}i:5;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:6;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:7;a:1:{s:5:"width";s:2:"5%";}}');
INSERT INTO `queries` VALUES ('WO_FINISHED', 'work_order', 'Pending Closeout', 'Completed Work Orders Ready to be Closed Out', 'manager,lead', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.description AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE complete_date IS NOT NULL AND wo_status NOT LIKE \'Completed\' AND wo_status NOT LIKE \'Hot Job\'', 'a:8:{i:0;a:3:{s:5:"style";s:21:"text-align: center;  ";s:5:"width";s:2:"5%";s:6:"nowrap";s:6:"nowrap";}i:1;a:1:{s:5:"width";s:2:"7%";}i:2;a:1:{s:5:"width";s:2:"5%";}i:3;a:1:{s:5:"width";s:3:"25%";}i:4;a:1:{s:5:"width";s:2:"5%";}i:5;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:6;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:7;a:1:{s:5:"width";s:2:"5%";}}');
INSERT INTO `queries` VALUES ('WO_ALL', 'work_order', 'All Work Orders', 'All Work Orders', 'manager,lead', 'SELECT wo.wo_id AS \'Work Order\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE wo_status NOT LIKE \'Pending Approval\'', 'a:8:{i:0;a:3:{s:5:"style";s:21:"text-align: center;  ";s:5:"width";s:2:"5%";s:6:"nowrap";s:6:"nowrap";}i:1;a:1:{s:5:"width";s:2:"7%";}i:2;a:1:{s:5:"width";s:2:"5%";}i:3;a:1:{s:5:"width";s:3:"25%";}i:4;a:1:{s:5:"width";s:2:"5%";}i:5;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:6;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:7;a:1:{s:5:"width";s:2:"5%";}}');
INSERT INTO `queries` VALUES ('RECENT_ACTIVITY', 'work_order', 'Recent', 'The 10 Most Recent System Activites', 'manager', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority ORDER BY updated DESC LIMIT 10', 'a:8:{i:0;a:3:{s:5:"style";s:21:"text-align: center;  ";s:5:"width";s:2:"5%";s:6:"nowrap";s:6:"nowrap";}i:1;a:1:{s:5:"width";s:2:"7%";}i:2;a:1:{s:5:"width";s:2:"5%";}i:3;a:1:{s:5:"width";s:3:"25%";}i:4;a:1:{s:5:"width";s:2:"5%";}i:5;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:6;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:7;a:1:{s:5:"width";s:2:"5%";}}');
INSERT INTO `queries` VALUES ('NEW_TO_YOU', 'work_order', 'While you were out', 'System Activity Since Your Last Login', 'manager,lead', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE updated > 20030715082003', 'a:8:{i:0;a:2:{s:5:"style";s:21:"text-align: center;  ";s:5:"width";s:3:"10%";}i:1;a:1:{s:5:"width";s:2:"7%";}i:2;a:1:{s:5:"width";s:2:"5%";}i:3;a:1:{s:5:"width";s:3:"25%";}i:4;a:1:{s:5:"width";s:2:"5%";}i:5;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:6;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:7;a:1:{s:5:"width";s:2:"5%";}}');
INSERT INTO `queries` VALUES ('WO_AUDIT', 'work_order', 'Audit Items', 'Work Orders Related To Audits', 'manager, lead', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE audit_item = 1 ', 'a:8:{i:0;a:3:{s:5:"style";s:21:"text-align: center;  ";s:5:"width";s:2:"5%";s:6:"nowrap";s:6:"nowrap";}i:1;a:1:{s:5:"width";s:2:"7%";}i:2;a:1:{s:5:"width";s:2:"5%";}i:3;a:1:{s:5:"width";s:3:"25%";}i:4;a:1:{s:5:"width";s:2:"5%";}i:5;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:6;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:7;a:1:{s:5:"width";s:2:"5%";}}');
INSERT INTO `queries` VALUES ('WO_RECENT_CLOSED', 'work_order', 'Recently Closed', 'The 10 Most Recently Closed Work Orders', 'manager', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE wo_status = \'Completed\' ORDER BY  complete_date DESC LIMIT 0,10', 'a:8:{i:0;a:2:{s:5:"style";s:21:"text-align: center;  ";s:5:"width";s:2:"5%";}i:1;a:1:{s:5:"width";s:2:"7%";}i:2;a:1:{s:5:"width";s:2:"5%";}i:3;a:1:{s:5:"width";s:3:"25%";}i:4;a:1:{s:5:"width";s:2:"5%";}i:5;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:6;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:7;a:1:{s:5:"width";s:2:"5%";}}');
INSERT INTO `queries` VALUES ('HJ_ALL', 'hot_job', 'All Trouble Calls', 'All Production-Down Jobs', 'manager,lead', 'SELECT hj_id AS \'Trouble Call\', hj.date AS \'Date\', description AS \'Summary\' , mech.lname AS \'Mechanic\', op.lname AS \'Operator\' FROM hot_jobs AS hj LEFT  JOIN mechanics AS mech ON mech.id = hj.mechanic_id LEFT  JOIN mechanics AS op ON op.id = hj.operator_id', 'a:5:{i:0;a:2:{s:5:"style";s:19:"text-align: center;";s:6:"nowrap";s:6:"nowrap";}i:1;a:0:{}i:2;a:0:{}i:3;a:0:{}i:4;a:0:{}}');
INSERT INTO `queries` VALUES ('HJ_RECENT', 'hot_job', 'Recent Trouble Calls', 'Recent Production-Down Jobs', 'manager,lead', 'SELECT hj_id AS \'Trouble Call\', hj.date AS \'Date\', description AS \'Summary\' , mech.lname AS \'Mechanic\', op.lname AS \'Operator\' FROM hot_jobs AS hj LEFT  JOIN mechanics AS mech ON mech.id = hj.mechanic_id LEFT  JOIN mechanics AS op ON op.id = hj.operator_id ORDER BY Date DESC LIMIT 0, 13', 'a:5:{i:0;a:2:{s:5:"style";s:19:"text-align: center;";s:6:"nowrap";s:6:"nowrap";}i:1;a:0:{}i:2;a:0:{}i:3;a:0:{}i:4;a:0:{}}');
INSERT INTO `queries` VALUES ('WO_SEARCH', 'work_order', 'Search Results', 'Search Results', 'manager,lead', 'SELECT wo.wo_id AS \'Work Order\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE " . $_REQUEST[\'found\'] . "', 'a:8:{i:0;a:3:{s:5:"style";s:21:"text-align: center;  ";s:5:"width";s:2:"5%";s:6:"nowrap";s:6:"nowrap";}i:1;a:1:{s:5:"width";s:2:"7%";}i:2;a:1:{s:5:"width";s:2:"5%";}i:3;a:1:{s:5:"width";s:3:"25%";}i:4;a:1:{s:5:"width";s:2:"5%";}i:5;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:6;a:2:{s:5:"width";s:3:"10%";s:5:"style";s:19:"text-align: center;";}i:7;a:1:{s:5:"width";s:2:"5%";}}');
# --------------------------------------------------------

#
# Table structure for table `work_orders`
#
# Creation: Aug 17, 2003 at 11:26 AM
# Last update: Aug 20, 2003 at 02:41 PM
#

CREATE TABLE `work_orders` (
  `wo_id` int(11) NOT NULL auto_increment,
  `descriptive_text` varchar(50) NOT NULL default '',
  `audit_item` tinyint(4) default NULL,
  `requestor` varchar(20) NOT NULL default '',
  `approval` varchar(20) NOT NULL default '',
  `equipment` varchar(30) NOT NULL default '',
  `description` text NOT NULL,
  `action` text,
  `mechanic_id` int(11) default NULL,
  `priority` tinyint(1) NOT NULL default '0',
  `submit_date` date NOT NULL default '0000-00-00',
  `est_hours` tinyint(4) default NULL,
  `act_hours` tinyint(4) default NULL,
  `account` varchar(15) default NULL,
  `complete_date` date default NULL,
  `coordinating_instructions` text,
  `needed_date` date default NULL,
  `wo_status` set('Approved','Assigned','Pending Approval','Suspended','Completed','Rejected','Hot Job') NOT NULL default '',
  `inspected_by` varchar(20) NOT NULL default '',
  `updated` timestamp(14) NOT NULL,
  PRIMARY KEY  (`wo_id`),
  INDEX (`priority`),
  INDEX (`submit_date`),
  CONSTRAINT `fk_mechanic` FOREIGN KEY (`mechanic_id`) REFERENCES `mechanics`(`id`)
) ENGINE=InnoDB COMMENT='Repack work orders' AUTO_INCREMENT=14 ;

#
# Dumping data for table `work_orders`
#

INSERT INTO `work_orders` VALUES (6, 'The very 1st FC work order', NULL, 'spongebob', '', '', 'Wow the first work order entered into the demo.', NULL, 0, 2, '2003-08-17', NULL, NULL, NULL, NULL, NULL, NULL, 'Completed', '', **************);
INSERT INTO `work_orders` VALUES (8, 'Replace light bulb', NULL, 'spongebob', '', '', 'Remove old bulb\r\nScrew in new bulb', NULL, 0, 2, '2003-08-17', NULL, NULL, NULL, NULL, NULL, NULL, 'Pending Approval', '', 20030817130857);
INSERT INTO `work_orders` VALUES (9, 'test request', NULL, 'spongebob', '', '', 'test request', NULL, 0, 1, '2003-08-17', NULL, NULL, NULL, NULL, NULL, NULL, 'Pending Approval', '', 20030817131234);
INSERT INTO `work_orders` VALUES (13, 'searsch.php gives an error', NULL, 'spongebob', '', '', 'Tried to search a word.', 'Problem fixed. WO_SEARCH query was omitted when database was setup. WO_SEARCH added to database.', 1, 4, '2003-08-19', NULL, 0, NULL, '2003-08-20', 'I either forgot or I never was told how to retireve the informatio you want.\r\nCopy everything between the lines and email it to Chris url:\r\nquery_name:\r\nquery_sql: SELECT * FROM queries WHERE name like \'WO_SEARCH\'\r\n', NULL, 'Completed', 'self', 20030820144146);






