<?php 

function css_site($style_sheet) {

    //determine font for this platform
    if (browser_is_windows() && browser_is_ie()) {

        //ie needs smaller fonts than anyone else
        $font_size='x-small';
        $font_smaller='7pt';
        $font_smallest='7pt';


    } else if (browser_is_windows()) {

        //netscape or "other" on wintel
        $font_size='small';
               $font_smaller='x-small';
        $font_smallest='x-small';

    } else if (browser_is_mac()){

        //mac users need bigger fonts
        $font_size='medium';
        $font_smaller='small';
        $font_smallest='x-small';

    } else {

        //linux and other users
        $font_size='small';
        $font_smaller='x-small';
        $font_smallest='x-small';

    }

    $site_fonts='verdana, arial, helvetica, sans-serif';

    // Add Navy-specific styles
    $navy_theme = "
        body {
            background-color: #002147;
            color: #ffffff;
        }
        a {
            color: #0056b3;
        }
    ";

    echo "<style>$navy_theme</style>";

    require($style_sheet);

}

?>