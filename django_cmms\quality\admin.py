from django.contrib import admin
from .models import (
    TestProcedure, TestParameter, TestRequest, TestResult,
    TestParameterResult, QualityAlert
)


@admin.register(TestProcedure)
class TestProcedureAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'test_type', 'frequency', 'is_mandatory', 'is_active']
    list_filter = ['test_type', 'frequency', 'is_mandatory', 'is_active']
    search_fields = ['name', 'code']
    filter_horizontal = ['applicable_products', 'applicable_categories']


@admin.register(TestParameter)
class TestParameterAdmin(admin.ModelAdmin):
    list_display = ['test_procedure', 'name', 'parameter_type', 'unit_of_measure', 'is_critical']
    list_filter = ['parameter_type', 'is_critical']
    search_fields = ['name', 'code']
    ordering = ['test_procedure', 'sequence_order']


@admin.register(TestRequest)
class TestRequestAdmin(admin.ModelAdmin):
    list_display = ['request_id', 'product', 'test_procedure', 'requested_by', 'date_required', 'status', 'priority']
    list_filter = ['status', 'priority', 'date_requested', 'test_procedure']
    search_fields = ['request_id', 'product__name']
    readonly_fields = ['is_overdue', 'days_overdue']


@admin.register(TestResult)
class TestResultAdmin(admin.ModelAdmin):
    list_display = ['test_request', 'overall_result', 'tested_by', 'test_start_time', 'certificate_issued']
    list_filter = ['overall_result', 'certificate_issued', 'test_start_time']
    search_fields = ['test_request__request_id']
    readonly_fields = ['test_duration', 'is_result_expired']


@admin.register(TestParameterResult)
class TestParameterResultAdmin(admin.ModelAdmin):
    list_display = ['test_result', 'test_parameter', 'get_display_value', 'within_limits']
    list_filter = ['within_limits', 'pass_fail_result']
    search_fields = ['test_parameter__name']


@admin.register(QualityAlert)
class QualityAlertAdmin(admin.ModelAdmin):
    list_display = ['title', 'alert_type', 'severity', 'created_by', 'assigned_to', 'is_resolved']
    list_filter = ['alert_type', 'severity', 'is_resolved', 'created_at']
    search_fields = ['title', 'description']
