<?php
include('config.inc.php');
session_start();

if ($_SESSION['group'] === 'manager') {
    try {
        // Connect to the database using PDO
        $pdo = new PDO("mysql:host=$hostName;dbname=$databaseName", $userName, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $col_attr = [];
        for ($i = 0; $i < $cols; $i++) {
            $col_attr[$i] = [];
            $key = "key" . $i;
            $value = "value" . $i;

            for ($j = 0; $j < count($$key); $j++) {
                if (!empty(${$key}[$j])) {
                    $col_attr[$i][${$key}[$j]] = ${$value}[$j];
                }
            }
        }

        $cereal = serialize($col_attr);
        $sql = "UPDATE queries SET col_attributes = :col_attributes, sql = :sql_query WHERE name LIKE :qry_name";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':col_attributes' => $cereal,
            ':sql_query' => $sql_query,
            ':qry_name' => $qry_name,
        ]);

        echo "Update Successful. <a href='$url'>Go Back</a>";
    } catch (PDOException $e) {
        die("Database error: " . $e->getMessage());
    }
} else {
    echo "<h1>You are unauthorized to edit this table</h1>";
    exit;
}
?>

