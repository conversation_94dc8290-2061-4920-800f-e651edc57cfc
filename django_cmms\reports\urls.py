"""
URL configuration for reports app.
"""
from django.urls import path
from . import views

app_name = 'reports'

urlpatterns = [
    # Dashboard and Analytics
    path('', views.ReportsDashboardView.as_view(), name='dashboard'),
    path('analytics/', views.AdvancedAnalyticsView.as_view(), name='analytics'),
    
    # Work Order Reports
    path('work-orders/', views.WorkOrderReportView.as_view(), name='work_order_report'),
    path('work-orders/export/', views.WorkOrderExportView.as_view(), name='work_order_export'),
    path('work-orders/performance/', views.WorkOrderPerformanceView.as_view(), name='work_order_performance'),
    
    # Equipment Reports
    path('equipment/', views.EquipmentReportView.as_view(), name='equipment_report'),
    path('equipment/reliability/', views.EquipmentReliabilityView.as_view(), name='equipment_reliability'),
    path('equipment/downtime/', views.EquipmentDowntimeView.as_view(), name='equipment_downtime'),
    
    # Maintenance Reports
    path('maintenance/', views.MaintenanceReportView.as_view(), name='maintenance_report'),
    path('maintenance/compliance/', views.MaintenanceComplianceView.as_view(), name='maintenance_compliance'),
    
    # Cost Analysis
    path('costs/', views.CostAnalysisView.as_view(), name='cost_analysis'),
    path('costs/trends/', views.CostTrendsView.as_view(), name='cost_trends'),
    
    # Custom Reports
    path('custom/', views.CustomReportView.as_view(), name='custom_report'),
    path('custom/builder/', views.ReportBuilderView.as_view(), name='report_builder'),
    
    # Export URLs
    path('export/pdf/<str:report_type>/', views.ExportPDFView.as_view(), name='export_pdf'),
    path('export/excel/<str:report_type>/', views.ExportExcelView.as_view(), name='export_excel'),
    
    # AJAX URLs for charts and data
    path('ajax/chart-data/<str:chart_type>/', views.chart_data_ajax, name='chart_data_ajax'),
    path('ajax/kpi-data/', views.kpi_data_ajax, name='kpi_data_ajax'),
]
