<?php
// Display all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Database connection parameters
$hostName = "localhost";
$userName = "root";
$password = "";
$databaseName = "cmms";

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Connect to database
    $connection = mysqli_connect($hostName, $userName, $password, $databaseName);

    if (!$connection) {
        die("Connection failed: " . mysqli_connect_error());
    }

    // Get username and password from form
    $username = mysqli_real_escape_string($connection, $_POST['username']);
    $password = mysqli_real_escape_string($connection, $_POST['password']);

    // Query to check if user exists
    $sql = "SELECT grp, uname, last_login FROM groups WHERE uname='$username' AND passwd='$password'";
    $result = mysqli_query($connection, $sql);

    if (!$result) {
        die("Query failed: " . mysqli_error($connection));
    }

    if (mysqli_num_rows($result) === 1) {
        // User found, set session variables
        $row = mysqli_fetch_assoc($result);
        $_SESSION['user'] = $row['uname'];
        $_SESSION['group'] = $row['grp'];
        $_SESSION['last_login'] = $row['last_login'];

        // Update last login time
        $update_sql = "UPDATE groups SET last_login = NOW() WHERE uname = '$username'";
        mysqli_query($connection, $update_sql);

        // Redirect to dashboard
        header("Location: minimal_dashboard.php");
        exit;
    } else {
        $error = "Invalid username or password";
    }

    mysqli_close($connection);
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Nigerian Navy Integrated Logistics Management System - Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 400px;
            margin: 50px auto;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        button:hover {
            background-color: #45a049;
        }
        .error {
            color: red;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Nigerian Navy Integrated Logistics Management System</h1>

        <?php if (isset($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>

        <form method="post" action="">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit">Login</button>
        </form>

        <p style="text-align: center; margin-top: 20px;">
            Default login: <strong>manager / manager</strong>
        </p>
    </div>
</body>
</html>
