<?php
// This script fixes common PHP compatibility issues in older PHP code
// It adds quotes around array keys and updates deprecated functions

// Display all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>PHP Compatibility Fixer</h1>";

// Directory to scan
$dir = __DIR__;

// Get all PHP files
$files = glob($dir . '/*.php');
$files = array_merge($files, glob($dir . '/*/*.php'));
$files = array_merge($files, glob($dir . '/*/*/*.php'));

// Patterns to fix
$patterns = [
    // Fix array access without quotes
    '~\$_POST\[([a-zA-Z0-9_]+)\]~' => '$_POST[\'$1\']',
    '~\$_GET\[([a-zA-Z0-9_]+)\]~' => '$_GET[\'$1\']',
    '~\$_REQUEST\[([a-zA-Z0-9_]+)\]~' => '$_REQUEST[\'$1\']',
    '~\$_SESSION\[([a-zA-Z0-9_]+)\]~' => '$_SESSION[\'$1\']',
    '~\$_COOKIE\[([a-zA-Z0-9_]+)\]~' => '$_COOKIE[\'$1\']',
    '~\$_SERVER\[([a-zA-Z0-9_]+)\]~' => '$_SERVER[\'$1\']',
    '~\$_ENV\[([a-zA-Z0-9_]+)\]~' => '$_ENV[\'$1\']',
    '~\$_FILES\[([a-zA-Z0-9_]+)\]~' => '$_FILES[\'$1\']',
    
    // Fix deprecated MySQL functions
    '~mysql_connect\((.*?)\)~' => 'mysqli_connect($1)',
    '~mysql_select_db\((.*?)\)~' => 'mysqli_select_db($1)',
    '~mysql_query\((.*?)\)~' => 'mysqli_query($1)',
    '~mysql_fetch_array\((.*?)\)~' => 'mysqli_fetch_array($1)',
    '~mysql_fetch_assoc\((.*?)\)~' => 'mysqli_fetch_assoc($1)',
    '~mysql_fetch_object\((.*?)\)~' => 'mysqli_fetch_object($1)',
    '~mysql_num_rows\((.*?)\)~' => 'mysqli_num_rows($1)',
    '~mysql_affected_rows\((.*?)\)~' => 'mysqli_affected_rows($1)',
    '~mysql_error\(\)~' => 'mysqli_error($connection)',
    '~mysql_close\((.*?)\)~' => 'mysqli_close($1)',
];

// Count of files processed and fixed
$processed = 0;
$fixed = 0;

// Process each file
foreach ($files as $file) {
    $processed++;
    $content = file_get_contents($file);
    $original = $content;
    
    // Apply all patterns
    foreach ($patterns as $pattern => $replacement) {
        $content = preg_replace($pattern, $replacement, $content);
    }
    
    // If content was changed, save the file
    if ($content !== $original) {
        file_put_contents($file, $content);
        $fixed++;
        echo "Fixed: $file<br>";
    }
}

echo "<h2>Summary</h2>";
echo "Processed $processed files<br>";
echo "Fixed $fixed files<br>";

echo "<h2>Next Steps</h2>";
echo "1. Try accessing the application again<br>";
echo "2. If you encounter more errors, note them down and fix them individually<br>";
echo "3. For database connection issues, make sure MySQL is running and the connection parameters are correct<br>";
?>
