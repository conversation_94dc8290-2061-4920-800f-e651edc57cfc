<?PHP
include('config.inc.php');
session_save_path($session_save_path);
session_start();

$return_url = $_SERVER['HTTP_REFERER'] ?? '';

/*
 * See if we need to authenticate a user
 */
if(!empty($_POST['uid']) && !empty($_POST['passwd']))
{
  include("./config.inc.php"); //common database variables

  /*
   * Establish a connection to the database
   */
  try {
    // Try mysqli (newer)
    $connection = mysqli_connect($hostName, $userName, $password, $databaseName);
    
    if (!$connection) {
      die('Could not connect to the database server: ' . mysqli_connect_error());
    }
    
    $sql = "SELECT grp, uname, last_login 
              FROM groups 
             WHERE uname='" . mysqli_real_escape_string($connection, $_POST["uid"]) . "' 
               AND passwd='" . mysqli_real_escape_string($connection, $_POST["passwd"]) . "' "; 

    $sql_results = mysqli_query($connection, $sql);
    
    if (!$sql_results) {
      die("Could not execute query: <br> $sql<br>" . mysqli_error($connection));
    }

    $num_rows = mysqli_num_rows($sql_results); 
    
    if ($num_rows == 1) {
      $result = mysqli_fetch_object($sql_results);
      
      $_SESSION['last_login'] = $result->last_login;
      $_SESSION['group']      = $result->grp;
      $_SESSION['user']       = $result->uname;
      
      $verified = "Login Successful";

      /*
       * Record the login time in the database. This is used for showing new WOs
       */
      $sql_login_time = "UPDATE groups SET last_login = NOW() WHERE uname = '" . mysqli_real_escape_string($connection, $result->uname) . "'";
      mysqli_query($connection, $sql_login_time) or die("Could not update last login time: $sql_login_time<br>" . mysqli_error($connection));
    }
    else {
      $verified = "<HTML><BODY>Login Failed<br> <a href=\"./list.php\">Go Back</a></BODY></html>";
      exit;
    }
  } catch (Exception $e) {
    die("Database connection error: " . $e->getMessage());
  }
} 
else {
  session_destroy();
}
?>

<script type="text/javascript">
<!--
function reload() { 
<?PHP
if(isset($_SESSION['group']) && ($_SESSION['group'] == "manager" || $_SESSION['group'] == "lead"))
{?>
 window.open("./important.php", "important_window", "toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,titlebar=no,copyhistory=yes,width=650,height=575");
<?PHP } ?>
top.location = "index.php?nav=work_orders"
//top.location = "start.php"
} 
 
setTimeout("reload();", 500);
// -->
</script>
