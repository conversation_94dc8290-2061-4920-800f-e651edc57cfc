from django.contrib import admin
from .models import (
    DocumentCategory, Document, DocumentVersion, Discussion,
    DiscussionMessage, Notification, ActivityLog
)


@admin.register(DocumentCategory)
class DocumentCategoryAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'color', 'requires_approval', 'is_active']
    list_filter = ['requires_approval', 'is_active']
    search_fields = ['name', 'code']


@admin.register(Document)
class DocumentAdmin(admin.ModelAdmin):
    list_display = ['document_number', 'title', 'document_type', 'category', 'status', 'author', 'version']
    list_filter = ['document_type', 'category', 'status', 'created_at']
    search_fields = ['title', 'document_number']
    filter_horizontal = ['access_groups', 'related_equipment', 'related_work_orders']
    readonly_fields = ['is_expired', 'file_extension', 'download_count']


@admin.register(DocumentVersion)
class DocumentVersionAdmin(admin.ModelAdmin):
    list_display = ['document', 'version', 'created_by', 'created_at']
    list_filter = ['created_at']
    search_fields = ['document__title', 'version']


@admin.register(Discussion)
class DiscussionAdmin(admin.ModelAdmin):
    list_display = ['title', 'discussion_type', 'started_by', 'is_closed', 'is_pinned', 'created_at']
    list_filter = ['discussion_type', 'is_closed', 'is_pinned', 'created_at']
    search_fields = ['title']
    filter_horizontal = ['participants']
    readonly_fields = ['message_count', 'last_message']


@admin.register(DiscussionMessage)
class DiscussionMessageAdmin(admin.ModelAdmin):
    list_display = ['discussion', 'author', 'created_at', 'is_edited']
    list_filter = ['is_edited', 'created_at']
    search_fields = ['content', 'author__username']
    readonly_fields = ['reply_count']


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ['recipient', 'notification_type', 'title', 'is_read', 'created_at']
    list_filter = ['notification_type', 'is_read', 'created_at']
    search_fields = ['title', 'recipient__username']


@admin.register(ActivityLog)
class ActivityLogAdmin(admin.ModelAdmin):
    list_display = ['user', 'action_type', 'object_type', 'object_name', 'created_at']
    list_filter = ['action_type', 'object_type', 'created_at']
    search_fields = ['user__username', 'description', 'object_name']
