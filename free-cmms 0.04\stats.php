<?PHP 

include("./config.inc.php");
session_save_path($session_save_path);
session_start(); ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>
<head>

    <meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
  
    <title>Submit a Work Order</title>
 
<!-- Include style sheet to make web form similar to paper form --> 

<link REL=StyleSheet HREF="/styles/nicetable.css" TYPE="text/css" TITLE="2D Input" MEDIA=all> 
<script src="./libraries/functions.js" type="text/javascript" language="javascript"></script>

</head>

<body>

<?PHP
    {
    $start_date = "2003-05-01";
    $end_date = "2003-05-19";

    /*
     *Establish a coneection to the database
     */
    
    include("config.inc.php");
    
    $connection=mysql_connect($hostName,$userName,$password) or die('Could not connect to the database server');
    $db = mysql_select_db($databaseName, $connection) or die ("Unable to select database.");


    /*
     * 1 Setup Queries
     */

    //1.0 New Request
    $sql['new_request'] = "SELECT count(*) as 'num' FROM work_orders WHERE submit_date > $start_date AND submit_date < $end_date";

    //1.1 All work request closed between dates
    $sql['closed'] = "SELECT count(*) as 'num' FROM work_orders WHERE complete_date > $start_date AND complete_date < $end_date";

    //1.2 Work Orders by mechanics
    $sql['by_mechanic'] = "SELECT count(*) as 'num' , m.lname, m.fname FROM work_orders AS wo LEFT  JOIN mechanics AS m ON wo.mechanic_id = m.id WHERE wo_status LIKE  'assigned' OR ( wo_status LIKE  'completed' AND ( complete_date > $start_date AND complete_date < $end_date ) ) GROUP  BY m.id";

    //1.3 Work Orders by mechanics Open
    $sql['by_mechanic_open'] = "SELECT count(*) as 'num' , m.lname, m.fname FROM work_orders AS wo LEFT  JOIN mechanics AS m ON wo.mechanic_id = m.id WHERE wo_status LIKE  'assigned'  GROUP BY m.id";

    //1.4 Work Orders by mechanics Closed
    $sql['by_mechanic_closed'] = "SELECT count(*) as 'num' , m.lname, m.fname FROM work_orders AS wo LEFT  JOIN mechanics AS m ON wo.mechanic_id = m.id WHERE  wo_status LIKE  'completed' AND ( complete_date BETWEEN '$start_date' AND  '$end_date' )  GROUP  BY m.id";

    
    /*
     * 2 Execute Queries
     */

    $results = array();
    foreach($sql AS $key => $value)
    {
      //debug
      //echo "$key: $value<br>";

      $results[$key] = mysql_query($value, $connection) or die("Query failed:<br>$value");

      ${$key} = array();

      if(mysql_num_rows($results[$key]) > 1)
	{
	     
	  while($row[$key] = mysql_fetch_object($results[$key])) 
	    {
	      array_push(${$key}, $row[$key]);

	    }
	}
      elseif(mysql_num_rows($results[$key]) ==1)
	{

	 
	  $$key = mysql_fetch_object($results[$key]);
	}
    }
   


    /*
     * 3 Display the results
     */
   
    }
?>

<table>
  <tr>
    <th>New Request</th>
    <td><?=$new_request->num?></td>
  </tr>
</table>
<table>
  <tr>
    <th colspan="2">Open Work Orders</th>
<?
foreach($by_mechanic_open as $mechanic)
{
  if(!empty($mechanic->lname)){echo "<tr><td /><td>" . ucfirst($mechanic->lname) .", " . ucfirst($mechanic->fname) . "</td><td>$mechanic->num</td></tr>";}
}
?>
   </tr>
</table>

<table>
  <tr>
    <th colspan="2">Closed Work Orders</th>
<?
foreach($by_mechanic_closed as $mechanic)
{
  if(!empty($mechanic->lname)){echo "<tr><td /><td>" . ucfirst($mechanic->lname) .", " . ucfirst($mechanic->fname) . "</td><td>$mechanic->num</td></tr>";}
}
?>
   </tr>
</table>

<?PHP 
/*
 * 4 Inventory Management Placeholder
 */
echo "<h2>Inventory Management</h2>";
echo "<p>Feature under development: Track and manage inventory across depots.</p>";

/*
 * 5 Reporting Placeholder
 */
echo "<h2>Reporting</h2>";
echo "<p>Feature under development: Generate detailed reports for logistics and maintenance.</p>";
?>
</body>
</html>

