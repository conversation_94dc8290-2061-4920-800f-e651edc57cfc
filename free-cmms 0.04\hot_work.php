<?PHP 
include('config.inc.php');
session_save_path($session_save_path);
session_start(); ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>

<head>
  
    <meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
  
   <title>Hot Work Record</title>
 
    <!-- Include style sheet to make web form similar to paper form --> 

    <style type="text/css">

    <?PHP
        require("./libraries/browser.inc.php");
        require("./styles/dynamic_css.php");
        css_site("input.css");
    ?>

    </style> 

</head>

<body>

    <h1>Maintenance Hot Work Job Record<br></h1>

    <?PHP
      {
	include("config.inc.php");
	include("./libraries/fill_select.inc.php");


	/*we will be using the users 'group' to customize the user experience*/
	/*the group is set at login*/
	$group = $_SESSION["group"];

	/*This function returns a work order database result*/
	function get_hot_job($wo_id, $connection )
	  {
	    /* make an array for query results*/
	    $arr_wo = array();


	    /*construct the query SQL statement*/
	    $sql = "SELECT * FROM trouble_calls WHERE hj_id = $hj_id";


	    /*execute query*/
	    $sql_result = mysql_query($sql,$connection) or die ("Could not execute query: <BR> $sql");
  

	    /*The result will be returned */
	    $work_order = mysql_fetch_object($sql_result) or die("There is no work order #$hj_id </form></table></body></html>");
  
	    return $hot_job;
	  }


	/*If the user is not logged in prompt user to log in and die*/
	if(empty($group))
	  {
	    echo "<h2>Please log in to proceed</h2>";
	    echo "<form action=\"auth.php\" method=\"POST\" class=\"form\">\n";
	    echo "Login:<br><input type=\"text\" name=\"uid\" class=\"input\" size=\"10\"><br>\n";
	    echo "Password:<br><input type=\"password\" name=\"passwd\" class=\"input\" size=\"10\"><br><br>\n";
	    echo "<input type=\"submit\" value=\"Login\"></form></html>\n";

	    exit;
	  }


	/*Establish connectino to the database*/
	$connection=mysql_connect($hostName,$userName,$password) or die('Could not connect to the database server');
	$db = mysql_select_db($databaseName, $connection) or die ("Unable to select database.");


	/* If a hj_id was passed to the script we want to retrieve that hj information
	 * otherwise get the next hj number
	 * @TODO lock tables to make concurrent use safe*/
	if(isset($hj_id))
	  {

	    /*get the hot job information*/
	    $hj=get_hot_job($hj_id, $connection);
	    
	  }
	else 
	  {
 
	    $sql = "SELECT MAX(hj_id)+1 as hj_id FROM trouble_calls";

	    $sql_results = mysql_query($sql, $connection) or die("Could not execute query: <br> $sql");

	    $hj = mysql_fetch_object($sql_results) or die("There was a problem determining the next work order id");
	    $new_hj = TRUE;
  
	  }

	/*construct the input form*/

	?>

	    <!-- a little javascript function to open up the equipment selection menu -->
	    <script type="text/javascript">
	       function openwindow()
	      {

		window.open("./equip_tree.php", "equp_window", "toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,titlebar=no,copyhistory=yes,width=300,height=575");
	      }
	    </script>

	    <TABLE WIDTH="600">
	        <form action="save_hj.php" method="get" class="form" name="hot_job">

		<TR>

		    <TD ALIGN="RIGHT">Hot Job Number:</td>
		    <td><span class="input"><?=$hj->hj_id?></span><input type="hidden" name="hj_id" value="<?=$hj->hj_id?>"></td>

		</tr>
		
		<TR>

		    <TD ALIGN="Right">Short Description</td>

		    <td><input type="text" maxlength="50" size="50" name="descriptive_text" value = "<?=$hj->short_description?>"></td>

		</tr>

		<TR>
		
		    <TD ALIGN="RIGHT" VALIGN="TOP">Full Description:</td>

		    <td><textarea class="textarea" name="description" cols="60" rows="10"><?=$hj->description?></textarea></td>

		</tr>
 
		<TR>
		
		    <TD ALIGN="RIGHT"> Type of Equipment/Area:</td>

		    <td><input disable="disable" readonly="readonly" type="text" class="input" name="equipment" value="<?=$hj->equipment"> <a  href="javascript://" onClick="openwindow()">list</a></td>

		</tr>

                    <TD ALIGN="RIGHT">

                    <?PHP
                       
                        if($new_hj)
			  {
			    $sub_date = date("Y-m-d");
			  }
			else
			  {$sub_date = $hj->date;
			  }
	             ?>


                <TR>
		    <td>Date:</td>

		    <td><input type="text" class="input" name="submit_date" value="$sub_date" ><span class="minitx"> yyyy-mm-dd</sapn></td>

		</tr>

		<TR>
		   
		   <TD ALIGN="RIGHT">Hours:</td>

		   <td><input type="text" class="input" name="hours" value="<?=$hj->hours?>"></td>

	       </tr>

	       <TR>

		   <TD ALIGN="RIGHT">Craftsman/Mechanic:$hj->mechanic</td>

		   <td>

		   <?PHP

		       $combo = create_combo($databaseName, 'mechanics', 'mechanic_id', $hj->mechanic_id , "unassigned");
 	               echo $combo;
	           ?>
			 
		   </td>
		   
               </tr>

	       <tr>

	           <td><br><input type="submit" class="submit" value="Save"></td>
   
              </tr> 

	      </form>

          </table>
      
      </body>

</html>


