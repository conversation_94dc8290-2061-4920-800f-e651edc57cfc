<?PHP 
include("./config.inc.php");
session_save_path($session_save_path);
session_start();
include("./libraries/browser.inc.php"); //detect the browser
include("./styles/dynamic_css.php");    //set up font sizes, etc per browser
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>
<head>

    <meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
  
    <title>Personnel</title>
 
	<!-- Include style sheet to make web form similar to paper form --> 


<?PHP 

css_site("nicetable.css");               //customize the style sheet for the current browser and platform
                        
?>



<?PHP

include("./libraries/Table.php"); //this is the class for HTML tables

/*Connect to the database */  
$connection=mysql_connect("$hostName","$userName","$password") or die('Could not connect to the database server');
$db = mysql_select_db("$databaseName", $connection) or die ("Unable to select database.");

if(isset($_POST['edit_mech']))
{
  update_mech($connection);
}
elseif(isset($_POST['new_mech']))
{
  new_mech($connection);
}

$sql = 'SELECT * FROM mechanics';

if(isset($_GET['order_by']))
{
  $sql .= ' ORDER BY ' . $_GET['order_by'];
}

$results = mysql_query($sql, $connection) or die('Query Execution Failed<br>$sql');

$mechanics = array();

while($mechanic = mysql_fetch_array($results, MYSQL_NUM))  //store the resulting records as an array
{
  array_push($mechanics, $mechanic);
}

$table_prop = array('border'     => '0', 
		    'cellpadding'=> '5'); //attributes for the table

$mech_table = new HTML_Table($table_prop);

$arr_head_attr = array(''=>''); //header attributes

$headers = array('id'=>'Id', 'lname'=>'Last Name', 'fname'=>'First Name', 'shift'=>'Shift', 'craft'=>'Craft', 'active'=>'Active', 'busy'=>'Busy');
$keys = array_keys($headers);
$columns = array();

foreach($keys as $key)
{
  array_push($columns, "<a href=" . $_SERVER['PHP_SELF'] . "?order_by=" . urlencode($key) . ">" .$headers["$key"] . "</a>");     
}

$mech_table->addRow($columns,$row_attr, 'TH'); //add the column names as header cells to the table


$test = TRUE;
$i    = 1;

foreach($mechanics as $mech)
{

  if($mech[5] == 1)
    {

      $active_chk = 'checked = "checked"';
    }
  else
    {
      $active_chk = '';
    }

  if($mech[6] == 1)
    {
      $busy_chk = 'checked = "checked"';
    }
  else
    {
      $busy_chk = '';
    }

  $mech[5] = "<input type=\"checkbox\" name=\"active[]\" $active_chk value=\"$mech[0]\">";
  $mech[6] = "<input type=\"checkbox\" name=\"busy[]\" $busy_chk  value=\"$mech[0]\">";
  $mech[0] = "<input type=\"hidden\" name=\"id[]\" value=\"$mech[0]\">$mech[0]";
  $mech[1] = "<input type=\"text\" name=\"lname[]\" value=\"$mech[1]\">";
  $mech[2] = "<input type=\"text\" name=\"fname[]\" value=\"$mech[2]\">";
  $mech[3] = "<input size=\"1\" type=\"text\" name=\"shift[]\" value=\"$mech[3]\">";
  $mech[4] = "<input type=\"text\" name=\"craft[]\" value=\"$mech[4]\">";

$test = !$test; 

$bg = ($test) ? "#CCCCCC" : "#DDDDDD"; //set the background color of the row

//a bit of javascript to highlight rows as they are moused over and clicked upon
  $row_attr = array();   

  $row = $mech_table->addRow($mech, $row_attr, 'TD', TRUE);

  $cell_attr = array('bgcolor' => "$bg",
		     'valign'  => 'top');

  $mech_table->setRowAttributes($row, $cell_attr, FALSE);
}
$html_table = $mech_table->toHtml();

?>
<body>

<h1>Edit Personnel</h1>

<form action="<?=$_SERVER['PHP_SELF']?>" method="post">

    <?=$html_table?>

    <input type="submit" value="Save Changes" name="edit_mech">

</form>

<hr>

<h1>Add a New Mechanic</h1>

<form action="<?=$_SERVER['PHP_SELF']?>" method="post">

    <table>

        <tr>
            <td>Last Name</td>
            <td>First Name</td>
            <td>Shift</td>
            <td>Craft</td>
            <td>Active</td>
        </tr>
 
        <tr>
            <td><input type="text" name="lname"></td>
            <td><input type="text" name="fname"></td>
            <td><input size="1" type="text" name="shift" ></td>
            <td><input type="text" name="craft"></td>
        </tr>

        <tr>
            <td colspan="2"><input type="submit" name="new_mech" value="Create a New Mechanic"></td>
        </tr>

    </table>

</form>

</body>

</html>

<?PHP


function update_mech($connection)
{

  for($i = 0; $i < count($_POST["id"]); $i++)
  {

    $mech_id = $_POST['id'][$i];

   if(array_search($mech_id, $_POST["busy"]) === FALSE)
      {
	
	$busy = 0;
      }
    else
      {
	$busy = 1;
      }

    if(array_search($mech_id, $_POST["active"]) === FALSE)  
      {
	$active = 0;
      }
    else
      {
	$active = 1;
      }

    $sql = "UPDATE mechanics SET id     =  "  . $_POST["id"][$i] . 
                                  ", lname   = '" . $_POST["lname"][$i] . 
                                  "', fname   = '" . $_POST["fname"][$i] . 
                                  "', shift  =  " . $_POST["shift"][$i] . 
                                  ", craft  = '" . $_POST["craft"][$i] . 
                                  "', active =  " . $active . 
                                  ", busy   =  " . $busy . 
                              " WHERE id = " . $_POST["id"][$i];

    mysql_query($sql, $connection) or die("<br>Query Failed:$sql</br>");
  }
}


function new_mech($connection)
{

$sql = "INSERT INTO `mechanics` ( `id` , `lname` , `fname` , `shift` , `craft` , `active` , `busy` ) 
                         VALUES ( '', '" . $_POST["lname"] . 
                                  "','" . $_POST["fname"] . 
                                  "'," . $_POST["shift"] . 
                                  ", '" . $_POST["craft"] . 
                                  "', 1, 0)"; 


    mysql_query($sql, $connection) or die("<br>Query Failed:$sql</br>");

}