<html>
 <script src="./libraries/functions.js" type="text/javascript" language="javascript"></script>
<body>
<?PHP
include("config.inc.php");
session_save_path($session_save_path);
include("./libraries/Search.php");//MySQL Search Class version 2 by <PERSON>


/*Connect to the database */  
$connection=mysql_connect($hostName,$userName,$password) or die('Could not connect to the database server');
$db = mysql_select_db($databaseName, $connection) or die ("Unable to select database.");

$search = new MysqlSearch; //create a new search object

$search->set_identifier('wo_id');

$search->set_SearchTable('work_orders');

$arr_results = $search->find($_POST['search_string']);

$num =$search->get_NumResults();




foreach($arr_results as $wo)
{
  $found .= "wo_id = $wo OR ";
}

$found = substr($found, 0, -3); //trim the last 'OR' from the SQL

$found = urlencode($found);

$link = "list.php?query_name=WO_SEARCH&found=" . $found;
?>
<script type="text/javascript">
<!--

function show_results()
{

if(<?=$num?> == 1)
{
    openwindow(<?=$arr_results[0]?>);
}

else if(<?=$num?> > 0)

{
    top.maintmain.location='<?=$link?>';
}

else

{

   top.maintmain.location='empty.php?q=<?PHP echo strip_tags($search_string)?>';


}


}
//-->
setTimeout("show_results();", 500);
</script>
</body>
</html>

