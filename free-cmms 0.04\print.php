<?PHP
include('config.inc.php');
include ('./libraries/ezpdf/class.ezpdf.php');
error_reporting('E_ALL');
session_save_path($session_save_path);
/*Connect to the database */  

$connection=mysql_connect($hostName,$userName,$password) or die('Could not connect to the database server');
$db = mysql_select_db($databaseName, $connection) or die ("Unable to select database.");

$sql = "SELECT wo. * , m.lname, m.fname, p.description AS priority FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id INNER JOIN priority AS p ON wo.priority = p.priority WHERE wo.wo_id = " . $_REQUEST['wo_id'];

$sql_result = mysql_query($sql,$connection) or die ("Query failed: <br> $sql");

$wo = mysql_fetch_object($sql_result);

$bs = 10; //padding between a rectangle and its text

$pdf = new Cezpdf('LETTER');
$pdf->selectFont('./libraries/ezpdf/fonts/Helvetica.afm');
$pdf->ezSetMargins(38,38,38,38);
$pdf->ezText('WO# ' . $wo->wo_id, 20 ,array('justification'=>'right'));
$pdf->ezText('Work Order', 32 ,array('justification'=>'center','leading'=> 50));

//Administrative info box
$pdf->rectangle(38,580,536,80);
$pdf->addText(48,640, 12, 'Priority: <c:uline>' . $wo->priority . '</c:uline>');
$pdf->addText(225,640, 12, 'Status: <c:uline>' . $wo->wo_status . '</c:uline>');
$pdf->addText(355,640, 12, 'External Reference #: <c:uline>' . $wo->ref_no . '</c:uline>');
$pdf->addText(48,615, 12, 'Requested By: <c:uline>' . $wo->requestor . '</c:uline>');
$pdf->addText(306,615, 12, 'Approved By: <c:uline>' . $wo->approval . '</c:uline>');
$pdf->addText(48,590, 12, 'Date Requested: <c:uline>' . $wo->submit_date . '</c:uline>');
$pdf->addText(306,590, 12, 'Date Needed: <c:uline>' . $wo->needed_date . '</c:uline>');

//Job Description Box
$pdf->rectangle(38,100,248,460);
//$pdf->setLineStyle(1);
$pdf->line(38,532,286,532);
$pdf->addText(110, 540, 18, '<b>Job Details</b>');
$pdf->ezSetY(532);
$pdf->ezText('<b>Equipment:</b> ' . $wo->equipment, 12,array('leading'=>16, 'justification'=>'left','aleft'=>40,'aright'=>276));
$pdf->ezSetY(500);
$pdf->ezText('<b>Coordinating Instructions:</b>', 12,array('leading'=>20, 'justification'=>'left','aleft'=>40,'aright'=>276));
$pdf->ezText($wo->coordinating_instructions ,12,array('justification'=>'full','aleft'=>48,'aright'=>276));
$pdf->ezSetY(350);
$pdf->ezText('<b>Work Description:</b>', 12,array('leading'=>20, 'justification'=>'left','aleft'=>40,'aright'=>276));
$pdf->ezText($wo->description ,12,array('justification'=>'full','aleft'=>48,'aright'=>276));

//Competion Box
$pdf->rectangle(326,100,248,460);
$pdf->line(326,532,574,532);
$pdf->addText(400, 540, 18, '<b>Completion</b>');
$pdf->ezSetY(532);
$pdf->ezText('<b>Craftsman:</b> ' . $wo->lname . " " . $wo->fname, 12,array('leading'=>24, 'justification'=>'left','aleft'=>328,'aright'=>564));

$pdf->ezText('<b>Hours:</b> ' . $wo->hours, 12,array('leading'=>24, 'justification'=>'left','aleft'=>328,'aright'=>564));
$pdf->ezText('<b>Complete Date:</b> ' . $wo->complete_date, 12,array('leading'=>24, 'justification'=>'left','aleft'=>328,'aright'=>564));
$pdf->ezText('<b>Action Taken:</b>', 12,array('leading'=>24, 'justification'=>'left','aleft'=>328,'aright'=>564));
$pdf->ezText($wo->action, 12,array('justification'=>'left','aleft'=>336,'aright'=>556));
$pdf->line(326,130,574,130);
$pdf->ezSetY(135);
$pdf->ezText('<b>Inspected By:</b> ' . $wo->inspected_by, 12,array('leading'=>24, 'justification'=>'left','aleft'=>328,'aright'=>564));

//Notes Box
$pdf->addText(38,80,12, '<b>Notes:</b>');
$pdf->ezStream();


?>
