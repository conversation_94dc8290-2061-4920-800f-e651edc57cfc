<style TYPE="text/css" TITLE="2D Input" MEDIA=all> 
body {
    font-family: arial, helvetica, geneva, sans-serif;
    font-size: <?PHP echo $font_smaller; ?>;
  /*background-color:  #000066;*/
    color: #333333;

}

#centerbox
{
	width: 20em;
        height: 14em;
        position: absolute;
        top: 30%;
        left: 25%;
	border: 1px dashed #000066;
        overflow: auto;

    }
    
#centerBox>form
{
	align: center;
}


pre, tt         {font_size: <?PHP echo $font_smaller; ?>}

form            
{
	font-family: arial, helvetica, geneva, sans-serif;
	font-size: <?PHP echo $font_smaller; ?>
}

input[type=text]       
{
	font-family: arial, helvetica, geneva, sans-serif; 
	font-size: <?PHP echo $font_smaller; ?>;

	background-color: transparent;
}

input.textfield, select, textarea
{
	font-family: arial, helvetica, geneva, sans-serif; 
	font-size: <?PHP echo $font_smaller; ?>;
	color: #000000; 
border: 1px solid black ;
	background-color: #FFFFFF
}

a.nav:link      
{
	font-family: arial, helvetica, geneva, sans-serif; 
	color: #000000
}

a.nav:visited   
{
	font-family: arial, helvetica, geneva, sans-serif; 
	color: #000000.
	text-decoration: none;
}

a.nav:hover     
{
	font-family: arial, helvetica, geneva, sans-serif; 
	color: #FF0000
	
}

td.topline      {font-size: 1px}

td              
{
	font-family: arial, helvetica, geneva, sans-serif; 
	font-size: <?PHP echo $font_smaller; ?>;
	color: #333344;

}

th              
{
	font-family: arial, helvetica, geneva, sans-serif;
	font-size: x-small;
	font-weight: bold;
	color: #000000;
	background-color: #D3DCE3;
 /*	background-color: #ABABAB;*/
	padding: 1px, 1px, 1px, 1px;
	margin: 1px, 1px, 1px, 1px;
	-moz-border-radius-topleft: 5px;
	-moz-border-radius-topright: 5px;
}
	
a:link
{
	text-decoration: none;
}

a:visited
{
	text-decoration: none;
}

.print
{
	font-family:arial;
	font-size:8pt;
}

.logo {
    background-image: url('../images/new_logo.png');
}

</style>