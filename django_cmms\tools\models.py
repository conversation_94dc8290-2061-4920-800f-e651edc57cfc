"""
Tools and Equipment Management models for Naval CMMS application.
Based on analysis of NAVT ATTRIBUTES ENTITIES document - Entity 4: Tools and Equipment.
"""
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.conf import settings
from datetime import date, timedelta
from decimal import Decimal


class ToolCategory(models.Model):
    """
    Categories for organizing tools and equipment.
    """
    name = models.CharField(
        max_length=100,
        help_text="Category name"
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Category code"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Category description"
    )
    
    requires_calibration = models.BooleanField(
        default=False,
        help_text="Whether tools in this category require calibration"
    )
    
    default_calibration_frequency_days = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Default calibration frequency in days"
    )
    
    safety_requirements = models.TextField(
        blank=True,
        help_text="Safety requirements for this category"
    )
    
    is_active = models.<PERSON><PERSON>an<PERSON>ield(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'tools_tool_category'
        ordering = ['name']
        verbose_name_plural = 'Tool Categories'
        
    def __str__(self):
        return f"{self.code} - {self.name}"


class Tool(models.Model):
    """
    Tools and equipment with calibration and usage tracking.
    """
    TOOL_TYPES = [
        ('mechanical', 'Mechanical Tool'),
        ('electrical', 'Electrical Tool'),
        ('electronic', 'Electronic Tool'),
        ('measuring', 'Measuring Instrument'),
        ('cutting', 'Cutting Tool'),
        ('lifting', 'Lifting Equipment'),
        ('safety', 'Safety Equipment'),
        ('diagnostic', 'Diagnostic Equipment'),
        ('calibration', 'Calibration Equipment'),
        ('specialized', 'Specialized Tool'),
    ]
    
    CONDITION_CHOICES = [
        ('excellent', 'Excellent'),
        ('good', 'Good'),
        ('fair', 'Fair'),
        ('poor', 'Poor'),
        ('out_of_service', 'Out of Service'),
        ('under_repair', 'Under Repair'),
        ('calibration_due', 'Calibration Due'),
        ('condemned', 'Condemned'),
    ]
    
    STATUS_CHOICES = [
        ('available', 'Available'),
        ('in_use', 'In Use'),
        ('checked_out', 'Checked Out'),
        ('maintenance', 'Under Maintenance'),
        ('calibration', 'Under Calibration'),
        ('repair', 'Under Repair'),
        ('lost', 'Lost'),
        ('disposed', 'Disposed'),
    ]
    
    # Basic Information
    tool_name = models.CharField(
        max_length=200,
        help_text="Tool name"
    )
    
    tool_serial_number = models.CharField(
        max_length=100,
        unique=True,
        help_text="Unique tool serial number"
    )
    
    part_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="Part number"
    )
    
    category = models.ForeignKey(
        ToolCategory,
        on_delete=models.PROTECT,
        related_name='tools',
        help_text="Tool category"
    )
    
    tool_type = models.CharField(
        max_length=20,
        choices=TOOL_TYPES,
        help_text="Type of tool"
    )
    
    # Specifications
    manufacturer = models.CharField(
        max_length=200,
        blank=True,
        help_text="Tool manufacturer"
    )
    
    model_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="Model number"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Tool description and specifications"
    )
    
    # Physical Properties
    weight_kg = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Weight in kilograms"
    )
    
    dimensions = models.CharField(
        max_length=100,
        blank=True,
        help_text="Dimensions (L x W x H)"
    )
    
    # Status and Condition
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='available',
        help_text="Current status"
    )
    
    condition = models.CharField(
        max_length=20,
        choices=CONDITION_CHOICES,
        default='good',
        help_text="Current condition"
    )
    
    # Assignment and Location
    assigned_personnel = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='assigned_tools',
        help_text="Personnel responsible for this tool"
    )
    
    storage_location = models.ForeignKey(
        'equipment.Location',
        on_delete=models.PROTECT,
        related_name='stored_tools',
        help_text="Storage location"
    )
    
    current_location = models.ForeignKey(
        'equipment.Location',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='current_tools',
        help_text="Current location"
    )
    
    # Calibration Information
    requires_calibration = models.BooleanField(
        default=False,
        help_text="Whether tool requires calibration"
    )
    
    calibration_frequency_days = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Calibration frequency in days"
    )
    
    last_calibration_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of last calibration"
    )
    
    next_calibration_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of next calibration"
    )
    
    calibration_certificate = models.CharField(
        max_length=100,
        blank=True,
        help_text="Calibration certificate number"
    )
    
    calibration_agency = models.CharField(
        max_length=200,
        blank=True,
        help_text="Calibration agency or provider"
    )
    
    # Maintenance Information
    last_service_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of last service/maintenance"
    )
    
    next_service_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of next service"
    )
    
    service_frequency_days = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Service frequency in days"
    )
    
    # Purchase and Warranty
    purchase_date = models.DateField(
        blank=True,
        null=True,
        help_text="Purchase date"
    )
    
    purchase_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Purchase cost"
    )
    
    supplier = models.ForeignKey(
        'inventory.Vendor',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='supplied_tools',
        help_text="Tool supplier"
    )
    
    warranty_start_date = models.DateField(
        blank=True,
        null=True,
        help_text="Warranty start date"
    )
    
    warranty_end_date = models.DateField(
        blank=True,
        null=True,
        help_text="Warranty end date"
    )
    
    # Usage Tracking
    total_usage_hours = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Total usage hours"
    )
    
    usage_limit_hours = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Usage limit in hours"
    )
    
    # Safety and Requirements
    safety_requirements = models.TextField(
        blank=True,
        help_text="Safety requirements and precautions"
    )
    
    operating_instructions = models.TextField(
        blank=True,
        help_text="Operating instructions"
    )
    
    storage_requirements = models.TextField(
        blank=True,
        help_text="Storage requirements"
    )
    
    # Additional Information
    notes = models.TextField(
        blank=True,
        help_text="Additional notes"
    )
    
    qr_code = models.CharField(
        max_length=100,
        blank=True,
        help_text="QR code for quick identification"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'tools_tool'
        ordering = ['tool_name']
        
    def __str__(self):
        return f"{self.tool_serial_number} - {self.tool_name}"
    
    def get_absolute_url(self):
        return reverse('tools:tool_detail', kwargs={'pk': self.pk})
    
    @property
    def is_calibration_due(self):
        """Check if calibration is due."""
        if not self.requires_calibration or not self.next_calibration_date:
            return False
        return self.next_calibration_date <= date.today()
    
    @property
    def is_calibration_overdue(self):
        """Check if calibration is overdue."""
        if not self.requires_calibration or not self.next_calibration_date:
            return False
        return self.next_calibration_date < date.today()
    
    @property
    def days_until_calibration(self):
        """Calculate days until next calibration."""
        if not self.next_calibration_date:
            return None
        return (self.next_calibration_date - date.today()).days
    
    @property
    def is_service_due(self):
        """Check if service is due."""
        if not self.next_service_date:
            return False
        return self.next_service_date <= date.today()
    
    @property
    def is_warranty_valid(self):
        """Check if warranty is still valid."""
        if not self.warranty_end_date:
            return False
        return self.warranty_end_date >= date.today()
    
    @property
    def usage_percentage(self):
        """Calculate usage percentage if limit is set."""
        if not self.usage_limit_hours or self.usage_limit_hours == 0:
            return None
        return (self.total_usage_hours / self.usage_limit_hours) * 100
    
    def calculate_next_calibration_date(self):
        """Calculate next calibration date."""
        if self.requires_calibration and self.calibration_frequency_days and self.last_calibration_date:
            return self.last_calibration_date + timedelta(days=self.calibration_frequency_days)
        return None
    
    def calculate_next_service_date(self):
        """Calculate next service date."""
        if self.service_frequency_days and self.last_service_date:
            return self.last_service_date + timedelta(days=self.service_frequency_days)
        return None


class ToolUsageLog(models.Model):
    """
    Tool usage tracking and history.
    """
    tool = models.ForeignKey(
        Tool,
        on_delete=models.CASCADE,
        related_name='usage_logs',
        help_text="Tool being used"
    )
    
    used_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='tool_usage',
        help_text="Person using the tool"
    )
    
    work_order = models.ForeignKey(
        'work_orders.WorkOrder',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='tool_usage',
        help_text="Related work order"
    )
    
    task_description = models.TextField(
        help_text="Description of task performed"
    )
    
    # Usage Period
    start_time = models.DateTimeField(
        help_text="Usage start time"
    )
    
    end_time = models.DateTimeField(
        blank=True,
        null=True,
        help_text="Usage end time"
    )
    
    usage_hours = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Total usage hours"
    )
    
    # Location and Conditions
    usage_location = models.ForeignKey(
        'equipment.Location',
        on_delete=models.PROTECT,
        related_name='tool_usage',
        help_text="Location where tool was used"
    )
    
    environmental_conditions = models.TextField(
        blank=True,
        help_text="Environmental conditions during use"
    )
    
    # Tool Condition
    condition_before = models.CharField(
        max_length=20,
        choices=Tool.CONDITION_CHOICES,
        help_text="Tool condition before use"
    )
    
    condition_after = models.CharField(
        max_length=20,
        choices=Tool.CONDITION_CHOICES,
        help_text="Tool condition after use"
    )
    
    # Issues and Observations
    issues_encountered = models.TextField(
        blank=True,
        help_text="Any issues encountered during use"
    )
    
    maintenance_required = models.BooleanField(
        default=False,
        help_text="Whether maintenance is required"
    )
    
    calibration_check_performed = models.BooleanField(
        default=False,
        help_text="Whether calibration check was performed"
    )
    
    calibration_within_tolerance = models.BooleanField(
        blank=True,
        null=True,
        help_text="Whether calibration was within tolerance"
    )
    
    # Additional Information
    remarks = models.TextField(
        blank=True,
        help_text="Additional remarks"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'tools_tool_usage_log'
        ordering = ['-start_time']
        
    def __str__(self):
        return f"{self.tool.tool_name} - {self.used_by.username} - {self.start_time}"
    
    def save(self, *args, **kwargs):
        """Calculate usage hours on save."""
        if self.start_time and self.end_time:
            duration = self.end_time - self.start_time
            self.usage_hours = duration.total_seconds() / 3600
        super().save(*args, **kwargs)


class ToolCalibration(models.Model):
    """
    Tool calibration records and certificates.
    """
    CALIBRATION_RESULTS = [
        ('pass', 'Pass'),
        ('fail', 'Fail'),
        ('limited_pass', 'Limited Pass'),
        ('adjustment_made', 'Adjustment Made'),
        ('out_of_tolerance', 'Out of Tolerance'),
    ]
    
    tool = models.ForeignKey(
        Tool,
        on_delete=models.CASCADE,
        related_name='calibrations',
        help_text="Tool being calibrated"
    )
    
    calibration_date = models.DateField(
        help_text="Calibration date"
    )
    
    calibrated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='performed_calibrations',
        help_text="Person who performed calibration"
    )
    
    calibration_agency = models.CharField(
        max_length=200,
        help_text="Calibration agency or department"
    )
    
    certificate_number = models.CharField(
        max_length=100,
        help_text="Calibration certificate number"
    )
    
    # Calibration Results
    result = models.CharField(
        max_length=20,
        choices=CALIBRATION_RESULTS,
        help_text="Calibration result"
    )
    
    accuracy_achieved = models.CharField(
        max_length=100,
        blank=True,
        help_text="Accuracy achieved"
    )
    
    tolerance_met = models.BooleanField(
        default=True,
        help_text="Whether tolerance requirements were met"
    )
    
    # Standards and Procedures
    calibration_standard = models.CharField(
        max_length=200,
        blank=True,
        help_text="Calibration standard used"
    )
    
    procedure_reference = models.CharField(
        max_length=100,
        blank=True,
        help_text="Calibration procedure reference"
    )
    
    # Environmental Conditions
    temperature = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Temperature during calibration (°C)"
    )
    
    humidity = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Humidity during calibration (%)"
    )
    
    # Validity
    valid_until = models.DateField(
        help_text="Calibration valid until"
    )
    
    next_calibration_due = models.DateField(
        help_text="Next calibration due date"
    )
    
    # Documentation
    calibration_data = models.JSONField(
        default=dict,
        blank=True,
        help_text="Detailed calibration data as JSON"
    )
    
    certificate_file = models.FileField(
        upload_to='tools/calibration_certificates/',
        blank=True,
        null=True,
        help_text="Calibration certificate file"
    )
    
    remarks = models.TextField(
        blank=True,
        help_text="Calibration remarks"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'tools_tool_calibration'
        ordering = ['-calibration_date']
        
    def __str__(self):
        return f"{self.tool.tool_name} - {self.calibration_date} - {self.result}"
    
    @property
    def is_expired(self):
        """Check if calibration is expired."""
        return self.valid_until < date.today()
    
    @property
    def days_until_expiry(self):
        """Calculate days until calibration expires."""
        return (self.valid_until - date.today()).days


class ToolMaintenance(models.Model):
    """
    Tool maintenance and service records.
    """
    MAINTENANCE_TYPES = [
        ('routine', 'Routine Maintenance'),
        ('preventive', 'Preventive Maintenance'),
        ('corrective', 'Corrective Maintenance'),
        ('repair', 'Repair'),
        ('overhaul', 'Overhaul'),
        ('inspection', 'Inspection'),
        ('cleaning', 'Cleaning'),
        ('lubrication', 'Lubrication'),
    ]
    
    tool = models.ForeignKey(
        Tool,
        on_delete=models.CASCADE,
        related_name='maintenance_records',
        help_text="Tool being maintained"
    )
    
    maintenance_type = models.CharField(
        max_length=20,
        choices=MAINTENANCE_TYPES,
        help_text="Type of maintenance"
    )
    
    maintenance_date = models.DateField(
        help_text="Maintenance date"
    )
    
    performed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='tool_maintenance',
        help_text="Person who performed maintenance"
    )
    
    description = models.TextField(
        help_text="Maintenance description"
    )
    
    parts_replaced = models.TextField(
        blank=True,
        help_text="Parts replaced during maintenance"
    )
    
    materials_used = models.TextField(
        blank=True,
        help_text="Materials and consumables used"
    )
    
    cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Maintenance cost"
    )
    
    duration_hours = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Maintenance duration in hours"
    )
    
    condition_before = models.CharField(
        max_length=20,
        choices=Tool.CONDITION_CHOICES,
        help_text="Tool condition before maintenance"
    )
    
    condition_after = models.CharField(
        max_length=20,
        choices=Tool.CONDITION_CHOICES,
        help_text="Tool condition after maintenance"
    )
    
    next_maintenance_due = models.DateField(
        blank=True,
        null=True,
        help_text="Next maintenance due date"
    )
    
    work_order = models.ForeignKey(
        'work_orders.WorkOrder',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='tool_maintenance',
        help_text="Related work order"
    )
    
    remarks = models.TextField(
        blank=True,
        help_text="Maintenance remarks"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'tools_tool_maintenance'
        ordering = ['-maintenance_date']
        
    def __str__(self):
        return f"{self.tool.tool_name} - {self.maintenance_type} - {self.maintenance_date}"


class ToolCheckout(models.Model):
    """
    Tool checkout and return tracking.
    """
    STATUS_CHOICES = [
        ('checked_out', 'Checked Out'),
        ('returned', 'Returned'),
        ('overdue', 'Overdue'),
        ('lost', 'Lost'),
        ('damaged', 'Damaged'),
    ]
    
    tool = models.ForeignKey(
        Tool,
        on_delete=models.CASCADE,
        related_name='checkouts',
        help_text="Tool being checked out"
    )
    
    checked_out_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='tool_checkouts',
        help_text="Person checking out the tool"
    )
    
    checked_out_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='received_tools',
        help_text="Person receiving the tool"
    )
    
    checkout_date = models.DateTimeField(
        default=timezone.now,
        help_text="Checkout date and time"
    )
    
    expected_return_date = models.DateField(
        help_text="Expected return date"
    )
    
    actual_return_date = models.DateTimeField(
        blank=True,
        null=True,
        help_text="Actual return date and time"
    )
    
    returned_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='tool_returns',
        help_text="Person who returned the tool"
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='checked_out',
        help_text="Checkout status"
    )
    
    purpose = models.TextField(
        help_text="Purpose of checkout"
    )
    
    work_order = models.ForeignKey(
        'work_orders.WorkOrder',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='tool_checkouts',
        help_text="Related work order"
    )
    
    condition_at_checkout = models.CharField(
        max_length=20,
        choices=Tool.CONDITION_CHOICES,
        help_text="Tool condition at checkout"
    )
    
    condition_at_return = models.CharField(
        max_length=20,
        choices=Tool.CONDITION_CHOICES,
        blank=True,
        help_text="Tool condition at return"
    )
    
    return_notes = models.TextField(
        blank=True,
        help_text="Notes about tool condition at return"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'tools_tool_checkout'
        ordering = ['-checkout_date']
        
    def __str__(self):
        return f"{self.tool.tool_name} - {self.checked_out_to.username} - {self.checkout_date}"
    
    @property
    def is_overdue(self):
        """Check if tool return is overdue."""
        if self.status == 'returned':
            return False
        return self.expected_return_date < date.today()
    
    @property
    def days_overdue(self):
        """Calculate days overdue."""
        if not self.is_overdue:
            return 0
        return (date.today() - self.expected_return_date).days
