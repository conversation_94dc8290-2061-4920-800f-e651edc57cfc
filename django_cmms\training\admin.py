from django.contrib import admin
from .models import (
    TrainingInstitution, CourseCategory, Course, TrainingSession,
    TrainingRecord, CertificationRequirement, TrainingPlan, TrainingPlanItem
)


@admin.register(TrainingInstitution)
class TrainingInstitutionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'institution_type', 'location', 'is_active']
    list_filter = ['institution_type', 'is_active']
    search_fields = ['name', 'code', 'location']


@admin.register(CourseCategory)
class CourseCategoryAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'color', 'is_active']
    list_filter = ['is_active']
    search_fields = ['name', 'code']


@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ['code', 'title', 'category', 'course_type', 'duration_hours', 'provides_certification']
    list_filter = ['category', 'course_type', 'delivery_method', 'provides_certification']
    search_fields = ['title', 'code']
    filter_horizontal = ['mandatory_for_ranks', 'mandatory_for_appointments']


@admin.register(TrainingSession)
class TrainingSessionAdmin(admin.ModelAdmin):
    list_display = ['session_id', 'course', 'institution', 'start_date', 'end_date', 'status', 'current_enrollment', 'max_participants']
    list_filter = ['status', 'institution', 'start_date']
    search_fields = ['session_id', 'course__title']
    readonly_fields = ['available_slots', 'is_full']


@admin.register(TrainingRecord)
class TrainingRecordAdmin(admin.ModelAdmin):
    list_display = ['record_id', 'personnel', 'session', 'status', 'grade', 'completion_date', 'certificate_issued']
    list_filter = ['status', 'grade', 'certificate_issued', 'completion_date']
    search_fields = ['record_id', 'personnel__user__first_name', 'personnel__user__last_name']
    readonly_fields = ['is_certified', 'is_certificate_expired']


@admin.register(CertificationRequirement)
class CertificationRequirementAdmin(admin.ModelAdmin):
    list_display = ['name', 'requirement_type', 'renewal_period_years', 'is_active']
    list_filter = ['requirement_type', 'is_active']
    search_fields = ['name']
    filter_horizontal = ['applicable_ranks', 'applicable_appointments', 'satisfying_courses']


@admin.register(TrainingPlan)
class TrainingPlanAdmin(admin.ModelAdmin):
    list_display = ['title', 'plan_type', 'personnel', 'unit', 'start_date', 'end_date', 'status']
    list_filter = ['plan_type', 'status', 'start_date']
    search_fields = ['title']
    readonly_fields = ['budget_remaining', 'budget_utilization_percentage']


@admin.register(TrainingPlanItem)
class TrainingPlanItemAdmin(admin.ModelAdmin):
    list_display = ['training_plan', 'course', 'target_start_date', 'target_completion_date', 'status', 'priority']
    list_filter = ['status', 'priority', 'target_start_date']
    search_fields = ['training_plan__title', 'course__title']
    filter_horizontal = ['target_personnel']
    readonly_fields = ['is_overdue', 'days_overdue']
