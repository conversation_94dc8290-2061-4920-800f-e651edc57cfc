<?PHP 
include("./config.inc.php");
session_save_path($session_save_path);
session_start(); 
//<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" 
    "http://www.w3.org/TR/html4/loose.dtd">


<html>

<head>
  
    <meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
  
   <title>Work Order/Request</title>
 
    <!-- Include style sheet to make web form similar to paper form --> 
<style type="text/css">
<?PHP
    require("./libraries/browser.inc.php");
    require("./styles/dynamic_css.php");
    css_site("input.css");
?>

</style> 
 
 <script src="./libraries/functions.js" type="text/javascript" language="javascript"></script>

</head>

<body onLoad="show_needed_date('hideDivL','hideDivR')">

<?PHP
  
include("config.inc.php");
include("././libraries/fill_select.inc.php");

$group = $_SESSION["group"];

if(empty($group)) //If the user is not logged in...
{ 
  $login = "<h2>You must be <a href=\"./index.php\">logged in</a> to make changes.</h2>";
}

/*Establish connection to the database*/
$connection = new mysqli($hostName, $userName, $password, $databaseName);
if ($connection->connect_error) {
    die("Connection failed: " . $connection->connect_error);
}

/* If a wo_id was passed to the script we want to retrieve that wo information
 * @TODO lock tables to make concurrent use safe
 */
if(isset($_REQUEST['wo_id']) && $_REQUEST['wo_id'] !== 'new')
{
    $stmt = $connection->prepare("SELECT * FROM work_orders WHERE wo_id = ?");
    $stmt->bind_param("i", $_REQUEST['wo_id']);
    $stmt->execute();
    $wo = $stmt->get_result()->fetch_object();
    $stmt->close();
}
else //if the work order number was not passed get the next available number
     //@TODO go back and use work order numbers that were skipped
{
 
  $sql = "SELECT id as wo_id FROM next_wo";

  $sql_results = $connection->query($sql) or die("Could not execute query: <br> $sql");

  $wo = $sql_results->fetch_object() or die("There was a problem determining the next work order id");

  $next_wo_sql = "UPDATE next_wo SET id = ($wo->wo_id + 1)";

  $connection->query($next_wo_sql) or die("Could not set the next work order:<br> $next_wo_sql");

  $new_wo = TRUE;
  
}

?>

<script type="text/javascript">

<!-- Javascript function to call the equipment list -->
function equipmentwindow(form_name, control_name)
{
    document.title = "Work Order";
    window.open("./equip_tree.php?form=" + form_name + "&control=" + control_name, "equip_window", "toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,titlebar=no,copyhistory=yes,width=300,height=575");

}

function show_needed_date(ltag, rtag){
var lobj = document.getElementById(ltag);
var robj = document.getElementById(rtag);

if(document.work_order.priority.value == 3)
{  

 lobj.style.display = '';
 robj.style.display = '';

} else {

 lobj.style.display = 'none';
 robj.style.display = 'none';
}

}
</script>

<h1 class="title">Maintenance Work Order #<?= htmlspecialchars($wo->wo_id) ?><br></h1>

<?= $login ?>

<form action="save.php" method="post" class="form" name="work_order">
    <input type="hidden" name="document" value="work_order">
    <input class="save" type="submit" value="Save & Close" name="done">
    <input type="hidden" name="wo_id" value="<?= htmlspecialchars($wo->wo_id) ?>">

    <table width="600">
        <tr>
            <td align="right" valign="top">Reference No.<br><span class="minitxt">optional</span></td>
            <td><?= ref_no_form_control() ?></td>
        </tr>
        <tr>
            <td align="right" valign="top">Short Description</td>
            <td><?= descriptive_text_form_control() ?></td>
        </tr>
        <tr>
            <td align="right">Submit Date</td>
            <td><?= submit_date_form_control() ?></td>
        </tr>
        <tr>
            <td align="right">Status:</td>
            <td><?= status_form_control() ?></td>
        </tr>
        <tr>
            <td align="right">Priority:</td>
            <td><?= priority_form_control() ?></td>
        </tr>
        <tr>
            <td align="right"><span id="hideDivL">Date Needed</span></td>
            <td><span id="hideDivR"><?= needed_date_form_control() ?></span></td>
        </tr>
        <tr>
            <td align="right">Audit Item</td>
            <td><?= audit_form_control() ?></td>
        </tr>
        <tr>
            <td align="right">Requestor</td>
            <td><?= requestor_form_control() ?></td>
        </tr>
        <tr>
            <td align="right">Equipment:</td>
            <td><?= equipment_form_control() ?></td>
        </tr>
        <tr>
            <td valign="top" align="right">Coordinating Instructions</td>
            <td><?= coordination_form_control() ?></td>
        </tr>
        <tr>
            <td valign="top" align="right">Full Description</td>
            <td><?= description_form_control() ?></td>
        </tr>
        <tr>
            <td valign="top" align="right">Action Taken</td>
            <td><?= action_taken_form_control() ?></td>
        </tr>
        <tr>
            <td align="right">Mechanic</td>
            <td><?= mechanic_form_control() ?></td>
        </tr>
        <tr>
            <td align="right">Actual Hours</td>
            <td><?= act_hours_form_control() ?></td>
        </tr>
        <tr>
            <td align="right">Inspected By</td>
            <td><?= inspector_form_control() ?></td>
        </tr>
        <tr>
            <td align="right">Date Completed</td>
            <td><?= complete_date_form_control() ?></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td>
                <input type="submit" class="submit" value="Save & Close" name="done">
                <input type="submit" class="submit" value="Save & Create Another" name="another">
            </td>
        </tr>
    </table>
</form>

</html>

<?PHP

/**
 * Gets work order information given the work order id number
 *
 * @param    int     the id number of the work order
 *           object  a mysql database connection
 *
 * @return   array   associative array of work order results 
 */
function get_work_order($wo_id, $connection )
{
  /* make an array for query results*/
  $arr_wo = array();


  /*construct the query SQL statement*/
  $sql = "SELECT * FROM work_orders WHERE wo_id = $wo_id";


  /*execute query*/
  $sql_result = $connection->query($sql) or die ("Could not execute query: <BR> $sql");
  

  /*Return the results */
  $work_order = $sql_result->fetch_object() or die("There is no work order #$wo_id </form></table></body></html>");
  
  return $work_order;
}//end of get_work_order()


/**
 * Construct the text box control for w.o. equipment and create the link for the equipment list window
 *
 * @return   string   HTML code for the equipment text box and equipment window opener
 */
function ref_no_form_control()
{

  global $wo;

  $ref_no_html = "<input type=\"text\" class=\"input\" name=\"ref_no\" value=\"$wo->ref_no\">";

  return $ref_no_html;

}
/**
 * Construct the select control for w.o. status
 *
 * @return   string   HTML code for status select box
 */
function status_form_control()
{

  global $group, $wo;

  $lock = "";

  /*Status - editable by >= lead, static for < lead*/
  if($group != "lead" && $group != "manager" && ($group != "mechanic" || !$new_wo) && ($group != "clerk"))
    {
      $lock = "disabled";
    }

  if($new_wo && $group != "manager" && $group != "lead" && $group != "mechanic" && $group != "clerk")
    {
      $status_html = "<input type=\"text\" class=\"input\" name=\"wo_status\" value=\"Pending Approval\" readonly=\"readonly\">";
    }
  else
    {
      if(!isset($wo->wo_status))
	{
	  $wo->wo_status = 'Pending Approval';
	}

      $status_html = "<select name=\"wo_status\" $lock class=\"input\"><option "; 

      if($wo->wo_status == 'Pending Approval')
	{
	  $status_html .= "selected";
	} 

      $status_html .= " value=\"Pending Approval\">Pending Approval</option> \n";
	
      if($group != "mechanic" && $group != "clerk") 
	{

          ${$wo->wo_status} = 'selected'; 

	  $status_html .= "<option $Appoved value=\"Approved\">Approved</option>\n
                <option $Assigned  value=\"Assigned\">Assigned</option>\n
                <option $Suspended value=\"Suspended\">Suspended</option>\n
                <option $Completed value=\"Completed\">Completed</option>\n
                <option $Rejected  value=\"Rejected\">Rejected</option>\n";
	}

      $status_html .= "</select>";

    }

  return $status_html;

}


/**
 * Construct the select control for w.o. priority
 *
 * @return   string   HTML code for priority select box
 */
function priority_form_control()
{

  global $group, $wo;
  $lock = "";

  if($group != 'lead' && $group != 'manager')
    {
      $lock = "disabled ";
    }
  

  $sel_var  = "selected_" . $wo->priority; //Create a string '$selected_X' where X is the priority
  $$sel_var = 'selected';                  //use the string as the name of a variable

  $priority_html .= "<select onchange=\"show_needed_date('hideDivL','hideDivR')\" $lock class=\"select\" name=\"priority\"> \n
  		            <option value=\"\">(select a priority) ...</option> \n
		            <option $selected_1 value=\"1\">Immediate/Safety</option> \n
		            <option $selected_2 value=\"2\">ASAP</option> \n
		            <option $selected_3 value=\"3\">Before specified date</option> \n
		            <option $selected_4 value=\"4\">As permitted</option> \n
		            <option $selected_5 value=\"5\">Standing work order</option> \n
	               </select>";
  return $priority_html;

    }


/**
 * Construct the form control for w.o. requestor
 *
 * @return   string   HTML code for requestor text box
 */
function requestor_form_control()
{

  global $new_wo, $wo;

  if($new_wo)
    {
      $requestor = $_SESSION['user'];
    }
  else
    {
      $requestor = $wo->requestor;
    }

  $requestor_html = "<input type=\"text\" class=\"input\" name=\"requestor\" value=\"$requestor\">";

  return $requestor_html;
}

/**
 * Construct the text box control for w.o. approver
 *
 * @return   string   HTML code for approval text box
 */
function approval_form_control()
{

  $lock = "";
  global $group, $wo;

  if($group != 'lead' && $group != 'manager')
    {
      $lock = " readonly=\"readonly\" ";
    }

  $approval_html .= "<input type=\"text\" $lock class=\"input\" name=\"approval\" value=\"$wo->approval\">";

  return $approval_html;
}


/**
 * Construct the text box control for w.o. equipment and create the link for the equipment list window
 *
 * @return   string   HTML code for the equipment text box and equipment window opener
 */
function equipment_form_control()
{

  global $wo;

  $equipment_html = "<input type=\"text\" class=\"input\" name=\"equipment\" value=\"$wo->equipment\">";// <a  href=\"javascript://\" onClick=\"equipmentwindow('work_order', 'equipment')\">list</a><input type=\"hidden\" name=\"equipment_id\" value=\"$wo->equipment_id\">";

  return $equipment_html;

}


/**
 * Construct the text box control for w.o. submit date
 *
 * @return   string   HTML code for the submit date text box
 */
function submit_date_form_control()
{

  global $wo, $new_wo;

if($new_wo)
{
$sub_date = date("m/d/Y");
}
 else
{
$sub_date = date("m/d/Y", strtotime($wo->submit_date));
}

$submit_date_html .= "<input type=\"text\" class=\"input\" name=\"submit_date\" value=\"$sub_date\" >";

return $submit_date_html;

}



/**
 * Construct the text box control for w.o. estimated man hours
 *
 * @return   string   HTML code for the est man hours text box
 */
function est_hours_form_control()
{

  global $wo;

  $est_hours_html = "<input type=\"text\" class=\"input\" name=\"est_hours\" value=\"$wo->est_hours\">";

  return $est_hours_html;
}


/**
 * Construct the text box control for w.o. coordinating instructions
 *
 * @return   string   HTML code for the coordinating instructions man hours text area
 */
function coordination_form_control()
{

  global $wo;

  $coordination_html = "<textarea class=\"textarea\" name=\"coordinating_instructions\" rows=\"5\" cols=\"60\">$wo->coordinating_instructions</textarea>";

  return $coordination_html;
   
}

/**
 * Construct the text box control for w.o. date needed
 *
 * @return   string   HTML code for the date needed text box
 */
function needed_date_form_control()
{

  global $wo;

  if(!is_null($wo->needed_date))
    {
      $n_date = date("m/d/y", strtotime($wo->needed_date));
    }
  $needed_date_html = "<input type=\"text\" class=\"input\" name=\"needed_date\" size=\"8\" value=$n_date >";

  return $needed_date_html;

}

/**
 * Construct the text area control for w.o. description
 *
 * @return   string   HTML code for the description text area
 */
function description_form_control()
{

  global $wo;

  $description_html = "<textarea class=\"textarea\" name=\"description\" cols=\"60\" rows=\"10\">$wo->description</textarea>";

  return $description_html;

}


/**
 * Construct the text area control for w.o. action taken
 *
 * @return   string   HTML code for the action taken text area
 */
function action_taken_form_control()
{

  global $wo;

  $action_taken_html = "<textarea class=\"textarea\" name=\"action\" cols=\"60\" rows=\"10\">$wo->action</textarea>";

  return $action_taken_html;

}


/**
 * Construct the select control for w.o. mechanic
 *
 * @return   string   HTML code for the mechanic select box
 */
function mechanic_form_control()
{

  global $wo, $databaseName;

  $mechanic_html = create_combo($databaseName, 'mechanics', 'mechanic_id', $wo->mechanic_id , "unassigned");

  return $mechanic_html;

}

/**
 * Construct the text box control for w.o. actual man hours
 *
 * @return   string   HTML code for the act man hours text box
 */
function act_hours_form_control()
{

  global $wo;

  $act_hours_html = "<input type=\"text\" class=\"input\" name=\"act_hours\" value=\"$wo->act_hours\">";

  return $act_hours_html;
}


/**
 * Construct the text box control for w.o. inspector
 *
 * @return   string   HTML code for the inspector name control
 */
function inspector_form_control()
{

  global $wo;

  $inspector_html = "<input type=\"text\" class=\"input\" name=\"inspected_by\" value=\"$wo->inspected_by\">";

  return $inspector_html;
}

/**
 * Construct the text box control for the short description
 *
 * @return   string   HTML code for the short description control
 */
function descriptive_text_form_control()
{

  global $wo;

  $descriptive_text_html = "<input type=\"text\" maxlength=\"50\" size=\"50\" name=\"descriptive_text\" value =\"$wo->descriptive_text\"><br><span class=\"minitxt\">Use a format like: verb - object - line. i.e. Replace conveyor belt Poly 3</span>";

  return $descriptive_text_html;
}

/**
 * Construct the text box control for w.o. complete date
 *
 * @return   string   HTML code for the complete date control
 */
function complete_date_form_control()
{

  global $wo;

    if(!is_null($wo->complete_date))
    {
      $c_date = date("m/d/y", strtotime($wo->complete_date));
    }
  $complete_date_html = "<input type=\"text\" class=\"input\" name=\"complete_date\" value=\"$c_date\">";

  return $complete_date_html;
}


/**
 * Construct the check box to indicate that this wo is associated with an audit
 *
 * @return   string   HTML code for the audit check box control
 */
function audit_form_control()
{

  global $wo;

  if($wo->audit_item)
    {
      $checked = "checked = \"checked\"";
    }

  $audit_html = "<input type=\"checkbox\" class=\"input\" name=\"audit_item\" $checked value=\"1\">";

  return $audit_html;

}
?>