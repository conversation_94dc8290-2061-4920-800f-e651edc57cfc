<?PHP 
include('config.inc.php');
session_save_path($session_save_path);
session_start(); 

?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <title>Nigerian Navy Integrated Logistics Management System</title>
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <link rel="stylesheet" href="./styles/title.css" type="text/css" />
</head>

<body>

<div class="personalBar">
<div class="location"> <?PHP echo str_replace("_", " ", $_SESSION['nav']) ?> >> 

<?PHP 
if(isset($_GET['title']))
    {
      echo $_GET['title'];
    }
   else
    {
      echo "home";
    }
?> 
</div>

<div class="auth">
<?PHP

if(!empty($_SESSION['user']))
   {
     echo $_SESSION['user'] . " is logged in :: <a href=\"auth.php\" target=\"maintmain\">logout</a>";
   }
   else
    {
      echo "You are not logged in :: <a href=\"auth.php\" target=\"maintmain\">login</a>";
    }
?>
</div>
</div>

<?PHP
echo "<h1>Nigerian Navy Integrated Logistics Management System</h1>";
?>

</body>
</html>

<?PHP

/*
 * sets the class of the tab
 */
function link_class($name, $nav)
{
  if($nav == $name)
    {
      $class = 'selected';
    } 
  else
    {
      $class = 'plain';
    }
  return $class;
}
?>
