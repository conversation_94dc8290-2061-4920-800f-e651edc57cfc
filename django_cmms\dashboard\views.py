"""
Dashboard views for CMMS application.
"""
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import TemplateView
from django.db.models import Count, Q, Avg
from django.utils import timezone
from datetime import timedelta, datetime

from work_orders.models import WorkOrder, Priority
from equipment.models import Equipment
from maintenance.models import MaintenanceTask, TroubleCall
from .models import DashboardWidget, SystemMetric


class DashboardView(LoginRequiredMixin, TemplateView):
    """
    Main dashboard view with customizable widgets.
    """
    template_name = 'dashboard/dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        
        # Get user's dashboard widgets
        context['widgets'] = DashboardWidget.objects.filter(
            user=user,
            is_visible=True
        ).order_by('position_y', 'position_x')
        
        # Get dashboard data based on user role
        context.update(self.get_dashboard_data())
        
        return context
    
    def get_dashboard_data(self):
        """Get dashboard data based on user permissions."""
        user = self.request.user
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        data = {}
        
        # Work Orders Summary
        work_orders_qs = WorkOrder.objects.all()
        if not user.is_supervisor_or_higher:
            # Regular users see only their work orders
            work_orders_qs = work_orders_qs.filter(
                Q(requestor=user) | Q(assigned_to=user)
            )
        
        data['work_orders'] = {
            'total': work_orders_qs.count(),
            'open': work_orders_qs.filter(
                status__in=['submitted', 'approved', 'assigned', 'in_progress']
            ).count(),
            'overdue': work_orders_qs.filter(
                due_date__lt=timezone.now(),
                status__in=['submitted', 'approved', 'assigned', 'in_progress']
            ).count(),
            'completed_this_week': work_orders_qs.filter(
                status='completed',
                completed_at__gte=week_ago
            ).count(),
        }
        
        # Equipment Status
        if user.is_mechanic:
            equipment_qs = Equipment.objects.filter(is_active=True)
            data['equipment'] = {
                'total': equipment_qs.count(),
                'operational': equipment_qs.filter(status='operational').count(),
                'maintenance': equipment_qs.filter(status='maintenance').count(),
                'out_of_service': equipment_qs.filter(status='out_of_service').count(),
                'maintenance_due': equipment_qs.filter(
                    next_maintenance_date__lte=today + timedelta(days=7)
                ).count(),
            }
        
        # Recent Activity
        data['recent_work_orders'] = work_orders_qs.order_by('-updated_at')[:5]
        
        # My Tasks (for assigned user)
        if user.is_mechanic:
            data['my_tasks'] = WorkOrder.objects.filter(
                assigned_to=user,
                status__in=['assigned', 'in_progress']
            ).order_by('due_date')[:10]
        
        # Maintenance Schedule
        if user.is_mechanic:
            data['upcoming_maintenance'] = MaintenanceTask.objects.filter(
                scheduled_date__gte=today,
                scheduled_date__lte=today + timedelta(days=14),
                status__in=['scheduled', 'in_progress']
            ).order_by('scheduled_date')[:10]
        
        # Priority Distribution
        data['priority_stats'] = work_orders_qs.values(
            'priority__name', 'priority__color'
        ).annotate(
            count=Count('id')
        ).order_by('priority__level')
        
        # Performance Metrics (for supervisors and above)
        if user.is_supervisor_or_higher:
            data['metrics'] = {
                'avg_completion_time': work_orders_qs.filter(
                    completed_at__isnull=False,
                    started_at__isnull=False
                ).aggregate(
                    avg_time=Avg('completed_at') - Avg('started_at')
                )['avg_time'],
                'completion_rate': self.calculate_completion_rate(work_orders_qs),
                'overdue_percentage': self.calculate_overdue_percentage(work_orders_qs),
            }
        
        return data
    
    def calculate_completion_rate(self, queryset):
        """Calculate work order completion rate."""
        total = queryset.count()
        if total == 0:
            return 0
        completed = queryset.filter(status='completed').count()
        return round((completed / total) * 100, 1)
    
    def calculate_overdue_percentage(self, queryset):
        """Calculate percentage of overdue work orders."""
        open_orders = queryset.filter(
            status__in=['submitted', 'approved', 'assigned', 'in_progress']
        )
        total_open = open_orders.count()
        if total_open == 0:
            return 0
        overdue = open_orders.filter(due_date__lt=timezone.now()).count()
        return round((overdue / total_open) * 100, 1)


@login_required
def dashboard_api_data(request):
    """
    API endpoint for dashboard data (for AJAX updates).
    """
    from django.http import JsonResponse
    
    # This would return JSON data for dynamic dashboard updates
    # Implementation depends on specific widget requirements
    
    return JsonResponse({
        'status': 'success',
        'timestamp': timezone.now().isoformat(),
        'data': {}
    })


class AnalyticsView(LoginRequiredMixin, TemplateView):
    """
    Advanced analytics and reporting view.
    """
    template_name = 'dashboard/analytics.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Only allow supervisors and above to access analytics
        if not self.request.user.is_supervisor_or_higher:
            context['access_denied'] = True
            return context
        
        # Get analytics data
        context.update(self.get_analytics_data())
        
        return context
    
    def get_analytics_data(self):
        """Get comprehensive analytics data."""
        today = timezone.now().date()
        
        # Time-based analysis
        data = {
            'monthly_trends': self.get_monthly_trends(),
            'equipment_reliability': self.get_equipment_reliability(),
            'cost_analysis': self.get_cost_analysis(),
            'performance_metrics': self.get_performance_metrics(),
        }
        
        return data
    
    def get_monthly_trends(self):
        """Get monthly trends for work orders."""
        # Implementation for monthly trend analysis
        return {}
    
    def get_equipment_reliability(self):
        """Get equipment reliability metrics."""
        # Implementation for equipment reliability analysis
        return {}
    
    def get_cost_analysis(self):
        """Get cost analysis data."""
        # Implementation for cost analysis
        return {}
    
    def get_performance_metrics(self):
        """Get performance metrics."""
        # Implementation for performance metrics
        return {}
