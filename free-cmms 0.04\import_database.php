<?php
// This script imports the database structure and initial data

// Display all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Database Import</h1>";

// Database connection parameters
$hostName = "localhost";
$userName = "root";
$password = "";
$databaseName = "cmms";

// Connect to MySQL
try {
    $conn = mysqli_connect($hostName, $userName, $password);
    if (!$conn) {
        die("Connection failed: " . mysqli_connect_error());
    }
    
    echo "Connected to MySQL successfully<br>";
    
    // Drop database if it exists
    $sql = "DROP DATABASE IF EXISTS $databaseName";
    if (mysqli_query($conn, $sql)) {
        echo "Database dropped successfully<br>";
    } else {
        echo "Error dropping database: " . mysqli_error($conn) . "<br>";
    }
    
    // Create database
    $sql = "CREATE DATABASE $databaseName";
    if (mysqli_query($conn, $sql)) {
        echo "Database created successfully<br>";
    } else {
        echo "Error creating database: " . mysqli_error($conn) . "<br>";
    }
    
    // Select database
    mysqli_select_db($conn, $databaseName);
    
    // Create tables
    echo "<h2>Creating Tables...</h2>";
    
    // Equipment table
    $sql = "CREATE TABLE equipment (
      id int(11) NOT NULL auto_increment,
      parent_id int(11) NOT NULL default '0',
      description varchar(20) NOT NULL default '',
      KEY id (id)
    ) ENGINE=InnoDB COMMENT='Equipment'";
    
    if (mysqli_query($conn, $sql)) {
        echo "Table equipment created successfully<br>";
    } else {
        echo "Error creating table: " . mysqli_error($conn) . "<br>";
    }
    
    // Groups table
    $sql = "CREATE TABLE groups (
      uname varchar(20) NOT NULL default '',
      passwd varchar(12) NOT NULL default '',
      grp set('clerk','mechanic','lead','supervisor','manager') NOT NULL default '',
      last_login timestamp NOT NULL
    ) ENGINE=InnoDB COMMENT='Table of groups of users'";
    
    if (mysqli_query($conn, $sql)) {
        echo "Table groups created successfully<br>";
    } else {
        echo "Error creating table: " . mysqli_error($conn) . "<br>";
    }
    
    // Mechanics table
    $sql = "CREATE TABLE mechanics (
      id int(11) NOT NULL auto_increment,
      lname char(20) NOT NULL default '',
      fname char(20) NOT NULL default '',
      craft set('op','mech','other') NOT NULL default '',
      busy int(4) NOT NULL default '0',
      PRIMARY KEY (id)
    ) ENGINE=InnoDB COMMENT='List of repack mechanics'";
    
    if (mysqli_query($conn, $sql)) {
        echo "Table mechanics created successfully<br>";
    } else {
        echo "Error creating table: " . mysqli_error($conn) . "<br>";
    }
    
    // Next_wo table
    $sql = "CREATE TABLE next_wo (
      id int(11) NOT NULL default '0'
    ) ENGINE=InnoDB COMMENT='The next work order number to use'";
    
    if (mysqli_query($conn, $sql)) {
        echo "Table next_wo created successfully<br>";
    } else {
        echo "Error creating table: " . mysqli_error($conn) . "<br>";
    }
    
    // Priority table
    $sql = "CREATE TABLE priority (
      priority int(11) NOT NULL default '0',
      description text NOT NULL,
      PRIMARY KEY (priority)
    ) ENGINE=InnoDB";
    
    if (mysqli_query($conn, $sql)) {
        echo "Table priority created successfully<br>";
    } else {
        echo "Error creating table: " . mysqli_error($conn) . "<br>";
    }
    
    // Queries table
    $sql = "CREATE TABLE queries (
      name varchar(25) NOT NULL default '',
      mode set('work_order','hot_job','equipment') NOT NULL default '',
      caption varchar(25) NOT NULL default '',
      title varchar(50) NOT NULL default '',
      groups text NOT NULL,
      sql_txt text NOT NULL,
      col_attributes text,
      PRIMARY KEY (name)
    ) ENGINE=InnoDB COMMENT='SQL query store'";
    
    if (mysqli_query($conn, $sql)) {
        echo "Table queries created successfully<br>";
    } else {
        echo "Error creating table: " . mysqli_error($conn) . "<br>";
    }
    
    // Trouble_calls table
    $sql = "CREATE TABLE trouble_calls (
      hj_id int(11) NOT NULL default '0',
      date date NOT NULL default '0000-00-00',
      short_description varchar(50) NOT NULL default '',
      description text NOT NULL,
      parts text NOT NULL,
      comments text NOT NULL,
      equipment_id int(11) NOT NULL default '0',
      mechanic_id int(4) NOT NULL default '0',
      operator_id int(4) NOT NULL default '0',
      hours int(4) NOT NULL default '0',
      PRIMARY KEY (hj_id)
    ) ENGINE=InnoDB COMMENT='Production-down jobs'";
    
    if (mysqli_query($conn, $sql)) {
        echo "Table trouble_calls created successfully<br>";
    } else {
        echo "Error creating table: " . mysqli_error($conn) . "<br>";
    }
    
    // Work_orders table
    $sql = "CREATE TABLE work_orders (
      wo_id int(11) NOT NULL auto_increment,
      ref_no varchar(32) default NULL,
      descriptive_text varchar(50) NOT NULL default '',
      audit_item int(4) default NULL,
      requestor varchar(20) NOT NULL default '',
      approval varchar(20) NOT NULL default '',
      equipment varchar(30) NOT NULL default '',
      description text NOT NULL,
      action text,
      mechanic_id int(11) default NULL,
      priority int(1) NOT NULL default '0',
      submit_date date NOT NULL default '0000-00-00',
      est_hours int(4) default NULL,
      act_hours int(4) default NULL,
      account varchar(15) default NULL,
      complete_date date default NULL,
      coordinating_instructions text,
      needed_date date default NULL,
      wo_status set('Approved','Assigned','Pending Approval','Suspended','Completed','Rejected','Hot Job') NOT NULL default '',
      inspected_by varchar(20) NOT NULL default '',
      updated timestamp NOT NULL,
      PRIMARY KEY (wo_id)
    ) ENGINE=InnoDB COMMENT='Repack work orders'";
    
    if (mysqli_query($conn, $sql)) {
        echo "Table work_orders created successfully<br>";
    } else {
        echo "Error creating table: " . mysqli_error($conn) . "<br>";
    }
    
    // Insert initial data
    echo "<h2>Inserting Initial Data...</h2>";
    
    // Insert into next_wo
    $sql = "INSERT INTO next_wo VALUES (1)";
    if (mysqli_query($conn, $sql)) {
        echo "Data inserted into next_wo successfully<br>";
    } else {
        echo "Error inserting data: " . mysqli_error($conn) . "<br>";
    }
    
    // Insert into priority
    $sql = "INSERT INTO priority VALUES 
            (1, '1 Immediate: Safety /Production Down'),
            (2, '2 ASAP'),
            (3, '3 Before specific date'),
            (4, '4 No priority'),
            (5, '5 Standing work order')";
    if (mysqli_query($conn, $sql)) {
        echo "Data inserted into priority successfully<br>";
    } else {
        echo "Error inserting data: " . mysqli_error($conn) . "<br>";
    }
    
    // Insert into groups
    $sql = "INSERT INTO groups VALUES ('manager', 'manager', 'manager', NOW())";
    if (mysqli_query($conn, $sql)) {
        echo "Data inserted into groups successfully<br>";
    } else {
        echo "Error inserting data: " . mysqli_error($conn) . "<br>";
    }
    
    echo "<h1>Database Setup Complete</h1>";
    echo "<p>You can now <a href='index.php'>access the application</a>.</p>";
    echo "<p>Login with:</p>";
    echo "<ul>";
    echo "<li>Username: manager</li>";
    echo "<li>Password: manager</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
