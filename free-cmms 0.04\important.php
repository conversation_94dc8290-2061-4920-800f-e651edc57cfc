<?PHP 
include('config.inc.php');
session_save_path($session_save_path);
session_start(); 


require("config.inc.php");
require_once("./libraries/Table.php");
$connection = mysqli_connect($hostName, $userName, $password, $databaseName) or die('Could not connect to host: ' . $hostName);


/**
 * This function retrieves a list of mechanics and their assigned work orders.
 * It creates a temporary table to track mechanics' workloads, fetches the data,
  // Creating a temporary table to track busy mechanics and their assigned work orders
 *
 * @return string HTML representation of the mechanics' workload table.
 */
function wander_mech()
{

  //There may be a better way to do the following but I could not produce it
$sql_1 = 'CREATE TEMPORARY TABLE busy 
        SELECT wo_id, mechanic_id 
        FROM work_orders  
        WHERE wo_status LIKE "Assigned";';
 
$sql_2 = 'SELECT CONCAT(lname,", ", fname), count( wo_id ) as count
        FROM mechanics AS m 
        LEFT JOIN busy AS b ON m.id = b.mechanic_id 
        WHERE m.craft LIKE "mech" GROUP BY m.id;';
 $sql_3 = 'DROP TABLE busy;';

  /*
$sql_1 = 'CREATE TEMPORARY TABLE busy 
        SELECT hj_id, mechanic_id 
        FROM trouble_calls';
 
$sql_2 = 'SELECT CONCAT(lname,", ", fname), count( hj_id ) as count
        FROM mechanics AS m 
        LEFT JOIN busy AS b ON m.id = b.mechanic_id 
        WHERE m.craft LIKE "mech" GROUP BY m.id;';
 $sql_3 = 'DROP TABLE busy;';

*/  
mysqli_query($connection, $sql_1) or die("Query failed: $sql_1");

$results = mysqli_query($connection, $sql_2) or die("Query failed: $sql_2");

mysqli_query($connection, $sql_3) or die("Query failed: $sql_3");

 $mechanics = array();

while($row = mysqli_fetch_array($results, MYSQLI_NUM))
 while($row = mysqli_fetch_array($results, MYSQLI_NUM))
     array_push($mechanics, $row);
   }


 $tbl_mechs = new HTML_Table;


 $header = array('Mechanics','Work Orders');
 $attr_empty = array();
 $tbl_mechs->addRow($header,$attr_empty,'TH');


 foreach($mechanics as $mechanic)
   {
     if($mechanic[1] == 0 || $mechanic[1] > 5) //if mechanic is assigned 0 or more than 5 work orders
     // Highlight mechanics who are either not assigned any work orders (0) 
     // or are overloaded with more than 5 work orders.
     if($mechanic[1] == 0 || $mechanic[1] > 5)
       {
   $attr = array('class'=>'hot');
       }
       {
	 unset($attr);
       }
     $row = $tbl_mechs->addRow($mechanic, $attr);
     $tbl_mechs->updateCellAttributes($row,1, "align=center");
    }

 return $tbl_mechs->toHtml();

}

function high_priority()
{

  $sql = "SELECT wo_id, descriptive_text, submit_date, wo_status 
          FROM work_orders 
          WHERE (wo_status LIKE 'Pending Approval' 
                 OR wo_status LIKE 'Assigned' 
                 OR wo_status LIKE 'Suspended') 
              AND priority = 1" ;
  
  $results = mysqli_query($connection, $sql) or die("Could not execute query: $sql");

  $wos = array();
 
  while($row = mysql_fetch_array($results, MYSQL_NUM))
   {
     array_push($wos, $row);
   }

  $tbl_priority = new HTML_Table;

  $header = array('', 'Description', 'Submit Date','Status');
  $attr_empty = array();
  $tbl_priority->addRow($header,$attr_empty,'TH');  

  foreach($wos as $wo)
    {
      $wo[0] = "<a href=\"javascript://\" onClick=\"openwindow($wo[0])\">$wo[0]</a>";
     $row = $tbl_priority->addRow($wo);
  
    }

  echo $tbl_priority->toHtml();
  
}

function almost_due()
{

  $sql = 'SELECT wo_id, ( TO_DAYS( needed_date ) - TO_DAYS( CURDATE( ) ) ) AS due, descriptive_text, wo_status 
          FROM work_orders 
          WHERE ( wo_status NOT LIKE "Completed" ) 
              AND ( wo_status NOT LIKE "Rejected" ) 
              AND priority = 3 
              AND TO_DAYS( needed_date ) - TO_DAYS( CURDATE( ) ) < 10 ';

  $results = mysql_query($sql) or die("could not execute query: $sql");

  $wos = array();
 
  while($row = mysql_fetch_array($results, MYSQL_NUM))
    {
      array_push($wos, $row);
    }

  $tbl_urgent = new HTML_Table;

  $header = array('', 'Days Left', 'Description', 'Status');
  $attr_empty = array();
  $tbl_urgent->addRow($header,$attr_empty,'TH');  
 
  foreach($wos as $wo) 
    {
      if($wo[1]<0) //change -x to (x)
	{
	  $wo[1] = "(" . abs($wo[1]) . ")";
	} 
 
      $wo[0] = "<a href=\"javascript://\" onClick=\"openwindow($wo[0])\">$wo[0]</a>";
      $row = $tbl_urgent->addRow($wo);
    }

  $tbl_urgent->updateColAttributes(1, array('align'=>'center'));

  return $tbl_urgent->toHtml();
}

function stats()
{

}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>

    <meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
  
    <title>Important Information At A Glance</title>

    <script src="./libraries/functions.js" type="text/javascript" language="javascript"></script>
	<!-- Include style sheet to make web form similar to paper form --> 

    <style>
<?PHP 
        
include("./libraries/browser.inc.php"); //detect the browser
include("./styles/dynamic_css.php");    //set up font sizes, etc per browser
css_site("important.css");               //applice this to the style sheet

                        
?>

    </style>
</head>

<body class="important">

    
    <h1>Work Orders By Mechanics</h1>
    <?PHP echo wander_mech();   ?>

    <h1>High Priority</h1>
    <?PHP echo high_priority(); ?>

    <h1>Due Soon</h1>
    <?PHP echo almost_due();    ?>

    <h1>Statistics</h1>
    <?PHP echo stats();         ?>

   </form>

</body>

</html>



<?PHP/
//these tags added to make mmm-php mode in emacs look better...take them out eventually...maybe
?>