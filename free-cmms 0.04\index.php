<?PHP 
include('config.inc.php');
session_save_path($session_save_path);
session_start(); 

//If we navigated to a new tab $_GET['nav'] will be set. So sync $_SESSION['nav'] to the new nav.
//$_SESSION['nav'] is necessary to preserve the 'You are here...' bit when navigating around screens
//within the same tab.
if(!empty($_GET['nav']))
{
  $_SESSION['nav'] = $_GET['nav'];
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Frameset//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-frameset.dtd">

<head>

    <title>NN CMMS</title>

    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />

    <link REL="shortcut icon" HREF="./images/new_logo.png" TYPE="image/png">

</head>

<frameset cols="*" rows="65px,*" framespacing="0" border="0"  bordercolor="#000000" >

<?PHP
echo "<frame class=\"title\" src=\"title.php?nav=" . $_SESSION['nav'] . "\" name=\"title\" scrolling=\"no\" frameborder=\"0\" bgcolor=\"5961a0\">";
?>

<?PHP



switch($_SESSION['nav'])
{
  case 'docs':
  //$frames = '<frame src="./xml/index.php" name="docs">';
  break;

  case 'pm':
      $frames = '<frame src="./pm.php" name="pm">';
  break;

  case 'admin':
      $frames = "<frameset cols=\"150,*\" rows=\"*\" framespacing=\"0\" border=\"0\" bordercolor=\"#000000\">	
										        <frame src=\"nav.php\" name=\"nav\" frameborder=\"0\" border=\"0\"  marginwidth=\"5\" marginheight=\"20\"/>				


       <frame src=\"./admin.php\" name=\"maintmain\" marginwidth=\"5\"  marginheight=\"20\" frameborder=\"0\"/>";
  break;

  case 'trouble_calls':
      $frames = "<frameset cols=\"150,*\" rows=\"*\" framespacing=\"0\" border=\"0\" bordercolor=\"#000000\">	
										        <frame src=\"nav.php\" name=\"nav\" frameborder=\"0\" border=\"0\"  marginwidth=\"5\" marginheight=\"20\"/>				


       <frame src=\"list.php?query_name=HJ_RECENT\" name=\"maintmain\" marginwidth=\"5\"  marginheight=\"20\" frameborder=\"0\"/>";
  break;

  case 'work_request':
      $frames = "<frameset cols=\"150,*\" rows=\"*\" framespacing=\"0\" border=\"0\" bordercolor=\"#000000\">	
										        <frame src=\"nav.php\" name=\"nav\" frameborder=\"0\" border=\"0\"  marginwidth=\"5\" marginheight=\"20\"/>				


       <frame src=\"list.php?query_name=WR_ALL\" name=\"maintmain\" marginwidth=\"5\"  marginheight=\"20\" frameborder=\"0\"/>";
  break;

  default:
  $frames = "<frameset cols=\"150,*\" rows=\"*\" framespacing=\"0\" border=\"0\" bordercolor=\"#000000\">	
														
        <frame src=\"nav.php\" name=\"nav\" frameborder=\"0\" border=\"0\"  marginwidth=\"5\" marginheight=\"20\"/>

       <frame src=\"list.php?query_name=NEW_TO_YOU\" name=\"maintmain\" marginwidth=\"5\"  marginheight=\"20\" frameborder=\"0\"/>";
  break;
}

echo $frames;
echo $nav
?>

    </frameset>
    
    <noframes>

        <body>
            <p>This interface requires a frames-capable browser<b>frames-capable</b> browser.</p>
        </body>

    </noframes>

</frameset>

</html>