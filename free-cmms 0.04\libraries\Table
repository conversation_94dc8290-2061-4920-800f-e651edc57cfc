.php
/**
* Adjusts ends (total number of rows and columns) 
* @param    int     $row        Row index
* @param    int     $col        Column index
* @param    string  $method     Method name of caller
*                              Used to populate PEAR_Error if thrown.
* @param    array   $attributes Assoc array of attributes
*                              Default is an empty array.
* @access   private
* @throws   PEAR_Error
*/
function _adjustEnds($row, $col, $method, $attributes = array())
{
    $colspan = isset($attributes['colspan']) ? $attributes['colspan'] : 1;
    $rowspan = isset($attributes['rowspan']) ? $attributes['rowspan'] : 1;
    
    if (($row + $rowspan - 1) >= $this->_rows) {
        if ($this->_autoGrow) {
            $this->_rows = $row + $rowspan;
        } else {
            $pear = new PEAR();
            return $pear->raiseError('Invalid table row reference[' .
                $row . '] in HTML_Table::' . $method);
        }
    }

    if (($col + $colspan - 1) >= $this->_cols) {
        if ($this->_autoGrow) {
            $this->_cols = $col + $colspan;
        } else {
            $pear = new PEAR();
            return $pear->raiseError('Invalid table column reference[' .
                $col . '] in HTML_Table::' . $method);
        }
    }
    
    return true; // Return true if no errors occurred
}