:root {
    --font-size-small: 12px;
    --font-size-medium: 16px;
    --color-text: #ffffff; /* Updated for better contrast */
    --color-background: #002147; /* Navy blue */
    --color-highlight: #0056b3; /* Lighter blue for highlights */
}

body {
    font-family: Arial, Helvetica, sans-serif;
    font-size: var(--font-size-small);
    color: var(--color-text);
    background-color: var(--color-background);
}

.important {
    background-color: var(--color-highlight);
    margin-left: 3%;
    color: var(--color-text); /* Ensure text is visible */
}

H1 {
    font-size: var(--font-size-medium);
    background-color: var(--color-background);
    border-style: solid;
    border-width: 1px 0px 1px 0px;
    padding-left: 10px;
    margin-right: 20px;
}

table {
    border-style: solid;
    border-width: 1px;
    border-color: var(--color-highlight);
    border-collapse: collapse;
    margin-left: 25px;
    margin-bottom: 25px;
    background-color: var(--color-background); /* Navy theme */
    color: var(--color-text);
}

th {
    text-align: center;
    background-color: var(--color-highlight); /* Highlighted header */
    color: var(--color-text);
}

.hot {
    color: red;
    font-weight: bold;
}

td {
    font-size: var(--font-size-small);
    color: var(--color-text);
    background-color: var(--color-background);
    border-color: var(--color-highlight);
    border-style: solid;
    border-width: 1px;
    border-collapse: collapse;
    padding: 2px 5px 2px 5px;
}

textarea, input, select {
    background-color: var(--color-highlight);
    border-width: 1px;
    border-color: #cccccc;
    border-style: solid;
    padding: 2px;
    margin: 1px;
    font-family: Arial, Helvetica, sans-serif;
    font-size: var(--font-size-small);
    color: #333333;
}
