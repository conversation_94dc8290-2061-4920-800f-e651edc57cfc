# Django CMMS PowerShell Runner
Write-Host "Django CMMS - Naval Fleet Management System" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host ""

# Check Python
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "Python not found. Please install Python 3.8+" -ForegroundColor Red
    exit 1
}

# Check if we're in the right directory
if (-not (Test-Path "manage.py")) {
    Write-Host "manage.py not found. Please run from Django project root." -ForegroundColor Red
    exit 1
}

Write-Host "Installing basic dependencies..." -ForegroundColor Yellow
try {
    pip install Django djangorestframework python-decouple
    Write-Host "Basic dependencies installed" -ForegroundColor Green
} catch {
    Write-Host "Warning: Could not install all dependencies" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Setting up database..." -ForegroundColor Yellow

# Create basic .env file if it doesn't exist
if (-not (Test-Path ".env")) {
    @"
SECRET_KEY=django-insecure-temp-key-for-development-only-change-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
"@ | Out-File -FilePath ".env" -Encoding UTF8
    Write-Host "Created .env file" -ForegroundColor Green
}

# Run migrations
try {
    python manage.py makemigrations --verbosity=0
    python manage.py migrate --verbosity=0
    Write-Host "Database setup completed" -ForegroundColor Green
} catch {
    Write-Host "Database setup failed" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Starting Django development server..." -ForegroundColor Green
Write-Host "Open your browser to: http://localhost:8000" -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Start the server
try {
    python manage.py runserver
} catch {
    Write-Host "Failed to start server" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Server stopped" -ForegroundColor Yellow
