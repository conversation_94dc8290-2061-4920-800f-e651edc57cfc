"""
Lifecycle Management models for Naval CMMS application.
Based on analysis of NAVT ATTRIBUTES ENTITIES document - advanced lifecycle tracking.
"""
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.conf import settings
from datetime import date, timedelta
from decimal import Decimal


class LifecycleStage(models.Model):
    """
    Lifecycle stages for equipment and components.
    """
    name = models.CharField(
        max_length=100,
        help_text="Stage name"
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Stage code"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Stage description"
    )
    
    percentage_of_life = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        help_text="Percentage of total lifecycle (0-100)"
    )
    
    color = models.CharField(
        max_length=7,
        default='#007bff',
        help_text="Color for visual identification"
    )
    
    requires_action = models.BooleanField(
        default=False,
        help_text="Whether this stage requires specific action"
    )
    
    action_description = models.TextField(
        blank=True,
        help_text="Description of required action"
    )
    
    is_active = models.<PERSON><PERSON>an<PERSON>ield(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'lifecycle_stage'
        ordering = ['percentage_of_life']
        
    def __str__(self):
        return f"{self.code} - {self.name}"


class EquipmentLifecycle(models.Model):
    """
    Lifecycle tracking for equipment with PMS cycles and disposal planning.
    """
    OPERATIONAL_STATUS_CHOICES = [
        ('operational', 'Operational'),
        ('maintenance', 'Under Maintenance'),
        ('repair', 'Under Repair'),
        ('testing', 'Under Testing'),
        ('standby', 'Standby'),
        ('decommissioned', 'Decommissioned'),
        ('disposed', 'Disposed'),
    ]
    
    equipment = models.OneToOneField(
        'equipment.Equipment',
        on_delete=models.CASCADE,
        related_name='lifecycle',
        help_text="Equipment being tracked"
    )
    
    # Installation and Service Dates
    date_of_installation = models.DateField(
        help_text="Date equipment was installed"
    )
    
    date_of_commissioning = models.DateField(
        blank=True,
        null=True,
        help_text="Date equipment was commissioned for service"
    )
    
    date_of_removal = models.DateField(
        blank=True,
        null=True,
        help_text="Date equipment was removed from service"
    )
    
    expected_disposal_date = models.DateField(
        blank=True,
        null=True,
        help_text="Expected disposal date"
    )
    
    actual_disposal_date = models.DateField(
        blank=True,
        null=True,
        help_text="Actual disposal date"
    )
    
    # Lifecycle Information
    expected_life_years = models.PositiveIntegerField(
        help_text="Expected operational life in years"
    )
    
    expected_life_hours = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Expected operational life in hours"
    )
    
    current_operating_hours = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Current operating hours"
    )
    
    current_lifecycle_stage = models.ForeignKey(
        LifecycleStage,
        on_delete=models.PROTECT,
        related_name='equipment_at_stage',
        help_text="Current lifecycle stage"
    )
    
    # Status
    operational_status = models.CharField(
        max_length=20,
        choices=OPERATIONAL_STATUS_CHOICES,
        default='operational',
        help_text="Current operational status"
    )
    
    # PMS (Planned Maintenance System) Information
    pms_cycle_days = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="PMS cycle in days"
    )
    
    pms_cycle_hours = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="PMS cycle in operating hours"
    )
    
    last_pms_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of last PMS"
    )
    
    next_pms_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of next scheduled PMS"
    )
    
    last_pms_hours = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Operating hours at last PMS"
    )
    
    next_pms_hours = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Operating hours for next PMS"
    )
    
    # Warranty Information
    warranty_start_date = models.DateField(
        blank=True,
        null=True,
        help_text="Warranty start date"
    )
    
    warranty_end_date = models.DateField(
        blank=True,
        null=True,
        help_text="Warranty end date"
    )
    
    warranty_terms = models.TextField(
        blank=True,
        help_text="Warranty terms and conditions"
    )
    
    warranty_provider = models.CharField(
        max_length=200,
        blank=True,
        help_text="Warranty provider/manufacturer"
    )
    
    # Storage and Environmental Requirements
    storage_requirements = models.TextField(
        blank=True,
        help_text="Special storage requirements"
    )
    
    environmental_conditions = models.JSONField(
        default=dict,
        blank=True,
        help_text="Required environmental conditions as JSON"
    )
    
    # Physical Specifications
    dimensions = models.CharField(
        max_length=100,
        blank=True,
        help_text="Equipment dimensions (L x W x H)"
    )
    
    weight_kg = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Weight in kilograms"
    )
    
    # Location Tracking
    location_on_ship = models.CharField(
        max_length=200,
        blank=True,
        help_text="Specific location on ship/unit"
    )
    
    # Additional Information
    replacement_parts_list = models.TextField(
        blank=True,
        help_text="List of replacement parts and part numbers"
    )
    
    disposal_instructions = models.TextField(
        blank=True,
        help_text="Special disposal instructions"
    )
    
    lifecycle_notes = models.TextField(
        blank=True,
        help_text="Additional lifecycle notes"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'lifecycle_equipment_lifecycle'
        
    def __str__(self):
        return f"{self.equipment.name} - Lifecycle"
    
    def get_absolute_url(self):
        return reverse('lifecycle:equipment_detail', kwargs={'pk': self.pk})
    
    @property
    def age_in_years(self):
        """Calculate equipment age in years."""
        today = date.today()
        return (today - self.date_of_installation).days / 365.25
    
    @property
    def age_in_days(self):
        """Calculate equipment age in days."""
        today = date.today()
        return (today - self.date_of_installation).days
    
    @property
    def lifecycle_percentage(self):
        """Calculate percentage of lifecycle completed."""
        if self.expected_life_years:
            return min((self.age_in_years / self.expected_life_years) * 100, 100)
        return 0
    
    @property
    def days_to_disposal(self):
        """Calculate days until expected disposal."""
        if not self.expected_disposal_date:
            return None
        return (self.expected_disposal_date - date.today()).days
    
    @property
    def is_nearing_end_of_life(self):
        """Check if equipment is nearing end of life (>80% of lifecycle)."""
        return self.lifecycle_percentage > 80
    
    @property
    def is_overdue_for_replacement(self):
        """Check if equipment is overdue for replacement."""
        return self.lifecycle_percentage >= 100
    
    @property
    def warranty_status(self):
        """Get warranty status."""
        if not self.warranty_end_date:
            return "No warranty information"
        
        today = date.today()
        if today > self.warranty_end_date:
            return "Warranty expired"
        elif (self.warranty_end_date - today).days <= 30:
            return "Warranty expiring soon"
        else:
            return "Under warranty"
    
    @property
    def is_warranty_expired(self):
        """Check if warranty is expired."""
        if not self.warranty_end_date:
            return False
        return date.today() > self.warranty_end_date
    
    @property
    def days_until_warranty_expiry(self):
        """Calculate days until warranty expiry."""
        if not self.warranty_end_date:
            return None
        return (self.warranty_end_date - date.today()).days
    
    @property
    def is_pms_due(self):
        """Check if PMS is due."""
        today = date.today()
        
        # Check date-based PMS
        if self.next_pms_date and today >= self.next_pms_date:
            return True
        
        # Check hours-based PMS
        if (self.next_pms_hours and 
            self.current_operating_hours >= self.next_pms_hours):
            return True
        
        return False
    
    @property
    def is_pms_overdue(self):
        """Check if PMS is overdue."""
        today = date.today()
        
        # Check date-based PMS (overdue by more than 7 days)
        if (self.next_pms_date and 
            (today - self.next_pms_date).days > 7):
            return True
        
        # Check hours-based PMS (overdue by more than 10% of cycle)
        if (self.next_pms_hours and self.pms_cycle_hours and
            self.current_operating_hours > (self.next_pms_hours + self.pms_cycle_hours * 0.1)):
            return True
        
        return False
    
    @property
    def time_to_next_pms(self):
        """Calculate time to next PMS."""
        if not self.next_pms_date:
            return None
        return (self.next_pms_date - date.today()).days
    
    def calculate_next_pms_date(self):
        """Calculate next PMS date based on cycle."""
        if self.pms_cycle_days and self.last_pms_date:
            return self.last_pms_date + timedelta(days=self.pms_cycle_days)
        return None
    
    def calculate_next_pms_hours(self):
        """Calculate next PMS hours based on cycle."""
        if self.pms_cycle_hours and self.last_pms_hours is not None:
            return self.last_pms_hours + self.pms_cycle_hours
        return None
    
    def update_lifecycle_stage(self):
        """Update lifecycle stage based on current percentage."""
        stages = LifecycleStage.objects.filter(is_active=True).order_by('percentage_of_life')
        
        for stage in stages:
            if self.lifecycle_percentage <= stage.percentage_of_life:
                self.current_lifecycle_stage = stage
                break
        else:
            # If no stage found, use the last one
            if stages.exists():
                self.current_lifecycle_stage = stages.last()
        
        self.save()


class MaintenanceHistory(models.Model):
    """
    Comprehensive maintenance history for equipment.
    """
    MAINTENANCE_TYPES = [
        ('pms', 'Planned Maintenance (PMS)'),
        ('corrective', 'Corrective Maintenance'),
        ('preventive', 'Preventive Maintenance'),
        ('overhaul', 'Major Overhaul'),
        ('inspection', 'Inspection'),
        ('testing', 'Testing'),
        ('calibration', 'Calibration'),
        ('repair', 'Repair'),
        ('replacement', 'Component Replacement'),
    ]
    
    equipment_lifecycle = models.ForeignKey(
        EquipmentLifecycle,
        on_delete=models.CASCADE,
        related_name='maintenance_history',
        help_text="Equipment lifecycle"
    )
    
    maintenance_type = models.CharField(
        max_length=20,
        choices=MAINTENANCE_TYPES,
        help_text="Type of maintenance"
    )
    
    date_performed = models.DateField(
        help_text="Date maintenance was performed"
    )
    
    operating_hours_at_maintenance = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Operating hours when maintenance was performed"
    )
    
    performed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='performed_maintenance',
        help_text="Person who performed maintenance"
    )
    
    supervised_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='supervised_maintenance',
        help_text="Supervisor"
    )
    
    work_order = models.ForeignKey(
        'work_orders.WorkOrder',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='maintenance_history',
        help_text="Related work order"
    )
    
    description = models.TextField(
        help_text="Description of maintenance performed"
    )
    
    parts_replaced = models.TextField(
        blank=True,
        help_text="Parts and components replaced"
    )
    
    consumables_used = models.TextField(
        blank=True,
        help_text="Consumables and materials used"
    )
    
    tools_used = models.TextField(
        blank=True,
        help_text="Tools and equipment used"
    )
    
    duration_hours = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Duration of maintenance in hours"
    )
    
    cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Total cost of maintenance"
    )
    
    findings = models.TextField(
        blank=True,
        help_text="Findings and observations"
    )
    
    recommendations = models.TextField(
        blank=True,
        help_text="Recommendations for future maintenance"
    )
    
    next_maintenance_due = models.DateField(
        blank=True,
        null=True,
        help_text="Next maintenance due date"
    )
    
    next_maintenance_hours = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Next maintenance due at operating hours"
    )
    
    attachments = models.FileField(
        upload_to='maintenance/attachments/',
        blank=True,
        null=True,
        help_text="Maintenance reports and photos"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'lifecycle_maintenance_history'
        ordering = ['-date_performed']
        
    def __str__(self):
        return f"{self.equipment_lifecycle.equipment.name} - {self.maintenance_type} - {self.date_performed}"


class ComponentReplacement(models.Model):
    """
    Track component replacements within equipment.
    """
    equipment_lifecycle = models.ForeignKey(
        EquipmentLifecycle,
        on_delete=models.CASCADE,
        related_name='component_replacements',
        help_text="Equipment lifecycle"
    )
    
    component_name = models.CharField(
        max_length=200,
        help_text="Name of replaced component"
    )
    
    old_part_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="Old part number"
    )
    
    new_part_number = models.CharField(
        max_length=100,
        help_text="New part number"
    )
    
    old_serial_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="Old serial number"
    )
    
    new_serial_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="New serial number"
    )
    
    replacement_date = models.DateField(
        help_text="Date of replacement"
    )
    
    replacement_reason = models.TextField(
        help_text="Reason for replacement"
    )
    
    replaced_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='component_replacements',
        help_text="Person who performed replacement"
    )
    
    supplier = models.ForeignKey(
        'inventory.Vendor',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='component_replacements',
        help_text="Component supplier"
    )
    
    cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Replacement cost"
    )
    
    warranty_period_months = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Warranty period in months"
    )
    
    warranty_expiry_date = models.DateField(
        blank=True,
        null=True,
        help_text="Component warranty expiry date"
    )
    
    maintenance_history = models.ForeignKey(
        MaintenanceHistory,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='component_replacements',
        help_text="Related maintenance record"
    )
    
    notes = models.TextField(
        blank=True,
        help_text="Additional notes"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'lifecycle_component_replacement'
        ordering = ['-replacement_date']
        
    def __str__(self):
        return f"{self.component_name} - {self.replacement_date}"
    
    @property
    def is_under_warranty(self):
        """Check if component is under warranty."""
        if not self.warranty_expiry_date:
            return False
        return date.today() <= self.warranty_expiry_date


class DisposalPlan(models.Model):
    """
    Disposal planning for equipment nearing end of life.
    """
    DISPOSAL_METHODS = [
        ('sale', 'Sale'),
        ('scrap', 'Scrap'),
        ('donation', 'Donation'),
        ('recycling', 'Recycling'),
        ('hazardous_disposal', 'Hazardous Disposal'),
        ('cannibalization', 'Cannibalization for Parts'),
        ('return_to_manufacturer', 'Return to Manufacturer'),
    ]
    
    STATUS_CHOICES = [
        ('planned', 'Planned'),
        ('approved', 'Approved'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    equipment_lifecycle = models.OneToOneField(
        EquipmentLifecycle,
        on_delete=models.CASCADE,
        related_name='disposal_plan',
        help_text="Equipment lifecycle"
    )
    
    planned_disposal_date = models.DateField(
        help_text="Planned disposal date"
    )
    
    disposal_method = models.CharField(
        max_length=30,
        choices=DISPOSAL_METHODS,
        help_text="Method of disposal"
    )
    
    disposal_reason = models.TextField(
        help_text="Reason for disposal"
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='planned',
        help_text="Disposal status"
    )
    
    planned_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='planned_disposals',
        help_text="Person who planned disposal"
    )
    
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='approved_disposals',
        help_text="Person who approved disposal"
    )
    
    approved_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of approval"
    )
    
    # Environmental and Safety Considerations
    environmental_impact = models.TextField(
        blank=True,
        help_text="Environmental impact assessment"
    )
    
    safety_requirements = models.TextField(
        blank=True,
        help_text="Safety requirements for disposal"
    )
    
    special_handling_required = models.BooleanField(
        default=False,
        help_text="Whether special handling is required"
    )
    
    # Financial Information
    estimated_disposal_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Estimated cost of disposal"
    )
    
    estimated_recovery_value = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Estimated recovery value"
    )
    
    actual_disposal_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Actual disposal cost"
    )
    
    actual_recovery_value = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Actual recovery value"
    )
    
    # Disposal Execution
    disposal_contractor = models.CharField(
        max_length=200,
        blank=True,
        help_text="Disposal contractor or facility"
    )
    
    disposal_certificate = models.FileField(
        upload_to='disposal/certificates/',
        blank=True,
        null=True,
        help_text="Disposal certificate"
    )
    
    actual_disposal_date = models.DateField(
        blank=True,
        null=True,
        help_text="Actual disposal date"
    )
    
    disposal_notes = models.TextField(
        blank=True,
        help_text="Disposal notes and observations"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'lifecycle_disposal_plan'
        
    def __str__(self):
        return f"Disposal Plan - {self.equipment_lifecycle.equipment.name}"
    
    @property
    def net_disposal_value(self):
        """Calculate net disposal value (recovery - cost)."""
        recovery = self.actual_recovery_value or self.estimated_recovery_value or 0
        cost = self.actual_disposal_cost or self.estimated_disposal_cost or 0
        return recovery - cost
    
    @property
    def is_overdue(self):
        """Check if disposal is overdue."""
        if self.status == 'completed':
            return False
        return self.planned_disposal_date < date.today()
