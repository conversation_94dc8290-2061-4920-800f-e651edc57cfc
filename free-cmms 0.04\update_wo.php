<?php
include("./config.inc.php");
session_save_path($session_save_path);
session_start();
   /*connect to the database*/
   $connection=mysql_connect($hostName,$userName,$password) or die('Could not connect to the database server');
   $db=mysql_select_db($databaseName,$connection) or die('Unable to select the database.');
   
   /*create html code for work orders*/
   $wo_id = substr($wo_id,8);   
   $wo_info = get_work_order_info("work_orders", $wo_id);
   $labor_info = get_labor_info($wo_id);
   $html_code = make_html($wo_info, $labor_info);

?>

<html><head></head><body>
<? echo ($html_code); ?>
</body></html>

<?
   function get_work_order_info($tbl,$id) {

  /*construct the query*/
     $sql = "SELECT $tbl.* FROM $tbl WHERE $tbl.id = $id";

     /*run the query*/
     $query = mysql_query($sql) or die("$sql");

     /*save the results*/
     $info = mysql_fetch_object($query);

     /*return the results*/
     return $info;

   }

function get_labor_info($wid){

  /*initialize an array*/
    $labor = array();

    /*construct the query*/
	 $sql = "SELECT labor.wo_id, labor.mech_id, SUM( labor.hours ) AS hours , mechanics.fname AS fname, mechanics.lname AS lname FROM labor INNER JOIN mechanics ON labor.mech_id = mechanics.id WHERE labor.wo_id = $wid GROUP BY labor.mech_id";

	 /*run the query*/
     $query = mysql_query($sql);

     /*save the results*/
while($row= mysql_fetch_object($query)) {
    array_push($labor, $row);
} 

/*return the results*/
     return $labor;
}

function make_html($wo, $labor) {

  $str .= "<table cellpadding=\"2\" cellspacing=\"2\" border=\"1\" style=\"text-align: left; width: 100%;\"><tbody><tr>\n";   
   $str .= "<td style=\"vertical-align: top; width: 200px;\"><b>Work Order:</b>&nbsp $wo->id<br></td>\n";
   
   $str .= "<td style=\"vertical-align: top; width: 200px;\"><b>Priority:</b> &nbsp $wo->priority<br></td>\n";
   
   $str .= "<td style=\"vertical-align: top;\"><b>Submit Date:</b> &nbsp $wo->start<br></td></tr>\n";
   
   $str .= "<tr><td style=\"vertical-align: top;\" 1=\"\" colspan=\"3\" rowspan=\"1\"><pre>$wo->description </pre><br>\n";

   foreach ($labor as $ele) {

     $str .= "$ele->lname $ele->fname $ele->hours";
}
  return $str;

  }

?>