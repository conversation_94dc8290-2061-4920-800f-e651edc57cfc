<?php
// Display all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>MySQL Connection Test</h1>";

$hostName = "localhost";
$userName = "root";
$password = "";
$databaseName = "cmms_test";

// Method 1: Using mysqli
echo "<h2>Testing mysqli_connect</h2>";
try {
    $conn = mysqli_connect($hostName, $userName, $password);
    if (!$conn) {
        echo "Failed to connect using mysqli_connect: " . mysqli_connect_error() . "<br>";
    } else {
        echo "Successfully connected using mysqli_connect<br>";
        
        // Create test database
        $sql = "CREATE DATABASE IF NOT EXISTS $databaseName";
        if (mysqli_query($conn, $sql)) {
            echo "Database created successfully<br>";
        } else {
            echo "Error creating database: " . mysqli_error($conn) . "<br>";
        }
        
        // Select database
        mysqli_select_db($conn, $databaseName);
        
        // Create test table
        $sql = "CREATE TABLE IF NOT EXISTS test_table (
            id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(30) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        if (mysqli_query($conn, $sql)) {
            echo "Table created successfully<br>";
        } else {
            echo "Error creating table: " . mysqli_error($conn) . "<br>";
        }
        
        mysqli_close($conn);
    }
} catch (Exception $e) {
    echo "Exception with mysqli_connect: " . $e->getMessage() . "<br>";
}

// Method 2: Using PDO
echo "<h2>Testing PDO</h2>";
try {
    $dsn = "mysql:host=$hostName";
    $pdo = new PDO($dsn, $userName, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Successfully connected using PDO<br>";
    
    // Create test database
    $sql = "CREATE DATABASE IF NOT EXISTS {$databaseName}_pdo";
    $pdo->exec($sql);
    echo "Database created successfully<br>";
    
    // Select database
    $pdo = new PDO("mysql:host=$hostName;dbname={$databaseName}_pdo", $userName, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create test table
    $sql = "CREATE TABLE IF NOT EXISTS test_table_pdo (
        id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(30) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "Table created successfully<br>";
    
} catch (PDOException $e) {
    echo "PDO Exception: " . $e->getMessage() . "<br>";
}

echo "<h2>MySQL Server Information</h2>";
try {
    $conn = mysqli_connect($hostName, $userName, $password);
    if ($conn) {
        echo "MySQL Server Version: " . mysqli_get_server_info($conn) . "<br>";
        mysqli_close($conn);
    }
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "<br>";
}

echo "<h2>PHP Information</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Loaded PHP Extensions:<br>";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<ul>";
foreach ($extensions as $extension) {
    if (strpos($extension, 'mysql') !== false || $extension == 'mysqli' || $extension == 'pdo') {
        echo "<li><strong>$extension</strong></li>";
    } else {
        echo "<li>$extension</li>";
    }
}
echo "</ul>";
?>
