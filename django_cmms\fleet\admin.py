from django.contrib import admin
from .models import ShipClass, ShipUnit, Deployment, MaintenanceSchedule


@admin.register(ShipClass)
class ShipClassAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'displacement', 'length', 'max_speed', 'crew_capacity', 'is_active']
    list_filter = ['is_active']
    search_fields = ['name', 'code']


@admin.register(ShipUnit)
class ShipUnitAdmin(admin.ModelAdmin):
    list_display = ['hull_number', 'name', 'unit_type', 'ship_class', 'status', 'home_port', 'commanding_officer']
    list_filter = ['unit_type', 'status', 'ship_class', 'home_port']
    search_fields = ['name', 'hull_number']
    readonly_fields = ['is_ship', 'is_due_for_docking', 'days_until_docking']


@admin.register(Deployment)
class DeploymentAdmin(admin.ModelAdmin):
    list_display = ['ship', 'title', 'deployment_type', 'start_date', 'end_date', 'status']
    list_filter = ['deployment_type', 'status', 'start_date']
    search_fields = ['title', 'ship__name']
    readonly_fields = ['duration_days', 'is_current']


@admin.register(MaintenanceSchedule)
class MaintenanceScheduleAdmin(admin.ModelAdmin):
    list_display = ['ship', 'title', 'maintenance_type', 'scheduled_start', 'scheduled_end', 'status']
    list_filter = ['maintenance_type', 'status', 'scheduled_start']
    search_fields = ['title', 'ship__name']
    readonly_fields = ['is_overdue', 'duration_days']
