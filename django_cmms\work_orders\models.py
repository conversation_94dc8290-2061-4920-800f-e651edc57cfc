"""
Work Order models for CMMS application.
"""
from django.db import models
from django.urls import reverse
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.conf import settings
from decimal import Decimal


class Priority(models.Model):
    """
    Work order priority levels.
    """
    level = models.PositiveIntegerField(
        unique=True,
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        help_text="Priority level (1=highest, 10=lowest)"
    )
    
    name = models.CharField(
        max_length=50,
        help_text="Priority name"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Priority description"
    )
    
    color = models.CharField(
        max_length=7,
        default='#007bff',
        help_text="Color for visual identification (hex code)"
    )
    
    response_time_hours = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Expected response time in hours"
    )
    
    is_active = models.Bo<PERSON>anField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'work_orders_priority'
        ordering = ['level']
        verbose_name_plural = 'Priorities'
        
    def __str__(self):
        return f"P{self.level} - {self.name}"


class WorkOrderType(models.Model):
    """
    Types of work orders (Corrective, Preventive, Emergency, etc.).
    """
    name = models.CharField(
        max_length=50,
        unique=True,
        help_text="Work order type name"
    )
    
    code = models.CharField(
        max_length=10,
        unique=True,
        help_text="Short code for the work order type"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Description of this work order type"
    )
    
    color = models.CharField(
        max_length=7,
        default='#007bff',
        help_text="Color for visual identification"
    )
    
    requires_approval = models.BooleanField(
        default=True,
        help_text="Whether this type requires approval"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'work_orders_type'
        ordering = ['name']
        
    def __str__(self):
        return f"{self.code} - {self.name}"


class WorkOrder(models.Model):
    """
    Main work order model with comprehensive tracking.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('pending_approval', 'Pending Approval'),
        ('approved', 'Approved'),
        ('assigned', 'Assigned'),
        ('in_progress', 'In Progress'),
        ('on_hold', 'On Hold'),
        ('completed', 'Completed'),
        ('closed', 'Closed'),
        ('cancelled', 'Cancelled'),
        ('rejected', 'Rejected'),
    ]
    
    # Basic Information
    wo_number = models.CharField(
        max_length=20,
        unique=True,
        help_text="Work order number"
    )
    
    title = models.CharField(
        max_length=200,
        help_text="Brief description of the work"
    )
    
    description = models.TextField(
        help_text="Detailed description of the work required"
    )
    
    work_type = models.ForeignKey(
        WorkOrderType,
        on_delete=models.PROTECT,
        related_name='work_orders',
        help_text="Type of work order"
    )
    
    priority = models.ForeignKey(
        Priority,
        on_delete=models.PROTECT,
        related_name='work_orders',
        help_text="Work order priority"
    )
    
    # Equipment and Location
    equipment = models.ForeignKey(
        'equipment.Equipment',
        on_delete=models.PROTECT,
        related_name='work_orders',
        blank=True,
        null=True,
        help_text="Equipment this work order is for"
    )
    
    location = models.ForeignKey(
        'equipment.Location',
        on_delete=models.PROTECT,
        related_name='work_orders',
        blank=True,
        null=True,
        help_text="Location where work is to be performed"
    )
    
    # People
    requestor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='requested_work_orders',
        help_text="Person who requested the work"
    )
    
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='assigned_work_orders',
        help_text="Person assigned to perform the work"
    )
    
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='approved_work_orders',
        help_text="Person who approved the work order"
    )
    
    # Status and Dates
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text="Current status of the work order"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    submitted_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the work order was submitted"
    )
    
    approved_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the work order was approved"
    )
    
    assigned_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the work order was assigned"
    )
    
    started_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When work actually started"
    )
    
    completed_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When work was completed"
    )
    
    closed_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the work order was closed"
    )
    
    due_date = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the work should be completed"
    )
    
    # Work Details
    estimated_hours = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Estimated hours to complete"
    )
    
    actual_hours = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Actual hours spent"
    )
    
    estimated_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Estimated cost"
    )
    
    actual_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Actual cost"
    )
    
    # Work Completion
    work_performed = models.TextField(
        blank=True,
        help_text="Description of work performed"
    )
    
    parts_used = models.TextField(
        blank=True,
        help_text="Parts and materials used"
    )
    
    completion_notes = models.TextField(
        blank=True,
        help_text="Notes about completion"
    )
    
    # Additional Information
    safety_notes = models.TextField(
        blank=True,
        help_text="Safety considerations and notes"
    )
    
    coordination_notes = models.TextField(
        blank=True,
        help_text="Coordination instructions"
    )
    
    is_emergency = models.BooleanField(
        default=False,
        help_text="Whether this is an emergency work order"
    )
    
    is_preventive = models.BooleanField(
        default=False,
        help_text="Whether this is preventive maintenance"
    )
    
    parent_work_order = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='child_work_orders',
        help_text="Parent work order (for sub-tasks)"
    )
    
    class Meta:
        db_table = 'work_orders_workorder'
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.wo_number} - {self.title}"
    
    def get_absolute_url(self):
        return reverse('work_orders:detail', kwargs={'pk': self.pk})
    
    @property
    def is_overdue(self):
        """Check if work order is overdue."""
        if not self.due_date:
            return False
        return self.due_date < timezone.now() and self.status not in ['completed', 'closed', 'cancelled']
    
    @property
    def days_overdue(self):
        """Calculate days overdue."""
        if not self.is_overdue:
            return 0
        return (timezone.now() - self.due_date).days
    
    @property
    def time_to_complete(self):
        """Calculate time taken to complete work order."""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None
    
    @property
    def can_be_started(self):
        """Check if work order can be started."""
        return self.status in ['approved', 'assigned']
    
    @property
    def can_be_completed(self):
        """Check if work order can be completed."""
        return self.status == 'in_progress'
    
    def save(self, *args, **kwargs):
        """Override save to handle status transitions."""
        # Auto-generate work order number if not provided
        if not self.wo_number:
            self.wo_number = self.generate_wo_number()
        
        # Set timestamps based on status changes
        if self.pk:
            old_instance = WorkOrder.objects.get(pk=self.pk)
            if old_instance.status != self.status:
                self.handle_status_change(old_instance.status, self.status)
        
        super().save(*args, **kwargs)
    
    def generate_wo_number(self):
        """Generate a unique work order number."""
        from datetime import datetime
        year = datetime.now().year
        # Get the last work order number for this year
        last_wo = WorkOrder.objects.filter(
            wo_number__startswith=f"WO{year}"
        ).order_by('-wo_number').first()
        
        if last_wo:
            last_num = int(last_wo.wo_number.split('-')[-1])
            new_num = last_num + 1
        else:
            new_num = 1
        
        return f"WO{year}-{new_num:04d}"
    
    def handle_status_change(self, old_status, new_status):
        """Handle status change timestamps."""
        now = timezone.now()
        
        if new_status == 'submitted' and not self.submitted_at:
            self.submitted_at = now
        elif new_status == 'approved' and not self.approved_at:
            self.approved_at = now
        elif new_status == 'assigned' and not self.assigned_at:
            self.assigned_at = now
        elif new_status == 'in_progress' and not self.started_at:
            self.started_at = now
        elif new_status == 'completed' and not self.completed_at:
            self.completed_at = now
        elif new_status == 'closed' and not self.closed_at:
            self.closed_at = now
