<?
 function create_combo($database, $table, $control_name, $selected, $first) {
  global $userName ;
  global $password ;
  global $hostName ;

/*  personnel_selection.inc.php - <PERSON>@pictsweet.com   */
/*  function for creating HTML code for a form selection control where  */
/*  each option is a row in a mysql database table */ 
/*  arguments are (database name to query, table to query, name of the control) */                    

/*  connect to the database */
 $connection=mysql_connect($hostName,$userName,$password) or die('Could not connect to the database server');
 $db = mysql_select_db($database, $connection) or die ("Unable to select database: $database");

/*  make an array to hold the rows  */
 $arr = array();

/*  construct a query */
 $sql = "SELECT id, lname, fname FROM $table ORDER BY lname ASC";

/*  execute the query */
   $query = mysql_query($sql) or die("Query Failed <br> $sql");

/*  package each row of the results in an array */
 while($op_row = mysql_fetch_object($query)) {
    array_push($arr, $op_row);
  }

/*  make the HTML code for a selection box of names*/

 $str="<select name=\"$control_name\">";

 /* if a first row was used (i.e. 'pick a mechanic...', make an entry for it*/

 if(isset($first)){$str .= "<option>$first</option>";}

foreach ($arr as $ele){
  
  $str .= "<option value=\"$ele->id\" ";

  if (($ele->id == $selected)){$str .= "selected=\"selected\"";}

   $str .= ">$ele->lname";

   if(!$ele->lname== "" && !$ele->fname== ""){ $str .= ", ";} //if there is no last name do not add a comma

   $str .= "$ele->fname</option>\n";
}

 $str .= "</select> ";

 return $str;
}

?>
