"""
URL configuration for equipment app.
"""
from django.urls import path
from . import views

app_name = 'equipment'

urlpatterns = [
    # Equipment URLs
    path('', views.EquipmentListView.as_view(), name='list'),
    path('tree/', views.EquipmentTreeView.as_view(), name='tree'),
    path('create/', views.EquipmentCreateView.as_view(), name='create'),
    path('<int:pk>/', views.EquipmentDetailView.as_view(), name='detail'),
    path('<int:pk>/edit/', views.EquipmentUpdateView.as_view(), name='edit'),
    path('<int:pk>/delete/', views.EquipmentDeleteView.as_view(), name='delete'),
    
    # Location URLs
    path('locations/', views.LocationListView.as_view(), name='location_list'),
    path('locations/create/', views.LocationCreateView.as_view(), name='location_create'),
    path('locations/<int:pk>/', views.LocationDetailView.as_view(), name='location_detail'),
    path('locations/<int:pk>/edit/', views.LocationUpdateView.as_view(), name='location_edit'),
    
    # AJAX URLs
    path('ajax/equipment-tree/', views.equipment_tree_ajax, name='equipment_tree_ajax'),
    path('ajax/equipment-search/', views.equipment_search_ajax, name='equipment_search_ajax'),
]
