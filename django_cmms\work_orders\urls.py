"""
URL configuration for work_orders app.
"""
from django.urls import path
from . import views

app_name = 'work_orders'

urlpatterns = [
    # Work Order URLs
    path('', views.WorkOrderListView.as_view(), name='list'),
    path('my/', views.MyWorkOrdersView.as_view(), name='my_work_orders'),
    path('create/', views.WorkOrderCreateView.as_view(), name='create'),
    path('<int:pk>/', views.WorkOrderDetailView.as_view(), name='detail'),
    path('<int:pk>/edit/', views.WorkOrderUpdateView.as_view(), name='edit'),
    path('<int:pk>/delete/', views.WorkOrderDeleteView.as_view(), name='delete'),
    
    # Work Order Actions
    path('<int:pk>/assign/', views.AssignWorkOrderView.as_view(), name='assign'),
    path('<int:pk>/start/', views.StartWorkOrderView.as_view(), name='start'),
    path('<int:pk>/complete/', views.CompleteWorkOrderView.as_view(), name='complete'),
    path('<int:pk>/close/', views.CloseWorkOrderView.as_view(), name='close'),
    path('<int:pk>/cancel/', views.CancelWorkOrderView.as_view(), name='cancel'),
    
    # Approval Workflow
    path('pending-approval/', views.PendingApprovalView.as_view(), name='pending_approval'),
    path('<int:pk>/approve/', views.ApproveWorkOrderView.as_view(), name='approve'),
    path('<int:pk>/reject/', views.RejectWorkOrderView.as_view(), name='reject'),
    
    # Bulk Operations
    path('bulk-assign/', views.BulkAssignView.as_view(), name='bulk_assign'),
    path('bulk-update-status/', views.BulkUpdateStatusView.as_view(), name='bulk_update_status'),
    
    # AJAX URLs
    path('ajax/work-order-stats/', views.work_order_stats_ajax, name='work_order_stats_ajax'),
    path('ajax/equipment-work-orders/', views.equipment_work_orders_ajax, name='equipment_work_orders_ajax'),
]
