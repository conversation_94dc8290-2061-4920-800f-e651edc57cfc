"""
Dashboard models for CMMS application.
"""
from django.db import models
from django.conf import settings


class DashboardWidget(models.Model):
    """
    Configurable dashboard widgets for users.
    """
    WIDGET_TYPES = [
        ('work_orders_summary', 'Work Orders Summary'),
        ('equipment_status', 'Equipment Status'),
        ('maintenance_schedule', 'Maintenance Schedule'),
        ('overdue_items', 'Overdue Items'),
        ('recent_activity', 'Recent Activity'),
        ('kpi_metrics', 'KPI Metrics'),
        ('charts', 'Charts and Graphs'),
    ]
    
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='dashboard_widgets'
    )
    
    widget_type = models.CharField(
        max_length=50,
        choices=WIDGET_TYPES,
        help_text="Type of widget"
    )
    
    title = models.CharField(
        max_length=100,
        help_text="Widget title"
    )
    
    position_x = models.PositiveIntegerField(
        default=0,
        help_text="X position on dashboard grid"
    )
    
    position_y = models.PositiveIntegerField(
        default=0,
        help_text="Y position on dashboard grid"
    )
    
    width = models.PositiveIntegerField(
        default=4,
        help_text="Widget width in grid units"
    )
    
    height = models.PositiveIntegerField(
        default=3,
        help_text="Widget height in grid units"
    )
    
    config = models.JSONField(
        default=dict,
        blank=True,
        help_text="Widget configuration as JSON"
    )
    
    is_visible = models.BooleanField(
        default=True,
        help_text="Whether widget is visible"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'dashboard_widget'
        ordering = ['position_y', 'position_x']
        unique_together = ['user', 'widget_type']
        
    def __str__(self):
        return f"{self.user.username} - {self.title}"


class SystemMetric(models.Model):
    """
    System-wide metrics for dashboard display.
    """
    METRIC_TYPES = [
        ('work_orders_open', 'Open Work Orders'),
        ('work_orders_overdue', 'Overdue Work Orders'),
        ('equipment_down', 'Equipment Down'),
        ('maintenance_due', 'Maintenance Due'),
        ('avg_completion_time', 'Average Completion Time'),
        ('cost_this_month', 'Cost This Month'),
        ('efficiency_rating', 'Efficiency Rating'),
    ]
    
    metric_type = models.CharField(
        max_length=50,
        choices=METRIC_TYPES,
        unique=True,
        help_text="Type of metric"
    )
    
    value = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Current metric value"
    )
    
    unit = models.CharField(
        max_length=20,
        blank=True,
        help_text="Unit of measurement"
    )
    
    target_value = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Target value for this metric"
    )
    
    last_updated = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'dashboard_metric'
        ordering = ['metric_type']
        
    def __str__(self):
        return f"{self.get_metric_type_display()}: {self.value} {self.unit}"
    
    @property
    def is_on_target(self):
        """Check if metric is meeting target."""
        if not self.target_value:
            return None
        return self.value <= self.target_value
    
    @property
    def variance_percentage(self):
        """Calculate variance from target as percentage."""
        if not self.target_value or self.target_value == 0:
            return None
        return ((self.value - self.target_value) / self.target_value) * 100
