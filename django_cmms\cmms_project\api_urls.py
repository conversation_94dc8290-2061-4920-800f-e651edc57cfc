"""
API URL configuration for CMMS project.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter

# Import API viewsets
from accounts.api.views import UserViewSet
from equipment.api.views import EquipmentViewSet, LocationViewSet
from work_orders.api.views import WorkOrderViewSet, PriorityViewSet
from maintenance.api.views import MaintenanceTaskViewSet, TroubleCallViewSet

# Create router and register viewsets
router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'equipment', EquipmentViewSet)
router.register(r'locations', LocationViewSet)
router.register(r'work-orders', WorkOrderViewSet)
router.register(r'priorities', PriorityViewSet)
router.register(r'maintenance-tasks', MaintenanceTaskViewSet)
router.register(r'trouble-calls', TroubleCallViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('auth/', include('rest_framework.urls')),
]
