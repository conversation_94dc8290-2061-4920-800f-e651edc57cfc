<h1>Setting up database...</h1>

<?php

include 'config.inc.php';

echo "<h2>Connecting with the database server...</h2>";

$connection = mysqli_connect($hostName, $userName, $password) or die("Failed to connect to the database server using username: $userName");

if($connection != FALSE){echo "Successfully connected";}



$sql['make_db'] = "CREATE DATABASE IF NOT EXISTS $databaseName";

echo "<h2>Creating database named $databaseName...</h2>";

$result = mysqli_query($connection, $sql['make_db']) or die("Failed to create database using SQL string:<br>" .$sql['make_db']. "<br> MySQL reported the following error:<br>" . mysqli_error($connection));

if($result){echo "Successfully created database $databaseName";}

echo "<h2>Selecting $databaseName...</h2>";

$result = mysqli_select_db($connection, $databaseName) or die("Failed to select database $databaseName. <br> MySQL reported the following error:<br>" . mysqli_error($connection));

if($result){echo "Successfully selected $databaseName<br>";}



echo "<h2>Creating Tables...</h2>";

$sql['make_tables'] = [];

$sql['make_tables']['equipment'] = "
CREATE TABLE equipment (
  id int(11) NOT NULL auto_increment,
  parent_id int(11) NOT NULL default '0',
  description varchar(20) NOT NULL default '',
  KEY id (id)
) ENGINE=InnoDB COMMENT='Equipment';";

$sql['make_tables']['groups'] = "
CREATE TABLE groups (
  uname varchar(20) NOT NULL default '',
  passwd varchar(12) NOT NULL default '',
  grp set('clerk','mechanic','lead','supervisor','manager') NOT NULL default '',
  last_login timestamp NOT NULL
) ENGINE=InnoDB COMMENT='Table of groups of users';";

$sql['make_tables']['mechanics'] = "
CREATE TABLE mechanics (
  id int(11) NOT NULL auto_increment,
  lname char(20) NOT NULL default '',
  fname char(20) NOT NULL default '',
  craft set('op','mech','other') NOT NULL default '',
  busy int(4) NOT NULL default '0',
  PRIMARY KEY  (id)
) ENGINE=InnoDB COMMENT='List of repack mechanics';";

$sql['make_tables']['next_wo'] = "
CREATE TABLE next_wo (
  id int(11) NOT NULL default '0'
) ENGINE=InnoDB COMMENT='The next work order number to use';";

$sql['make_tables']['priority'] = "
CREATE TABLE priority (
  priority int(11) NOT NULL default '0',
  description text NOT NULL,
  PRIMARY KEY  (priority)
) ENGINE=InnoDB;";

$sql['make_tables']['queries'] = "
CREATE TABLE queries (
  name varchar(25) NOT NULL default '',
  mode set('work_order','hot_job','equipment') NOT NULL default '',
  caption varchar(25) NOT NULL default '',
  title varchar(50) NOT NULL default '',
  groups text NOT NULL,
  sql_txt text NOT NULL,
  col_attributes text,
  PRIMARY KEY  (name),
  INDEX col_attributes (col_attributes(255)),
  INDEX col_attributes_2 (col_attributes(255))
) ENGINE=InnoDB COMMENT='SQL query store';";

$sql['make_tables']['trouble'] = "
CREATE TABLE trouble_calls (
  hj_id int(11) NOT NULL default '0',
  date date NOT NULL default '0000-00-00',
  short_description varchar(50) NOT NULL default '',
  description text NOT NULL,
  parts text NOT NULL,
  comments text NOT NULL,
  equipment_id int(11) NOT NULL default '0',
  mechanic_id int(4) NOT NULL default '0',
  operator_id int(4) NOT NULL default '0',
  hours int(4) NOT NULL default '0',
  PRIMARY KEY  (hj_id)
) ENGINE=InnoDB COMMENT='Production-down jobs';";

$sql['make_tables']['work_orders'] = "
CREATE TABLE work_orders (
  wo_id int(11) NOT NULL auto_increment,
  ref_no varchar(32) default NULL,
  descriptive_text varchar(50) NOT NULL default '',
  audit_item int(4) default NULL,
  requestor varchar(20) NOT NULL default '',
  approval varchar(20) NOT NULL default '',
  equipment varchar(30) NOT NULL default '',
  description text NOT NULL,
  action text,
  mechanic_id int(11) default NULL,
  priority int(1) NOT NULL default '0',
  submit_date date NOT NULL default '0000-00-00',
  est_hours int(4) default NULL,
  act_hours int(4) default NULL,
  account varchar(15) default NULL,
  complete_date date default NULL,
  coordinating_instructions text,
  needed_date date default NULL,
  wo_status set('Approved','Assigned','Pending Approval','Suspended','Completed','Rejected','Hot Job') NOT NULL default '',
  inspected_by varchar(20) NOT NULL default '',
  updated timestamp NOT NULL,
  PRIMARY KEY  (wo_id)
) ENGINE=InnoDB COMMENT='Repack work orders';";


foreach($sql['make_tables'] as $name => $sql_str)
{
    $result = mysqli_query($connection, $sql_str) or die("Failed to create table $name using SQL string:<br>$sql_str<br> MySQL reported the following error:<br>" . mysqli_error($connection));

    if($result){echo "Successfully created table $name<br>";}
}

echo "<h2>Populating Utility Tables...</h2>";

$sql['data']['next_wo'] = "INSERT INTO next_wo VALUES ($start)";

$sql['data']['priority'] = "INSERT INTO priority VALUES (1, '1 Immediate: Safety /Production Down'),
                            (2, '2 ASAP'),
                            (3, '3 Before specific date'),
                            (4, '4 No priority'),
                            (5, '5 Standing work order');";

$sql['data']['groups'] = "INSERT INTO groups VALUES ('manager', 'manager', 'manager','');";

$sql['data']['work_order'] = "INSERT INTO work_orders VALUES ('',1, 'This is the very first work order', 0, 'manager', '', '', 'This is the very first work order.', NULL, NULL, 0, '2003-08-25', NULL, NULL, NULL, NULL, NULL, NULL, 'Pending Approval', '', 20030825163153);";

$sql['data']['trouble_job'] = "INSERT INTO trouble_calls VALUES (1, '2003-08-25', 'First trouble call record', 'This is the very first trouble call', '', '', 0, 0, 0, 1);";

$sql['data']['queries-01'] = "INSERT INTO queries VALUES ('WR_ALL', 'work_order', 'All Work Request', 'Work Request Pending Approval', 'manager,lead', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.description AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE wo_status = \'Pending Approval\'', 'a:8:{i:0;a:3:{s:5:\"style\";s:21:\"text-align: center;  \";s:5:\"width\";s:2:\"5%\";s:6:\"nowrap\";s:6:\"nowrap\";}i:1;a:1:{s:5:\"width\";s:2:\"7%\";}i:2;a:1:{s:5:\"width\";s:2:\"5%\";}i:3;a:1:{s:5:\"width\";s:3:\"25%\";}i:4;a:1:{s:5:\"width\";s:2:\"5%\";}i:5;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:6;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:7;a:1:{s:5:\"width\";s:2:\"5%\";}}');";

$sql['data']['queries-02'] = "INSERT INTO queries VALUES ('WO_BACKLOG', 'work_order', 'Pending Assignment', 'Work Order Backlog', 'manager,lead', 'SELECT wo.wo_id AS \'W.O.\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Desciption\', wo.wo_status AS \'Status\', wo.submit_date AS \'Submit Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE wo_status LIKE \'Approved\' AND mechanic_id=0', 'a:6:{i:0;a:2:{s:5:\"style\";s:21:\"text-align: center;  \";s:5:\"width\";s:3:\"10%\";}i:1;a:1:{s:5:\"width\";s:2:\"7%\";}i:2;a:1:{s:5:\"width\";s:3:\"25%\";}i:3;a:1:{s:5:\"width\";s:2:\"5%\";}i:4;a:1:{s:5:\"width\";s:2:\"5%\";}i:5;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}}');";

$sql['data']['queries-03'] = "INSERT INTO queries VALUES ('WO_PENDING', 'work_order', 'Active', 'Work Orders Pending Completion', 'manager,lead,mechanic', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.description AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE wo_status LIKE \'assigned\' AND complete_date IS NULL', 'a:8:{i:0;a:3:{s:5:\"style\";s:21:\"text-align: center;  \";s:5:\"width\";s:2:\"5%\";s:6:\"nowrap\";s:6:\"nowrap\";}i:1;a:1:{s:5:\"width\";s:2:\"7%\";}i:2;a:1:{s:5:\"width\";s:2:\"5%\";}i:3;a:1:{s:5:\"width\";s:3:\"25%\";}i:4;a:1:{s:5:\"width\";s:2:\"5%\";}i:5;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:6;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:7;a:1:{s:5:\"width\";s:2:\"5%\";}}');";

$sql['data']['queries-04'] = "INSERT INTO queries VALUES ('WO_SUSPENDED', 'work_order', 'Suspended', 'Suspended Work Orders', 'manager,lead', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.description AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE wo_status LIKE \'Suspended\'', 'a:8:{i:0;a:3:{s:5:\"style\";s:21:\"text-align: center;  \";s:5:\"width\";s:2:\"5%\";s:6:\"nowrap\";s:6:\"nowrap\";}i:1;a:1:{s:5:\"width\";s:2:\"7%\";}i:2;a:1:{s:5:\"width\";s:2:\"5%\";}i:3;a:1:{s:5:\"width\";s:3:\"25%\";}i:4;a:1:{s:5:\"width\";s:2:\"5%\";}i:5;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:6;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:7;a:1:{s:5:\"width\";s:2:\"5%\";}}');";

$sql['data']['queries-05'] = "INSERT INTO queries VALUES ('WO_FINISHED', 'work_order', 'Pending Closeout', 'Completed Work Orders Ready to be Closed Out', 'manager,lead', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.description AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE complete_date IS NOT NULL AND wo_status NOT LIKE \'Completed\' AND wo_status NOT LIKE \'Hot Job\'', 'a:8:{i:0;a:3:{s:5:\"style\";s:21:\"text-align: center;  \";s:5:\"width\";s:2:\"5%\";s:6:\"nowrap\";s:6:\"nowrap\";}i:1;a:1:{s:5:\"width\";s:2:\"7%\";}i:2;a:1:{s:5:\"width\";s:2:\"5%\";}i:3;a:1:{s:5:\"width\";s:3:\"25%\";}i:4;a:1:{s:5:\"width\";s:2:\"5%\";}i:5;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:6;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:7;a:1:{s:5:\"width\";s:2:\"5%\";}}');";

$sql['data']['queries-06'] = "INSERT INTO queries VALUES ('WO_ALL', 'work_order', 'All Work Orders', 'All Work Orders', 'manager,lead', 'SELECT wo.wo_id AS \'Work Order\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority', 'a:8:{i:0;a:3:{s:5:\"style\";s:21:\"text-align: center;  \";s:5:\"width\";s:2:\"5%\";s:6:\"nowrap\";s:6:\"nowrap\";}i:1;a:1:{s:5:\"width\";s:2:\"7%\";}i:2;a:1:{s:5:\"width\";s:2:\"5%\";}i:3;a:1:{s:5:\"width\";s:3:\"25%\";}i:4;a:1:{s:5:\"width\";s:2:\"5%\";}i:5;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:6;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:7;a:1:{s:5:\"width\";s:2:\"5%\";}}');";

$sql['data']['queries-07'] = "INSERT INTO queries VALUES ('RECENT_ACTIVITY', 'work_order', 'Recent', 'The 10 Most Recent System Activites', 'manager', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority ORDER BY updated DESC LIMIT 10', 'a:8:{i:0;a:3:{s:5:\"style\";s:21:\"text-align: center;  \";s:5:\"width\";s:2:\"5%\";s:6:\"nowrap\";s:6:\"nowrap\";}i:1;a:1:{s:5:\"width\";s:2:\"7%\";}i:2;a:1:{s:5:\"width\";s:2:\"5%\";}i:3;a:1:{s:5:\"width\";s:3:\"25%\";}i:4;a:1:{s:5:\"width\";s:2:\"5%\";}i:5;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:6;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:7;a:1:{s:5:\"width\";s:2:\"5%\";}}');";

$sql['data']['queries-08'] = "INSERT INTO queries VALUES ('NEW_TO_YOU', 'work_order', 'While you were out', 'System Activity Since Your Last Login', 'manager,lead', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE updated > \" . \$_SESSION[\'last_login\'] . \"', 'a:8:{i:0;a:2:{s:5:\"style\";s:21:\"text-align: center;  \";s:5:\"width\";s:2:\"8%\";}i:1;a:1:{s:5:\"width\";s:2:\"7%\";}i:2;a:1:{s:5:\"width\";s:2:\"5%\";}i:3;a:1:{s:5:\"width\";s:3:\"25%\";}i:4;a:1:{s:5:\"width\";s:2:\"5%\";}i:5;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:6;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:7;a:1:{s:5:\"width\";s:2:\"5%\";}}')";

$sql['data']['queries-09'] = "INSERT INTO queries VALUES ('WO_AUDIT', 'work_order', 'Audit Items', 'Work Orders Related To Audits', 'manager, lead', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE audit_item = 1 ', 'a:8:{i:0;a:3:{s:5:\"style\";s:21:\"text-align: center;  \";s:5:\"width\";s:2:\"5%\";s:6:\"nowrap\";s:6:\"nowrap\";}i:1;a:1:{s:5:\"width\";s:2:\"7%\";}i:2;a:1:{s:5:\"width\";s:2:\"5%\";}i:3;a:1:{s:5:\"width\";s:3:\"25%\";}i:4;a:1:{s:5:\"width\";s:2:\"5%\";}i:5;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:6;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:7;a:1:{s:5:\"width\";s:2:\"5%\";}}');";

$sql['data']['queries-10'] = "INSERT INTO queries VALUES ('WO_RECENT_CLOSED', 'work_order', 'Recently Closed', 'The 10 Most Recently Closed Work Orders', 'manager', 'SELECT wo.wo_id AS \'WO\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE wo_status = \'Completed\' ORDER BY  complete_date DESC LIMIT 0,10', 'a:8:{i:0;a:2:{s:5:\"style\";s:21:\"text-align: center;  \";s:5:\"width\";s:2:\"5%\";}i:1;a:1:{s:5:\"width\";s:2:\"7%\";}i:2;a:1:{s:5:\"width\";s:2:\"5%\";}i:3;a:1:{s:5:\"width\";s:3:\"25%\";}i:4;a:1:{s:5:\"width\";s:2:\"5%\";}i:5;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:6;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:7;a:1:{s:5:\"width\";s:2:\"5%\";}}');";

$sql['data']['queries-11'] = "INSERT INTO queries VALUES ('HJ_ALL', 'hot_job', 'All Trouble Calls', 'All Production-Down Jobs', 'manager,lead', 'SELECT hj_id AS \'Trouble Call\', tc.date AS \'Date\', description AS \'Summary\' , mech.lname AS \'Mechanic\', op.lname AS \'Operator\' FROM trouble_calls AS tc LEFT  JOIN mechanics AS mech ON mech.id = tc.mechanic_id LEFT  JOIN mechanics AS op ON op.id = tc.operator_id ORDER BY Date DESC', 'a:5:{i:0;a:2:{s:5:\"style\";s:19:\"text-align: center;\";s:6:\"nowrap\";s:6:\"nowrap\";}i:1;a:0:{}i:2;a:0:{}i:3;a:0:{}i:4;a:0:{}}');";

$sql['data']['queries-12'] = "INSERT INTO queries VALUES ('HJ_RECENT', 'hot_job', 'Recent Trouble Calls', 'Recent Production-Down Jobs', 'manager,lead', 'SELECT hj_id AS \'Trouble Call\', tc.date AS \'Date\', description AS \'Summary\' , mech.lname AS \'Mechanic\', op.lname AS \'Operator\' FROM trouble_calls AS tc LEFT  JOIN mechanics AS mech ON mech.id = tc.mechanic_id LEFT  JOIN mechanics AS op ON op.id = tc.operator_id ORDER BY Date DESC LIMIT 0, 13', 'a:5:{i:0;a:2:{s:5:\"style\";s:19:\"text-align: center;\";s:6:\"nowrap\";s:6:\"nowrap\";}i:1;a:0:{}i:2;a:0:{}i:3;a:0:{}i:4;a:0:{}}');";

$sql['data']['queries-13'] = "INSERT INTO queries VALUES ('WO_SEARCH', 'work_order', 'Search Results', 'Search Results', 'manager,lead', 'SELECT wo.wo_id AS \'Work Order\', wo.priority AS \'Priority\', wo.equipment AS \'Equipment\', wo.descriptive_text AS \'Description\', wo.wo_status AS \'Status\',   wo.submit_date AS \'Submit Date\', wo.complete_date AS \'Completed Date\', m.lname AS \'Mechanic\', m.fname, m.id, p.description as \'Priority\' , m.lname, m.fname, m.id, p.description as description FROM work_orders AS wo LEFT JOIN mechanics AS m ON wo.mechanic_id = m.id LEFT JOIN priority AS p ON wo.priority = p.priority WHERE \" . \$_REQUEST[\'found\'] . \"', 'a:8:{i:0;a:3:{s:5:\"style\";s:21:\"text-align: center;  \";s:5:\"width\";s:2:\"5%\";s:6:\"nowrap\";s:6:\"nowrap\";}i:1;a:1:{s:5:\"width\";s:2:\"7%\";}i:2;a:1:{s:5:\"width\";s:2:\"5%\";}i:3;a:1:{s:5:\"width\";s:3:\"25%\";}i:4;a:1:{s:5:\"width\";s:2:\"5%\";}i:5;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:6;a:2:{s:5:\"width\";s:3:\"10%\";s:5:\"style\";s:19:\"text-align: center;\";}i:7;a:1:{s:5:\"width\";s:2:\"5%\";}}');";

   
foreach($sql['data'] as $name => $sql_str)
{
    $result = mysqli_query($connection, $sql_str) or die("Failed to populate table $name using SQL string:<br>$sql_str<br> MySQL reported the following error:<br>" . mysqli_error($connection));

    if($result){echo "Successfully populated table $name<br>";}
}


echo "<h1>Finished</h1>Now use your favorite MySQL client (such as phpMyAdmin) to populate the mechanics, equipment, and groups table. One user has been setup for you: User=manager Password=manager. If you want to try out Free-CMMS right away try the demo at free-cmms/sourceforge.net";

echo "<h1><a href='./index.php'>Home</h1>";
?>



