from django.contrib import admin
from .models import (
    Rank, Appointment, NavalPersonnel, PersonnelAppointmentHistory,
    PromotionHistory, Award, PersonnelAward, ProfessionalMembership
)


@admin.register(Rank)
class RankAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'commission_type', 'pay_grade', 'age_ceiling', 'max_service_years']
    list_filter = ['commission_type', 'is_active']
    search_fields = ['name', 'code']
    ordering = ['commission_type', 'pay_grade']


@admin.register(Appointment)
class AppointmentAdmin(admin.ModelAdmin):
    list_display = ['code', 'title', 'appointment_type', 'required_rank']
    list_filter = ['appointment_type', 'is_active']
    search_fields = ['title', 'code']


@admin.register(NavalPersonnel)
class NavalPersonnelAdmin(admin.ModelAdmin):
    list_display = ['official_number', 'get_full_name', 'current_rank', 'current_appointment', 'current_unit', 'service_status']
    list_filter = ['current_rank', 'service_status', 'current_unit']
    search_fields = ['official_number', 'user__first_name', 'user__last_name']
    readonly_fields = ['current_age', 'years_in_service', 'seniority_in_rank']
    
    def get_full_name(self, obj):
        return obj.user.get_full_name()
    get_full_name.short_description = 'Full Name'


@admin.register(PersonnelAppointmentHistory)
class PersonnelAppointmentHistoryAdmin(admin.ModelAdmin):
    list_display = ['personnel', 'appointment', 'unit', 'start_date', 'end_date', 'is_current']
    list_filter = ['appointment', 'is_current', 'start_date']
    search_fields = ['personnel__user__first_name', 'personnel__user__last_name']


@admin.register(PromotionHistory)
class PromotionHistoryAdmin(admin.ModelAdmin):
    list_display = ['personnel', 'from_rank', 'to_rank', 'promotion_date', 'promotion_type']
    list_filter = ['promotion_type', 'promotion_date']
    search_fields = ['personnel__user__first_name', 'personnel__user__last_name']


@admin.register(Award)
class AwardAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'award_type', 'precedence']
    list_filter = ['award_type', 'is_active']
    search_fields = ['name', 'code']
    ordering = ['precedence']


@admin.register(PersonnelAward)
class PersonnelAwardAdmin(admin.ModelAdmin):
    list_display = ['personnel', 'award', 'date_awarded', 'awarding_authority']
    list_filter = ['award', 'date_awarded']
    search_fields = ['personnel__user__first_name', 'personnel__user__last_name']


@admin.register(ProfessionalMembership)
class ProfessionalMembershipAdmin(admin.ModelAdmin):
    list_display = ['personnel', 'organization_name', 'membership_type', 'date_joined', 'is_active']
    list_filter = ['membership_type', 'is_active']
    search_fields = ['personnel__user__first_name', 'personnel__user__last_name', 'organization_name']
