<h1>Setting up database...</h1>

<?php

include 'config.inc.php';

echo "<h2>Connecting with the database server...</h2>";

// Try different connection methods
try {
    // Method 1: Using mysqli with explicit port
    $connection = mysqli_connect($hostName, $userName, $password, '', 3306);
    
    if (!$connection) {
        throw new Exception("Failed to connect using method 1: " . mysqli_connect_error());
    }
    
    echo "Successfully connected using method 1<br>";
} catch (Exception $e) {
    try {
        // Method 2: Using PDO
        $dsn = "mysql:host=$hostName;port=3306";
        $pdo = new PDO($dsn, $userName, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "Successfully connected using PDO<br>";
        
        // Create database if it doesn't exist
        $sql = "CREATE DATABASE IF NOT EXISTS $databaseName";
        $pdo->exec($sql);
        echo "Successfully created database $databaseName<br>";
        
        // Select the database
        $pdo->exec("USE $databaseName");
        echo "Successfully selected database $databaseName<br>";
        
        // Create tables and populate them
        echo "<h2>Creating Tables...</h2>";
        
        // Equipment table
        $sql = "CREATE TABLE IF NOT EXISTS equipment (
          id int(11) NOT NULL auto_increment,
          parent_id int(11) NOT NULL default '0',
          description varchar(20) NOT NULL default '',
          KEY id (id)
        ) ENGINE=InnoDB COMMENT='Equipment'";
        $pdo->exec($sql);
        echo "Successfully created table equipment<br>";
        
        // Groups table
        $sql = "CREATE TABLE IF NOT EXISTS groups (
          uname varchar(20) NOT NULL default '',
          passwd varchar(12) NOT NULL default '',
          grp set('clerk','mechanic','lead','supervisor','manager') NOT NULL default '',
          last_login timestamp NOT NULL
        ) ENGINE=InnoDB COMMENT='Table of groups of users'";
        $pdo->exec($sql);
        echo "Successfully created table groups<br>";
        
        // Insert default user
        $sql = "INSERT INTO groups VALUES ('manager', 'manager', 'manager', NOW())";
        $pdo->exec($sql);
        echo "Successfully added default user (manager/manager)<br>";
        
        echo "<h1>Finished</h1>Basic setup is complete. You can now <a href='index.php'>login to the application</a> using:<br><br>
        Username: manager<br>
        Password: manager<br><br>
        For full functionality, you'll need to run the complete setup script or manually create the remaining tables.";
        
        exit;
    } catch (PDOException $e) {
        echo "Failed to connect using PDO: " . $e->getMessage() . "<br>";
    }
}

// If we get here, we're using the mysqli connection

$sql['make_db'] = "CREATE DATABASE IF NOT EXISTS $databaseName";

echo "<h2>Creating database named $databaseName...</h2>";

$result = mysqli_query($connection, $sql['make_db']) or die("Failed to create database using SQL string:<br>" .$sql['make_db']. "<br> MySQL reported the following error:<br>" . mysqli_error($connection));

if($result){echo "Successfully created database $databaseName";}

echo "<h2>Selecting $databaseName...</h2>";

$result = mysqli_select_db($connection, $databaseName) or die("Failed to select database $databaseName. <br> MySQL reported the following error:<br>" . mysqli_error($connection));

if($result){echo "Successfully selected $databaseName<br>";}

// Create minimal tables for basic functionality
echo "<h2>Creating Basic Tables...</h2>";

$sql = "CREATE TABLE IF NOT EXISTS groups (
  uname varchar(20) NOT NULL default '',
  passwd varchar(12) NOT NULL default '',
  grp set('clerk','mechanic','lead','supervisor','manager') NOT NULL default '',
  last_login timestamp NOT NULL
) ENGINE=InnoDB COMMENT='Table of groups of users'";

$result = mysqli_query($connection, $sql) or die("Failed to create table groups using SQL string:<br>$sql<br> MySQL reported the following error:<br>" . mysqli_error($connection));

if($result){echo "Successfully created table groups<br>";}

// Insert default user
$sql = "INSERT INTO groups VALUES ('manager', 'manager', 'manager', NOW())";
$result = mysqli_query($connection, $sql) or die("Failed to add default user: " . mysqli_error($connection));

if($result){echo "Successfully added default user (manager/manager)<br>";}

echo "<h1>Finished</h1>Basic setup is complete. You can now <a href='index.php'>login to the application</a> using:<br><br>
Username: manager<br>
Password: manager<br><br>
For full functionality, you'll need to run the complete setup script or manually create the remaining tables.";
?>
