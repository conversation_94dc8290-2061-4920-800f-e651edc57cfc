<?php
include('config.inc.php');
session_start();

if (!isset($_SESSION['group'])) {
    header("Location: auth.php");
    exit;
}

try {
    // Connect to the database using PDO
    $pdo = new PDO("mysql:host=$hostName;dbname=$databaseName", $userName, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    if (!empty($_GET['query_name'])) {
        $_SESSION['query_name'] = $_GET['query_name'];
    }

    $query_name = $_SESSION['query_name'] ?? '';
    if (empty($query_name)) {
        echo "<h2>Please select a list from the navigation pane on the left.</h2>";
        exit;
    }

    $stmt = $pdo->prepare("SELECT * FROM queries WHERE name LIKE :query_name");
    $stmt->execute([':query_name' => $query_name]);
    $table = $stmt->fetch(PDO::FETCH_OBJ);

    if (!$table) {
        echo "<h2>Query not found.</h2>";
        exit;
    }

    $sql = $table->sql;
    $stmt = $pdo->query($sql);
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Render table (use a modern frontend framework in the next steps)
    echo "<table border='1'>";
    foreach ($rows as $row) {
        echo "<tr>";
        foreach ($row as $col) {
            echo "<td>" . htmlspecialchars($col) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} catch (PDOException $e) {
    die("Database error: " . $e->getMessage());
}
?>







