"""
Fleet Management models for Naval CMMS application.
Based on analysis of rough file requirements for ships, units, and naval operations.
"""
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.conf import settings


class ShipClass(models.Model):
    """
    Ship classes/types for naval vessels.
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Ship class name"
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Ship class code"
    )

    description = models.TextField(
        blank=True,
        help_text="Ship class description"
    )

    displacement = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Displacement in tons"
    )

    length = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Length in meters"
    )

    beam = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Beam in meters"
    )

    draft = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Draft in meters"
    )

    max_speed = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Maximum speed in knots"
    )

    crew_capacity = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Maximum crew capacity"
    )

    fuel_capacity = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Fuel capacity in liters"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'fleet_ship_class'
        ordering = ['name']
        verbose_name_plural = 'Ship Classes'

    def __str__(self):
        return f"{self.code} - {self.name}"


class ShipUnit(models.Model):
    """
    Individual ships and naval units.
    """
    UNIT_TYPES = [
        ('ship', 'Ship'),
        ('submarine', 'Submarine'),
        ('establishment', 'Naval Establishment'),
        ('base', 'Naval Base'),
        ('station', 'Naval Station'),
        ('facility', 'Naval Facility'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active Service'),
        ('maintenance', 'Under Maintenance'),
        ('docked', 'Docked'),
        ('deployed', 'Deployed'),
        ('reserve', 'Reserve'),
        ('decommissioned', 'Decommissioned'),
    ]

    # Basic Information
    name = models.CharField(
        max_length=100,
        help_text="Ship/unit name (e.g., NNS Aradu, NNS Thunder)"
    )

    hull_number = models.CharField(
        max_length=20,
        unique=True,
        help_text="Hull number or unit identifier"
    )

    unit_type = models.CharField(
        max_length=20,
        choices=UNIT_TYPES,
        default='ship',
        help_text="Type of unit"
    )

    ship_class = models.ForeignKey(
        ShipClass,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='ships',
        help_text="Ship class (e.g., frigate, destroyer, supply ship)"
    )

    # Naval Command Structure
    command = models.CharField(
        max_length=100,
        choices=[
            ('HQ_LOC', 'HQ Logistics Command'),
            ('WESTERN_COMMAND', 'Western Naval Command'),
            ('EASTERN_COMMAND', 'Eastern Naval Command'),
            ('CENTRAL_COMMAND', 'Central Naval Command'),
            ('TRAINING_COMMAND', 'Training Command'),
        ],
        blank=True,
        help_text="Naval command structure"
    )

    # Personnel Capacity
    personnel_capacity = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Maximum personnel capacity"
    )

    current_personnel_count = models.PositiveIntegerField(
        default=0,
        help_text="Current number of assigned personnel"
    )

    # Status and Location
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        help_text="Current operational status"
    )

    home_port = models.ForeignKey(
        'equipment.Location',
        on_delete=models.PROTECT,
        related_name='home_port_ships',
        help_text="Home port or base location"
    )

    current_location = models.ForeignKey(
        'equipment.Location',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='current_location_ships',
        help_text="Current location"
    )

    # Personnel
    commanding_officer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='commanded_ships',
        help_text="Commanding Officer"
    )

    executive_officer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='executive_ships',
        help_text="Executive Officer"
    )

    engineering_officer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='engineering_ships',
        help_text="Chief Engineering Officer"
    )

    # Dates
    commissioned_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of commissioning"
    )

    last_docking_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of last dry docking"
    )

    next_docking_date = models.DateField(
        blank=True,
        null=True,
        help_text="Scheduled next dry docking"
    )

    # Operational Data
    operating_hours = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Total operating hours"
    )

    fuel_consumption_rate = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Fuel consumption rate (liters/hour)"
    )

    # Additional Information
    specifications = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional specifications as JSON"
    )

    notes = models.TextField(
        blank=True,
        help_text="Additional notes"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'fleet_ship_unit'
        ordering = ['name']

    def __str__(self):
        return f"{self.hull_number} - {self.name}"

    def get_absolute_url(self):
        return reverse('fleet:ship_detail', kwargs={'pk': self.pk})

    @property
    def is_ship(self):
        """Check if this unit is a ship."""
        return self.unit_type in ['ship', 'submarine']

    @property
    def is_due_for_docking(self):
        """Check if ship is due for docking."""
        if not self.next_docking_date:
            return False
        return self.next_docking_date <= timezone.now().date()

    @property
    def days_until_docking(self):
        """Calculate days until next docking."""
        if not self.next_docking_date:
            return None
        return (self.next_docking_date - timezone.now().date()).days


class Deployment(models.Model):
    """
    Ship deployments and missions.
    """
    DEPLOYMENT_TYPES = [
        ('patrol', 'Patrol'),
        ('exercise', 'Exercise'),
        ('mission', 'Mission'),
        ('training', 'Training'),
        ('maintenance', 'Maintenance'),
        ('port_visit', 'Port Visit'),
    ]

    STATUS_CHOICES = [
        ('planned', 'Planned'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    ship = models.ForeignKey(
        ShipUnit,
        on_delete=models.CASCADE,
        related_name='deployments',
        help_text="Deployed ship"
    )

    deployment_type = models.CharField(
        max_length=20,
        choices=DEPLOYMENT_TYPES,
        help_text="Type of deployment"
    )

    title = models.CharField(
        max_length=200,
        help_text="Deployment title/name"
    )

    description = models.TextField(
        blank=True,
        help_text="Deployment description"
    )

    start_date = models.DateField(
        help_text="Deployment start date"
    )

    end_date = models.DateField(
        help_text="Deployment end date"
    )

    departure_port = models.ForeignKey(
        'equipment.Location',
        on_delete=models.PROTECT,
        related_name='departure_deployments',
        help_text="Departure port"
    )

    destination = models.ForeignKey(
        'equipment.Location',
        on_delete=models.PROTECT,
        related_name='destination_deployments',
        help_text="Destination"
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='planned',
        help_text="Deployment status"
    )

    estimated_fuel_consumption = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Estimated fuel consumption"
    )

    actual_fuel_consumption = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Actual fuel consumption"
    )

    remarks = models.TextField(
        blank=True,
        help_text="Deployment remarks"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'fleet_deployment'
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.ship.name} - {self.title}"

    @property
    def duration_days(self):
        """Calculate deployment duration in days."""
        return (self.end_date - self.start_date).days

    @property
    def is_current(self):
        """Check if deployment is currently active."""
        today = timezone.now().date()
        return self.start_date <= today <= self.end_date and self.status == 'active'


class MaintenanceSchedule(models.Model):
    """
    Maintenance schedules for ships and equipment.
    """
    MAINTENANCE_TYPES = [
        ('routine', 'Routine Maintenance'),
        ('preventive', 'Preventive Maintenance'),
        ('docking', 'Dry Docking'),
        ('overhaul', 'Major Overhaul'),
        ('inspection', 'Inspection'),
        ('certification', 'Certification'),
    ]

    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('postponed', 'Postponed'),
        ('cancelled', 'Cancelled'),
    ]

    ship = models.ForeignKey(
        ShipUnit,
        on_delete=models.CASCADE,
        related_name='maintenance_schedules',
        help_text="Ship for maintenance"
    )

    maintenance_type = models.CharField(
        max_length=20,
        choices=MAINTENANCE_TYPES,
        help_text="Type of maintenance"
    )

    title = models.CharField(
        max_length=200,
        help_text="Maintenance title"
    )

    description = models.TextField(
        help_text="Maintenance description"
    )

    scheduled_start = models.DateField(
        help_text="Scheduled start date"
    )

    scheduled_end = models.DateField(
        help_text="Scheduled end date"
    )

    actual_start = models.DateField(
        blank=True,
        null=True,
        help_text="Actual start date"
    )

    actual_end = models.DateField(
        blank=True,
        null=True,
        help_text="Actual end date"
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='scheduled',
        help_text="Maintenance status"
    )

    facility = models.ForeignKey(
        'equipment.Location',
        on_delete=models.PROTECT,
        related_name='maintenance_schedules',
        help_text="Maintenance facility"
    )

    estimated_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Estimated cost"
    )

    actual_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Actual cost"
    )

    remarks = models.TextField(
        blank=True,
        help_text="Maintenance remarks"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'fleet_maintenance_schedule'
        ordering = ['scheduled_start']

    def __str__(self):
        return f"{self.ship.name} - {self.title}"

    @property
    def is_overdue(self):
        """Check if maintenance is overdue."""
        if self.status in ['completed', 'cancelled']:
            return False
        return self.scheduled_end < timezone.now().date()

    @property
    def duration_days(self):
        """Calculate scheduled duration in days."""
        return (self.scheduled_end - self.scheduled_start).days
