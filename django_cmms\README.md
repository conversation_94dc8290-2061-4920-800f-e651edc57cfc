# Django CMMS - Computerized Maintenance Management System

A modern, feature-rich CMMS application built with Django, designed to replace and significantly improve upon the legacy PHP-based Free CMMS system.

## 🚀 Features

### Core Functionality
- **Work Order Management**: Complete lifecycle management from creation to closure
- **Equipment Management**: Hierarchical equipment structure with maintenance tracking
- **Preventive Maintenance**: Automated scheduling and tracking
- **Trouble Calls/Hot Jobs**: Emergency maintenance request handling
- **User Management**: Role-based access control with multiple user types
- **Dashboard & Analytics**: Real-time insights and performance metrics

### Key Improvements Over Legacy System
- **Modern Web Framework**: Built with Django 4.2 for security and maintainability
- **Responsive Design**: Mobile-friendly interface using Bootstrap 5
- **REST API**: Full API support for mobile apps and integrations
- **Advanced Security**: Built-in CSRF protection, SQL injection prevention
- **Real-time Notifications**: Instant updates for critical events
- **Comprehensive Reporting**: Advanced analytics and customizable reports
- **File Management**: Document and image attachments
- **Activity Tracking**: Complete audit trail of all actions

### User Roles
- **Clerk**: Basic work order creation and viewing
- **Mechanic**: Work order execution and maintenance tasks
- **Lead Mechanic**: Team coordination and work assignment
- **Supervisor**: Approval workflows and team management
- **Manager**: Strategic oversight and analytics
- **Administrator**: System configuration and user management

## 📋 Requirements

- Python 3.8+
- Django 4.2+
- PostgreSQL (recommended) or SQLite for development
- Redis (for caching and background tasks)
- Node.js (for frontend asset compilation, optional)

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd django_cmms
```

### 2. Create Virtual Environment
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Environment Configuration
Create a `.env` file in the project root:
```env
SECRET_KEY=your-secret-key-here
DEBUG=True
DB_ENGINE=django.db.backends.postgresql
DB_NAME=cmms_db
DB_USER=cmms_user
DB_PASSWORD=your-password
DB_HOST=localhost
DB_PORT=5432
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

### 5. Database Setup
```bash
# Create and run migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Load initial data (optional)
python manage.py loaddata fixtures/initial_data.json
```

### 6. Collect Static Files
```bash
python manage.py collectstatic
```

### 7. Run Development Server
```bash
python manage.py runserver
```

Visit `http://localhost:8000` to access the application.

## 🔧 Configuration

### Database Configuration
For production, use PostgreSQL:
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'cmms_db',
        'USER': 'cmms_user',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

### Background Tasks (Celery)
Start Celery worker for background tasks:
```bash
celery -A cmms_project worker -l info
```

Start Celery beat for scheduled tasks:
```bash
celery -A cmms_project beat -l info
```

## 📱 API Usage

The application provides a comprehensive REST API:

### Authentication
```bash
# Get token
curl -X POST http://localhost:8000/api/v1/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'
```

### Work Orders
```bash
# List work orders
curl -H "Authorization: Token your_token" \
  http://localhost:8000/api/v1/work-orders/

# Create work order
curl -X POST http://localhost:8000/api/v1/work-orders/ \
  -H "Authorization: Token your_token" \
  -H "Content-Type: application/json" \
  -d '{"title": "Fix conveyor belt", "description": "Belt is making noise"}'
```

## 🏗️ Architecture

### Project Structure
```
django_cmms/
├── cmms_project/          # Main project settings
├── accounts/              # User management
├── equipment/             # Equipment and location management
├── work_orders/           # Work order management
├── maintenance/           # Maintenance tasks and trouble calls
├── dashboard/             # Dashboard and analytics
├── reports/               # Reporting functionality
├── templates/             # HTML templates
├── static/                # Static files (CSS, JS, images)
└── media/                 # User uploaded files
```

### Key Models
- **User**: Extended user model with CMMS-specific fields
- **Equipment**: Hierarchical equipment structure using MPTT
- **WorkOrder**: Comprehensive work order tracking
- **MaintenanceTask**: Scheduled maintenance management
- **TroubleCall**: Emergency maintenance requests

## 🔒 Security Features

- CSRF protection on all forms
- SQL injection prevention through ORM
- XSS protection with template auto-escaping
- Secure password hashing with PBKDF2
- Role-based access control
- Session security with configurable timeouts
- File upload validation and sanitization

## 📊 Monitoring & Analytics

### Built-in Metrics
- Work order completion rates
- Equipment downtime tracking
- Maintenance cost analysis
- Response time monitoring
- Resource utilization reports

### Dashboard Widgets
- Work order summary
- Equipment status overview
- Maintenance schedule
- Overdue items alerts
- Performance KPIs

## 🧪 Testing

Run the test suite:
```bash
# Run all tests
python manage.py test

# Run with coverage
coverage run --source='.' manage.py test
coverage report
coverage html
```

## 🚀 Deployment

### Production Checklist
- [ ] Set `DEBUG = False`
- [ ] Configure secure database
- [ ] Set up Redis for caching
- [ ] Configure email settings
- [ ] Set up SSL/HTTPS
- [ ] Configure static file serving
- [ ] Set up backup procedures
- [ ] Configure monitoring

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d
```

## 📚 Documentation

- [User Guide](docs/user_guide.md)
- [API Documentation](docs/api.md)
- [Administrator Guide](docs/admin_guide.md)
- [Development Guide](docs/development.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the FAQ section

## 🔄 Migration from Legacy PHP System

### Data Migration
Use the provided migration scripts to transfer data from the old PHP system:
```bash
python manage.py migrate_legacy_data --source=/path/to/php/database
```

### Feature Mapping
| Legacy Feature | Django Equivalent | Improvements |
|---------------|-------------------|--------------|
| Work Orders | work_orders.WorkOrder | Enhanced workflow, better tracking |
| Equipment Tree | equipment.Equipment (MPTT) | Better performance, more features |
| Hot Jobs | maintenance.TroubleCall | Improved categorization, SLA tracking |
| User Groups | accounts.User (roles) | More granular permissions |
| Reports | reports app | Interactive dashboards, real-time data |

## 🎯 Roadmap

- [ ] Mobile application (React Native)
- [ ] Advanced analytics with ML
- [ ] IoT sensor integration
- [ ] Barcode/QR code scanning
- [ ] Inventory management module
- [ ] Vendor management
- [ ] Predictive maintenance algorithms
