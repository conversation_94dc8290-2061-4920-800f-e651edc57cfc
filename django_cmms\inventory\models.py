"""
Enhanced Inventory Management models for Naval CMMS application.
Based on analysis of rough file requirements for POL, spares, and depot management.
"""
from django.db import models
from django.urls import reverse
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.conf import settings
from decimal import Decimal


class Vendor(models.Model):
    """
    Vendor/Supplier management for procurement.
    """
    name = models.CharField(
        max_length=200,
        help_text="Vendor/supplier name"
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Vendor code"
    )
    
    contact_person = models.Char<PERSON>ield(
        max_length=100,
        blank=True,
        help_text="Primary contact person"
    )
    
    email = models.EmailField(
        blank=True,
        help_text="Contact email"
    )
    
    phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Contact phone"
    )
    
    address = models.TextField(
        blank=True,
        help_text="Vendor address"
    )
    
    rating = models.IntegerField(
        choices=[(i, i) for i in range(1, 6)],
        default=3,
        help_text="Vendor performance rating (1-5)"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'inventory_vendor'
        ordering = ['name']
        
    def __str__(self):
        return f"{self.code} - {self.name}"


class ProductCategory(models.Model):
    """
    Product categories for inventory classification.
    """
    CATEGORY_TYPES = [
        ('pol', 'POL (Petroleum, Oil, Lubricants)'),
        ('spares', 'Spare Parts'),
        ('consumables', 'Consumables'),
        ('tools', 'Tools & Equipment'),
        ('safety', 'Safety Equipment'),
        ('ammunition', 'Ammunition'),
        ('provisions', 'Provisions'),
        ('medical', 'Medical Supplies'),
    ]
    
    name = models.CharField(
        max_length=100,
        help_text="Category name"
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Category code"
    )
    
    category_type = models.CharField(
        max_length=20,
        choices=CATEGORY_TYPES,
        help_text="Type of category"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Category description"
    )
    
    is_hazardous = models.BooleanField(
        default=False,
        help_text="Whether items in this category are hazardous"
    )
    
    requires_certification = models.BooleanField(
        default=False,
        help_text="Whether handling requires special certification"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'inventory_product_category'
        ordering = ['name']
        verbose_name_plural = 'Product Categories'
        
    def __str__(self):
        return f"{self.code} - {self.name}"


class Product(models.Model):
    """
    Products/Items in inventory (POL, spares, consumables, etc.).
    """
    UNIT_CHOICES = [
        ('liters', 'Liters'),
        ('drums', 'Drums'),
        ('pieces', 'Pieces'),
        ('kg', 'Kilograms'),
        ('tons', 'Tons'),
        ('meters', 'Meters'),
        ('boxes', 'Boxes'),
        ('sets', 'Sets'),
        ('gallons', 'Gallons'),
        ('barrels', 'Barrels'),
    ]
    
    name = models.CharField(
        max_length=200,
        help_text="Product name"
    )
    
    part_number = models.CharField(
        max_length=100,
        unique=True,
        help_text="Part/product number"
    )
    
    category = models.ForeignKey(
        ProductCategory,
        on_delete=models.PROTECT,
        related_name='products',
        help_text="Product category"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Detailed product description"
    )
    
    unit_of_measure = models.CharField(
        max_length=20,
        choices=UNIT_CHOICES,
        help_text="Unit of measurement"
    )
    
    # Specifications
    manufacturer = models.CharField(
        max_length=100,
        blank=True,
        help_text="Manufacturer name"
    )
    
    model_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="Model number"
    )
    
    specifications = models.JSONField(
        default=dict,
        blank=True,
        help_text="Technical specifications as JSON"
    )
    
    # Inventory Management
    minimum_stock = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Minimum stock level (reorder point)"
    )
    
    maximum_stock = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Maximum stock level"
    )
    
    standard_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Standard unit cost"
    )
    
    # Safety and Compliance
    is_hazardous = models.BooleanField(
        default=False,
        help_text="Whether this product is hazardous"
    )
    
    safety_data_sheet = models.FileField(
        upload_to='inventory/sds/',
        blank=True,
        null=True,
        help_text="Safety Data Sheet (SDS)"
    )
    
    shelf_life_days = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Shelf life in days"
    )
    
    storage_requirements = models.TextField(
        blank=True,
        help_text="Special storage requirements"
    )
    
    # Equipment Compatibility
    compatible_equipment = models.ManyToManyField(
        'equipment.Equipment',
        blank=True,
        related_name='compatible_products',
        help_text="Equipment this product is compatible with"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'inventory_product'
        ordering = ['name']
        
    def __str__(self):
        return f"{self.part_number} - {self.name}"
    
    def get_absolute_url(self):
        return reverse('inventory:product_detail', kwargs={'pk': self.pk})
    
    @property
    def total_stock(self):
        """Calculate total stock across all depots."""
        return self.stock_items.aggregate(
            total=models.Sum('quantity')
        )['total'] or 0
    
    @property
    def is_below_minimum(self):
        """Check if total stock is below minimum level."""
        return self.total_stock < self.minimum_stock


class Depot(models.Model):
    """
    Storage depots/warehouses (LLD, PHLD, SAPLD, CALLD, etc.).
    """
    DEPOT_TYPES = [
        ('main', 'Main Depot'),
        ('regional', 'Regional Depot'),
        ('ship', 'Ship Storage'),
        ('forward', 'Forward Operating Base'),
        ('emergency', 'Emergency Storage'),
    ]
    
    name = models.CharField(
        max_length=100,
        help_text="Depot name"
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Depot code (e.g., LLD, PHLD)"
    )
    
    depot_type = models.CharField(
        max_length=20,
        choices=DEPOT_TYPES,
        default='main',
        help_text="Type of depot"
    )
    
    location = models.ForeignKey(
        'equipment.Location',
        on_delete=models.PROTECT,
        related_name='depots',
        help_text="Physical location"
    )
    
    capacity = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Storage capacity"
    )
    
    capacity_unit = models.CharField(
        max_length=20,
        blank=True,
        help_text="Capacity unit (cubic meters, etc.)"
    )
    
    manager = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='managed_depots',
        help_text="Depot manager"
    )
    
    contact_info = models.JSONField(
        default=dict,
        blank=True,
        help_text="Contact information as JSON"
    )
    
    security_level = models.CharField(
        max_length=20,
        choices=[
            ('low', 'Low Security'),
            ('medium', 'Medium Security'),
            ('high', 'High Security'),
            ('restricted', 'Restricted Access'),
        ],
        default='medium',
        help_text="Security classification"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'inventory_depot'
        ordering = ['name']
        
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def get_absolute_url(self):
        return reverse('inventory:depot_detail', kwargs={'pk': self.pk})
    
    @property
    def current_utilization(self):
        """Calculate current storage utilization percentage."""
        if not self.capacity:
            return None
        
        # This would need to be implemented based on actual volume calculations
        # For now, return a placeholder
        return 0


class StockItem(models.Model):
    """
    Current stock levels of products at specific depots.
    """
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='stock_items',
        help_text="Product"
    )
    
    depot = models.ForeignKey(
        Depot,
        on_delete=models.CASCADE,
        related_name='stock_items',
        help_text="Storage depot"
    )
    
    quantity = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Current quantity in stock"
    )
    
    reserved_quantity = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Quantity reserved for pending requests"
    )
    
    last_updated = models.DateTimeField(auto_now=True)
    
    # Batch/Lot tracking
    batch_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Batch or lot number"
    )
    
    expiry_date = models.DateField(
        blank=True,
        null=True,
        help_text="Expiry date for perishable items"
    )
    
    received_date = models.DateField(
        default=timezone.now,
        help_text="Date received in depot"
    )
    
    unit_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Unit cost for this batch"
    )
    
    class Meta:
        db_table = 'inventory_stock_item'
        unique_together = ['product', 'depot', 'batch_number']
        ordering = ['product__name', 'depot__name']
        
    def __str__(self):
        return f"{self.product.name} @ {self.depot.name}: {self.quantity}"
    
    @property
    def available_quantity(self):
        """Calculate available quantity (total - reserved)."""
        return self.quantity - self.reserved_quantity
    
    @property
    def is_expired(self):
        """Check if item is expired."""
        if not self.expiry_date:
            return False
        return self.expiry_date < timezone.now().date()
    
    @property
    def days_to_expiry(self):
        """Calculate days until expiry."""
        if not self.expiry_date:
            return None
        return (self.expiry_date - timezone.now().date()).days
