"""
Enhanced Inventory Management models for Naval CMMS application.
Based on analysis of rough file requirements for POL, spares, and depot management.
"""
from django.db import models
from django.urls import reverse
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.conf import settings
from decimal import Decimal


class Vendor(models.Model):
    """
    Vendor/Supplier management for procurement.
    """
    name = models.CharField(
        max_length=200,
        help_text="Vendor/supplier name"
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Vendor code"
    )

    contact_person = models.Char<PERSON>ield(
        max_length=100,
        blank=True,
        help_text="Primary contact person"
    )

    email = models.EmailField(
        blank=True,
        help_text="Contact email"
    )

    phone = models.CharField(
        max_length=20,
        blank=True,
        help_text="Contact phone"
    )

    address = models.TextField(
        blank=True,
        help_text="Vendor address"
    )

    rating = models.IntegerField(
        choices=[(i, i) for i in range(1, 6)],
        default=3,
        help_text="Vendor performance rating (1-5)"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'inventory_vendor'
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"


class ProductCategory(models.Model):
    """
    Product categories for inventory classification.
    """
    CATEGORY_TYPES = [
        ('pol', 'POL (Petroleum, Oil, Lubricants)'),
        ('spares', 'Spare Parts'),
        ('consumables', 'Consumables'),
        ('tools', 'Tools & Equipment'),
        ('safety', 'Safety Equipment'),
        ('ammunition', 'Ammunition'),
        ('provisions', 'Provisions'),
        ('medical', 'Medical Supplies'),
    ]

    name = models.CharField(
        max_length=100,
        help_text="Category name"
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Category code"
    )

    category_type = models.CharField(
        max_length=20,
        choices=CATEGORY_TYPES,
        help_text="Type of category"
    )

    description = models.TextField(
        blank=True,
        help_text="Category description"
    )

    is_hazardous = models.BooleanField(
        default=False,
        help_text="Whether items in this category are hazardous"
    )

    requires_certification = models.BooleanField(
        default=False,
        help_text="Whether handling requires special certification"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'inventory_product_category'
        ordering = ['name']
        verbose_name_plural = 'Product Categories'

    def __str__(self):
        return f"{self.code} - {self.name}"


class Product(models.Model):
    """
    Products/Items in inventory (POL, spares, consumables, etc.).
    """
    UNIT_CHOICES = [
        ('liters', 'Liters'),
        ('drums', 'Drums'),
        ('pieces', 'Pieces'),
        ('kg', 'Kilograms'),
        ('tons', 'Tons'),
        ('meters', 'Meters'),
        ('boxes', 'Boxes'),
        ('sets', 'Sets'),
        ('gallons', 'Gallons'),
        ('barrels', 'Barrels'),
    ]

    name = models.CharField(
        max_length=200,
        help_text="Product name"
    )

    part_number = models.CharField(
        max_length=100,
        unique=True,
        help_text="Part/product number"
    )

    category = models.ForeignKey(
        ProductCategory,
        on_delete=models.PROTECT,
        related_name='products',
        help_text="Product category"
    )

    description = models.TextField(
        blank=True,
        help_text="Detailed product description"
    )

    unit_of_measure = models.CharField(
        max_length=20,
        choices=UNIT_CHOICES,
        help_text="Unit of measurement"
    )

    # Specifications
    manufacturer = models.CharField(
        max_length=100,
        blank=True,
        help_text="Manufacturer name"
    )

    model_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="Model number"
    )

    specifications = models.JSONField(
        default=dict,
        blank=True,
        help_text="Technical specifications as JSON"
    )

    # Inventory Management
    minimum_stock = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Minimum stock level (reorder point)"
    )

    maximum_stock = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Maximum stock level"
    )

    standard_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Standard unit cost"
    )

    # Safety and Compliance
    is_hazardous = models.BooleanField(
        default=False,
        help_text="Whether this product is hazardous"
    )

    safety_data_sheet = models.FileField(
        upload_to='inventory/sds/',
        blank=True,
        null=True,
        help_text="Safety Data Sheet (SDS)"
    )

    shelf_life_days = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Shelf life in days"
    )

    storage_requirements = models.TextField(
        blank=True,
        help_text="Special storage requirements"
    )

    # Equipment Compatibility
    compatible_equipment = models.ManyToManyField(
        'equipment.Equipment',
        blank=True,
        related_name='compatible_products',
        help_text="Equipment this product is compatible with"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'inventory_product'
        ordering = ['name']

    def __str__(self):
        return f"{self.part_number} - {self.name}"

    def get_absolute_url(self):
        return reverse('inventory:product_detail', kwargs={'pk': self.pk})

    @property
    def total_stock(self):
        """Calculate total stock across all depots."""
        return self.stock_items.aggregate(
            total=models.Sum('quantity')
        )['total'] or 0

    @property
    def is_below_minimum(self):
        """Check if total stock is below minimum level."""
        return self.total_stock < self.minimum_stock


class Depot(models.Model):
    """
    Storage depots/warehouses (LLD, PHLD, SAPLD, CALLD, etc.).
    """
    DEPOT_TYPES = [
        ('main', 'Main Depot'),
        ('regional', 'Regional Depot'),
        ('ship', 'Ship Storage'),
        ('forward', 'Forward Operating Base'),
        ('emergency', 'Emergency Storage'),
    ]

    name = models.CharField(
        max_length=100,
        help_text="Depot name"
    )

    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Depot code (e.g., LLD, PHLD)"
    )

    depot_type = models.CharField(
        max_length=20,
        choices=DEPOT_TYPES,
        default='main',
        help_text="Type of depot"
    )

    location = models.ForeignKey(
        'equipment.Location',
        on_delete=models.PROTECT,
        related_name='depots',
        help_text="Physical location"
    )

    capacity = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Storage capacity"
    )

    capacity_unit = models.CharField(
        max_length=20,
        blank=True,
        help_text="Capacity unit (cubic meters, etc.)"
    )

    manager = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='managed_depots',
        help_text="Depot manager"
    )

    contact_info = models.JSONField(
        default=dict,
        blank=True,
        help_text="Contact information as JSON"
    )

    security_level = models.CharField(
        max_length=20,
        choices=[
            ('low', 'Low Security'),
            ('medium', 'Medium Security'),
            ('high', 'High Security'),
            ('restricted', 'Restricted Access'),
        ],
        default='medium',
        help_text="Security classification"
    )

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'inventory_depot'
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"

    def get_absolute_url(self):
        return reverse('inventory:depot_detail', kwargs={'pk': self.pk})

    @property
    def current_utilization(self):
        """Calculate current storage utilization percentage."""
        if not self.capacity:
            return None

        # This would need to be implemented based on actual volume calculations
        # For now, return a placeholder
        return 0


class StockItem(models.Model):
    """
    Current stock levels of products at specific depots.
    """
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='stock_items',
        help_text="Product"
    )

    depot = models.ForeignKey(
        Depot,
        on_delete=models.CASCADE,
        related_name='stock_items',
        help_text="Storage depot"
    )

    quantity = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Current quantity in stock"
    )

    reserved_quantity = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Quantity reserved for pending requests"
    )

    last_updated = models.DateTimeField(auto_now=True)

    # Batch/Lot tracking
    batch_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Batch or lot number"
    )

    expiry_date = models.DateField(
        blank=True,
        null=True,
        help_text="Expiry date for perishable items"
    )

    received_date = models.DateField(
        default=timezone.now,
        help_text="Date received in depot"
    )

    unit_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Unit cost for this batch"
    )

    class Meta:
        db_table = 'inventory_stock_item'
        unique_together = ['product', 'depot', 'batch_number']
        ordering = ['product__name', 'depot__name']

    def __str__(self):
        return f"{self.product.name} @ {self.depot.name}: {self.quantity}"

    @property
    def available_quantity(self):
        """Calculate available quantity (total - reserved)."""
        return self.quantity - self.reserved_quantity

    @property
    def is_expired(self):
        """Check if item is expired."""
        if not self.expiry_date:
            return False
        return self.expiry_date < timezone.now().date()

    @property
    def days_to_expiry(self):
        """Calculate days until expiry."""
        if not self.expiry_date:
            return None
        return (self.expiry_date - timezone.now().date()).days


class StockRequest(models.Model):
    """
    Stock requests from ships/units with automatic stock level triggers.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
        ('issued', 'Issued'),
        ('partially_issued', 'Partially Issued'),
        ('due_out', 'Due Out'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('rejected', 'Rejected'),
    ]

    PRIORITY_CHOICES = [
        (1, 'Emergency'),
        (2, 'Urgent'),
        (3, 'Normal'),
        (4, 'Low'),
    ]

    # Basic Information
    request_id = models.CharField(
        max_length=20,
        unique=True,
        help_text="Stock request ID"
    )

    requested_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='stock_requests',
        help_text="Person making the request"
    )

    requesting_unit = models.ForeignKey(
        'fleet.ShipUnit',
        on_delete=models.PROTECT,
        related_name='stock_requests',
        help_text="Requesting ship/unit"
    )

    supplier_depot = models.ForeignKey(
        Depot,
        on_delete=models.PROTECT,
        related_name='stock_requests',
        help_text="Supplier depot"
    )

    # Request Details
    date_requested = models.DateField(
        default=timezone.now,
        help_text="Date of request"
    )

    date_required = models.DateField(
        help_text="Date when stock is required"
    )

    priority = models.IntegerField(
        choices=PRIORITY_CHOICES,
        default=3,
        help_text="Request priority"
    )

    # Status and Approval
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text="Request status"
    )

    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='approved_stock_requests',
        help_text="Person who approved the request"
    )

    approved_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of approval"
    )

    issued_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='issued_stock_requests',
        help_text="Person who issued the stock"
    )

    received_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='received_stock_requests',
        help_text="Person who received the stock"
    )

    date_issued = models.DateField(
        blank=True,
        null=True,
        help_text="Date stock was issued"
    )

    # Additional Information
    justification = models.TextField(
        help_text="Justification for the request"
    )

    remarks = models.TextField(
        blank=True,
        help_text="Additional remarks"
    )

    # Auto-generated flags
    auto_generated = models.BooleanField(
        default=False,
        help_text="Whether request was auto-generated by system"
    )

    trigger_reason = models.CharField(
        max_length=100,
        blank=True,
        help_text="Reason for auto-generation (e.g., 'Below minimum stock')"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'inventory_stock_request'
        ordering = ['-date_requested']

    def __str__(self):
        return f"{self.request_id} - {self.requesting_unit.name}"

    def get_absolute_url(self):
        return reverse('inventory:request_detail', kwargs={'pk': self.pk})

    @property
    def is_overdue(self):
        """Check if request is overdue."""
        if self.status in ['completed', 'cancelled', 'rejected']:
            return False
        return self.date_required < timezone.now().date()

    @property
    def days_overdue(self):
        """Calculate days overdue."""
        if not self.is_overdue:
            return 0
        return (timezone.now().date() - self.date_required).days

    def generate_request_id(self):
        """Generate unique request ID."""
        from datetime import datetime
        year = datetime.now().year
        last_request = StockRequest.objects.filter(
            request_id__startswith=f"SR{year}"
        ).order_by('-request_id').first()

        if last_request:
            last_num = int(last_request.request_id.split('-')[-1])
            new_num = last_num + 1
        else:
            new_num = 1

        return f"SR{year}-{new_num:04d}"


class StockRequestItem(models.Model):
    """
    Individual items in a stock request.
    """
    stock_request = models.ForeignKey(
        StockRequest,
        on_delete=models.CASCADE,
        related_name='items',
        help_text="Stock request"
    )

    product = models.ForeignKey(
        Product,
        on_delete=models.PROTECT,
        related_name='request_items',
        help_text="Requested product"
    )

    quantity_requested = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Quantity requested"
    )

    quantity_approved = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Quantity approved"
    )

    quantity_issued = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Quantity issued so far"
    )

    # Due Out Information
    is_due_out = models.BooleanField(
        default=False,
        help_text="Whether item is on due out (not available)"
    )

    due_out_date = models.DateField(
        blank=True,
        null=True,
        help_text="Expected availability date for due out items"
    )

    due_out_reason = models.CharField(
        max_length=200,
        blank=True,
        help_text="Reason for due out status"
    )

    # Additional Information
    justification = models.TextField(
        blank=True,
        help_text="Justification for this item"
    )

    remarks = models.TextField(
        blank=True,
        help_text="Remarks about this item"
    )

    class Meta:
        db_table = 'inventory_stock_request_item'
        unique_together = ['stock_request', 'product']

    def __str__(self):
        return f"{self.product.name} - {self.quantity_requested} {self.product.unit_of_measure}"

    @property
    def quantity_pending(self):
        """Calculate quantity still pending."""
        approved = self.quantity_approved or self.quantity_requested
        return approved - self.quantity_issued

    @property
    def is_fully_issued(self):
        """Check if item is fully issued."""
        approved = self.quantity_approved or self.quantity_requested
        return self.quantity_issued >= approved


class DueOutForm(models.Model):
    """
    Due out forms for tracking unavailable stock items.
    """
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('fulfilled', 'Fulfilled'),
        ('cancelled', 'Cancelled'),
        ('expired', 'Expired'),
    ]

    form_number = models.CharField(
        max_length=20,
        unique=True,
        help_text="Due out form number"
    )

    request_item = models.ForeignKey(
        StockRequestItem,
        on_delete=models.CASCADE,
        related_name='due_out_forms',
        help_text="Related request item"
    )

    product = models.ForeignKey(
        Product,
        on_delete=models.PROTECT,
        related_name='due_out_forms',
        help_text="Product on due out"
    )

    quantity = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Quantity on due out"
    )

    requesting_unit = models.ForeignKey(
        'fleet.ShipUnit',
        on_delete=models.PROTECT,
        related_name='due_out_forms',
        help_text="Requesting unit"
    )

    supplier_depot = models.ForeignKey(
        Depot,
        on_delete=models.PROTECT,
        related_name='due_out_forms',
        help_text="Supplier depot"
    )

    # Dates
    date_created = models.DateField(
        default=timezone.now,
        help_text="Date due out form was created"
    )

    expected_availability_date = models.DateField(
        help_text="Expected date when stock will be available"
    )

    actual_fulfillment_date = models.DateField(
        blank=True,
        null=True,
        help_text="Actual date when stock was provided"
    )

    expiry_date = models.DateField(
        blank=True,
        null=True,
        help_text="Due out form expiry date"
    )

    # Status
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        help_text="Due out form status"
    )

    # Tracking
    supplier_reference = models.CharField(
        max_length=100,
        blank=True,
        help_text="Supplier reference number"
    )

    tracking_notes = models.TextField(
        blank=True,
        help_text="Tracking notes and updates"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'inventory_due_out_form'
        ordering = ['-date_created']

    def __str__(self):
        return f"{self.form_number} - {self.product.name}"

    @property
    def is_overdue(self):
        """Check if due out is overdue."""
        if self.status != 'active':
            return False
        return self.expected_availability_date < timezone.now().date()

    @property
    def days_overdue(self):
        """Calculate days overdue."""
        if not self.is_overdue:
            return 0
        return (timezone.now().date() - self.expected_availability_date).days

    def generate_form_number(self):
        """Generate unique due out form number."""
        from datetime import datetime
        year = datetime.now().year
        last_form = DueOutForm.objects.filter(
            form_number__startswith=f"DO{year}"
        ).order_by('-form_number').first()

        if last_form:
            last_num = int(last_form.form_number.split('-')[-1])
            new_num = last_num + 1
        else:
            new_num = 1

        return f"DO{year}-{new_num:04d}"


class StockMovement(models.Model):
    """
    Track all stock movements for audit and inventory management.
    """
    MOVEMENT_TYPES = [
        ('receipt', 'Receipt'),
        ('issue', 'Issue'),
        ('transfer', 'Transfer'),
        ('adjustment', 'Adjustment'),
        ('return', 'Return'),
        ('disposal', 'Disposal'),
        ('loss', 'Loss/Damage'),
    ]

    movement_id = models.CharField(
        max_length=20,
        unique=True,
        help_text="Movement ID"
    )

    product = models.ForeignKey(
        Product,
        on_delete=models.PROTECT,
        related_name='movements',
        help_text="Product moved"
    )

    movement_type = models.CharField(
        max_length=20,
        choices=MOVEMENT_TYPES,
        help_text="Type of movement"
    )

    # Locations
    from_depot = models.ForeignKey(
        Depot,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='outgoing_movements',
        help_text="Source depot"
    )

    to_depot = models.ForeignKey(
        Depot,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='incoming_movements',
        help_text="Destination depot"
    )

    # Quantity and Batch
    quantity = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Quantity moved"
    )

    batch_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Batch number"
    )

    unit_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Unit cost at time of movement"
    )

    # People and References
    authorized_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='authorized_movements',
        help_text="Person who authorized the movement"
    )

    executed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='executed_movements',
        help_text="Person who executed the movement"
    )

    # Related Documents
    related_request = models.ForeignKey(
        StockRequest,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='movements',
        help_text="Related stock request"
    )

    related_work_order = models.ForeignKey(
        'work_orders.WorkOrder',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='stock_movements',
        help_text="Related work order"
    )

    # Additional Information
    reference_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="Reference document number"
    )

    reason = models.TextField(
        help_text="Reason for movement"
    )

    remarks = models.TextField(
        blank=True,
        help_text="Additional remarks"
    )

    movement_date = models.DateTimeField(
        default=timezone.now,
        help_text="Date and time of movement"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'inventory_stock_movement'
        ordering = ['-movement_date']

    def __str__(self):
        return f"{self.movement_id} - {self.movement_type} - {self.product.name}"

    def generate_movement_id(self):
        """Generate unique movement ID."""
        from datetime import datetime
        year = datetime.now().year
        last_movement = StockMovement.objects.filter(
            movement_id__startswith=f"SM{year}"
        ).order_by('-movement_id').first()

        if last_movement:
            last_num = int(last_movement.movement_id.split('-')[-1])
            new_num = last_num + 1
        else:
            new_num = 1

        return f"SM{year}-{new_num:06d}"
