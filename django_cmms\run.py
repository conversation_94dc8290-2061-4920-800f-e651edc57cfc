#!/usr/bin/env python
"""
Quick start script for Django CMMS application.
"""

import os
import sys
import subprocess
from pathlib import Path


def check_requirements():
    """Check if basic requirements are met."""
    print("🔍 Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    
    # Check if manage.py exists
    if not os.path.exists('manage.py'):
        print("❌ manage.py not found. Please run from the Django project root.")
        return False
    
    # Check if virtual environment is activated (recommended)
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected. It's recommended to use a virtual environment.")
        choice = input("Continue anyway? (y/n): ").lower()
        if choice not in ['y', 'yes']:
            return False
    
    print("✅ Requirements check passed")
    return True


def install_dependencies():
    """Install Python dependencies."""
    print("📦 Installing dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_database():
    """Set up the database."""
    print("🗄️  Setting up database...")
    
    try:
        # Run migrations
        subprocess.run([sys.executable, "manage.py", "migrate"], check=True, capture_output=True)
        print("✅ Database setup completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Database setup failed: {e}")
        return False


def create_env_file():
    """Create a basic .env file if it doesn't exist."""
    if os.path.exists('.env'):
        return True
    
    print("📝 Creating .env file...")
    
    import secrets
    import string
    
    # Generate secret key
    alphabet = string.ascii_letters + string.digits + '!@#$%^&*(-_=+)'
    secret_key = ''.join(secrets.choice(alphabet) for i in range(50))
    
    env_content = f"""SECRET_KEY={secret_key}
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
DB_ENGINE=django.db.backends.sqlite3
DB_NAME=db.sqlite3
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print("✅ .env file created")
    return True


def run_server():
    """Start the Django development server."""
    print("🚀 Starting Django development server...")
    print("📍 The application will be available at: http://localhost:8000")
    print("🛑 Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        subprocess.run([sys.executable, "manage.py", "runserver"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed to start: {e}")


def main():
    """Main function to run the application."""
    print("🚀 Django CMMS Quick Start")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Create .env file if needed
    if not create_env_file():
        sys.exit(1)
    
    # Check if this is first run
    if not os.path.exists('db.sqlite3'):
        print("\n🔧 First time setup detected...")
        
        # Install dependencies
        if not install_dependencies():
            print("❌ Setup failed. Please run 'python setup.py' for detailed setup.")
            sys.exit(1)
        
        # Setup database
        if not setup_database():
            print("❌ Database setup failed. Please run 'python setup.py' for detailed setup.")
            sys.exit(1)
        
        print("\n✅ Initial setup completed!")
        print("💡 For full setup with sample data, run: python setup.py")
        print("💡 To create an admin user, run: python manage.py createsuperuser")
    
    # Start the server
    print("\n" + "=" * 40)
    run_server()


if __name__ == "__main__":
    main()
