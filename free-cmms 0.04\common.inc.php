<?PHP

/*
 * This functio takes any english date and manipulates it into 
 * a MySQL date like yyyy-mm-dd
 */

function fix_date($any_date)
{
  if(empty($any_date)) //was a date passed?
    {
      return FALSE;
      exit;
    }

  $any_date = str_replace("-", "/", $any_date); //replace all '-' with '/'
  $unix_time = strtotime($any_date);
  $mysql_date = date("Y-m-d", $unix_time);

  return $mysql_date;
  
  exit;

}

?>