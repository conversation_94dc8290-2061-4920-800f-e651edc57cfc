"""
Equipment and Location models for CMMS application.
"""
from django.db import models
from django.urls import reverse
from django.core.validators import MinValueValidator, MaxValueValidator
from mptt.models import MPTTModel, TreeForeignKey
from decimal import Decimal


class Location(MPTTModel):
    """
    Hierarchical location model using MPTT for efficient tree operations.
    """
    name = models.CharField(
        max_length=100,
        help_text="Location name (e.g., Building A, Floor 2, Room 201)"
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Unique location code"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Detailed description of the location"
    )
    
    parent = TreeForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        help_text="Parent location"
    )
    
    address = models.TextField(
        blank=True,
        help_text="Physical address"
    )
    
    coordinates = models.CharField(
        max_length=50,
        blank=True,
        help_text="GPS coordinates (latitude, longitude)"
    )
    
    is_active = models.<PERSON><PERSON>an<PERSON>ield(
        default=True,
        help_text="Whether this location is active"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class MPTTMeta:
        order_insertion_by = ['name']
    
    class Meta:
        db_table = 'equipment_location'
        ordering = ['tree_id', 'lft']
        
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def get_absolute_url(self):
        return reverse('equipment:location_detail', kwargs={'pk': self.pk})
    
    @property
    def full_path(self):
        """Return the full path from root to this location."""
        ancestors = self.get_ancestors(include_self=True)
        return " > ".join([loc.name for loc in ancestors])


class EquipmentCategory(models.Model):
    """
    Equipment categories for classification.
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Category name"
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Category code"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Category description"
    )
    
    color = models.CharField(
        max_length=7,
        default='#007bff',
        help_text="Color for visual identification (hex code)"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'equipment_category'
        ordering = ['name']
        verbose_name_plural = 'Equipment Categories'
        
    def __str__(self):
        return f"{self.code} - {self.name}"


class Equipment(MPTTModel):
    """
    Hierarchical equipment model with comprehensive maintenance tracking.
    """
    STATUS_CHOICES = [
        ('operational', 'Operational'),
        ('maintenance', 'Under Maintenance'),
        ('repair', 'Under Repair'),
        ('out_of_service', 'Out of Service'),
        ('retired', 'Retired'),
    ]
    
    CRITICALITY_CHOICES = [
        (1, 'Low'),
        (2, 'Medium'),
        (3, 'High'),
        (4, 'Critical'),
    ]
    
    # Basic Information
    name = models.CharField(
        max_length=200,
        help_text="Equipment name or description"
    )
    
    equipment_id = models.CharField(
        max_length=50,
        unique=True,
        help_text="Unique equipment identifier"
    )
    
    category = models.ForeignKey(
        EquipmentCategory,
        on_delete=models.PROTECT,
        related_name='equipment',
        help_text="Equipment category"
    )
    
    parent = TreeForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        help_text="Parent equipment (for sub-components)"
    )
    
    location = models.ForeignKey(
        Location,
        on_delete=models.PROTECT,
        related_name='equipment',
        help_text="Current location"
    )
    
    # Technical Specifications
    manufacturer = models.CharField(
        max_length=100,
        blank=True,
        help_text="Equipment manufacturer"
    )
    
    model = models.CharField(
        max_length=100,
        blank=True,
        help_text="Model number or name"
    )
    
    serial_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="Serial number"
    )
    
    specifications = models.JSONField(
        default=dict,
        blank=True,
        help_text="Technical specifications as JSON"
    )
    
    # Status and Criticality
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='operational',
        help_text="Current operational status"
    )
    
    criticality = models.IntegerField(
        choices=CRITICALITY_CHOICES,
        default=2,
        help_text="Equipment criticality level"
    )
    
    # Dates
    installation_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of installation"
    )
    
    commissioning_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of commissioning"
    )
    
    warranty_expiry = models.DateField(
        blank=True,
        null=True,
        help_text="Warranty expiry date"
    )
    
    # Financial Information
    purchase_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Original purchase cost"
    )
    
    current_value = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Current estimated value"
    )
    
    # Maintenance Information
    maintenance_interval_days = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Preventive maintenance interval in days"
    )
    
    last_maintenance_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of last maintenance"
    )
    
    next_maintenance_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of next scheduled maintenance"
    )
    
    # Additional Information
    notes = models.TextField(
        blank=True,
        help_text="Additional notes and comments"
    )
    
    documentation = models.FileField(
        upload_to='equipment/docs/',
        blank=True,
        null=True,
        help_text="Equipment documentation (manuals, drawings, etc.)"
    )
    
    image = models.ImageField(
        upload_to='equipment/images/',
        blank=True,
        null=True,
        help_text="Equipment photo"
    )
    
    qr_code = models.CharField(
        max_length=100,
        blank=True,
        help_text="QR code for mobile access"
    )
    
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this equipment is active"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class MPTTMeta:
        order_insertion_by = ['name']
    
    class Meta:
        db_table = 'equipment_equipment'
        ordering = ['tree_id', 'lft']
        
    def __str__(self):
        return f"{self.equipment_id} - {self.name}"
    
    def get_absolute_url(self):
        return reverse('equipment:detail', kwargs={'pk': self.pk})
    
    @property
    def full_path(self):
        """Return the full equipment hierarchy path."""
        ancestors = self.get_ancestors(include_self=True)
        return " > ".join([eq.name for eq in ancestors])
    
    @property
    def is_overdue_maintenance(self):
        """Check if equipment is overdue for maintenance."""
        if not self.next_maintenance_date:
            return False
        from django.utils import timezone
        return self.next_maintenance_date < timezone.now().date()
    
    @property
    def maintenance_status(self):
        """Get maintenance status description."""
        if not self.next_maintenance_date:
            return "No schedule"
        
        from django.utils import timezone
        today = timezone.now().date()
        days_until = (self.next_maintenance_date - today).days
        
        if days_until < 0:
            return f"Overdue by {abs(days_until)} days"
        elif days_until == 0:
            return "Due today"
        elif days_until <= 7:
            return f"Due in {days_until} days"
        else:
            return f"Due in {days_until} days"
    
    def calculate_next_maintenance_date(self):
        """Calculate next maintenance date based on interval."""
        if self.maintenance_interval_days and self.last_maintenance_date:
            from datetime import timedelta
            return self.last_maintenance_date + timedelta(days=self.maintenance_interval_days)
        return None
    
    def save(self, *args, **kwargs):
        """Override save to auto-calculate next maintenance date."""
        if self.maintenance_interval_days and self.last_maintenance_date and not self.next_maintenance_date:
            self.next_maintenance_date = self.calculate_next_maintenance_date()
        super().save(*args, **kwargs)
