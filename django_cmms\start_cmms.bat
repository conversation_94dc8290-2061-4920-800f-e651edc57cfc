@echo off
echo ========================================
echo    Django CMMS - Quick Start
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

echo Python found. Starting Django CMMS...
echo.

REM Check if manage.py exists
if not exist "manage.py" (
    echo ERROR: manage.py not found
    echo Please run this script from the Django CMMS project directory
    pause
    exit /b 1
)

REM Run the quick start script
python run.py

echo.
echo Press any key to exit...
pause >nul
