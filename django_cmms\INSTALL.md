# Django CMMS Installation Guide

This guide will help you install and run the Django CMMS application on your system.

## 🎯 Quick Start (Recommended)

### Option 1: Automated Setup
```bash
# Navigate to the project directory
cd django_cmms

# Run the automated setup script
python setup.py

# Start the application
python run.py
```

### Option 2: Quick Run (Minimal Setup)
```bash
# Navigate to the project directory
cd django_cmms

# Quick start with minimal setup
python run.py
```

## 📋 Prerequisites

### Required Software
- **Python 3.8 or higher** - [Download Python](https://www.python.org/downloads/)
- **pip** (usually comes with Python)
- **Git** (optional, for cloning) - [Download Git](https://git-scm.com/)

### Optional (for Production)
- **PostgreSQL** - [Download PostgreSQL](https://www.postgresql.org/download/)
- **Redis** - [Download Redis](https://redis.io/download)

## 🔧 Detailed Installation Steps

### Step 1: Get the Code
If you have the code in a zip file, extract it. If using Git:
```bash
git clone <repository-url>
cd django_cmms
```

### Step 2: Create Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### Step 3: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 4: Configure Environment
Create a `.env` file in the project root:
```env
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (SQLite for development)
DB_ENGINE=django.db.backends.sqlite3
DB_NAME=db.sqlite3

# For PostgreSQL (production):
# DB_ENGINE=django.db.backends.postgresql
# DB_NAME=cmms_db
# DB_USER=cmms_user
# DB_PASSWORD=your_password
# DB_HOST=localhost
# DB_PORT=5432
```

### Step 5: Set Up Database
```bash
# Create database tables
python manage.py migrate

# Create admin user
python manage.py createsuperuser

# Load sample data (optional)
python manage.py shell -c "exec(open('load_sample_data.py').read())"
```

### Step 6: Start the Server
```bash
python manage.py runserver
```

Visit `http://localhost:8000` in your browser.

## 🚀 Production Setup

### Database Configuration (PostgreSQL)
1. Install PostgreSQL
2. Create database and user:
```sql
CREATE DATABASE cmms_db;
CREATE USER cmms_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE cmms_db TO cmms_user;
```

3. Update `.env` file:
```env
DB_ENGINE=django.db.backends.postgresql
DB_NAME=cmms_db
DB_USER=cmms_user
DB_PASSWORD=your_secure_password
DB_HOST=localhost
DB_PORT=5432
```

### Security Settings
For production, update these settings in `.env`:
```env
DEBUG=False
SECRET_KEY=your-very-secure-secret-key
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
```

### Static Files
```bash
python manage.py collectstatic
```

### Background Tasks (Optional)
Install and configure Redis, then start Celery:
```bash
# Start Celery worker
celery -A cmms_project worker -l info

# Start Celery beat (for scheduled tasks)
celery -A cmms_project beat -l info
```

## 🐳 Docker Installation (Alternative)

If you prefer Docker:

1. Create `docker-compose.yml`:
```yaml
version: '3.8'
services:
  web:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    environment:
      - DEBUG=True
    depends_on:
      - db
  
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: cmms_db
      POSTGRES_USER: cmms_user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

2. Run with Docker:
```bash
docker-compose up -d
```

## 🔍 Troubleshooting

### Common Issues

#### "No module named 'django'"
**Solution:** Install dependencies
```bash
pip install -r requirements.txt
```

#### "CSRF verification failed"
**Solution:** Clear browser cookies and cache

#### "Database connection error"
**Solution:** Check database configuration in `.env` file

#### "Permission denied" on Linux/macOS
**Solution:** Make scripts executable
```bash
chmod +x setup.py run.py
```

#### Port 8000 already in use
**Solution:** Use a different port
```bash
python manage.py runserver 8080
```

### Getting Help
1. Check the error message carefully
2. Ensure all prerequisites are installed
3. Verify your `.env` configuration
4. Try running `python setup.py` for a fresh setup

## 📱 First Login

### Default Admin Access
After creating a superuser, you can:
1. Visit `http://localhost:8000/admin/` for admin interface
2. Visit `http://localhost:8000/` for main application
3. Log in with your superuser credentials

### Creating Users
1. Log in as admin
2. Go to Admin Panel → Users
3. Add new users with appropriate roles:
   - **Clerk**: Basic work order creation
   - **Mechanic**: Work execution
   - **Lead**: Team coordination
   - **Supervisor**: Approvals and management
   - **Manager**: Strategic oversight

## 🎯 Next Steps

After installation:
1. **Configure Equipment Categories** - Set up your equipment types
2. **Add Locations** - Create your facility structure
3. **Import Equipment** - Add your equipment inventory
4. **Set Up Users** - Create user accounts with proper roles
5. **Configure Priorities** - Set up work order priorities
6. **Create Work Order Types** - Define your maintenance types
7. **Start Using** - Begin creating work orders and maintenance tasks

## 📚 Additional Resources

- [User Guide](docs/user_guide.md) - How to use the application
- [API Documentation](docs/api.md) - REST API reference
- [Admin Guide](docs/admin_guide.md) - System administration
- [Customization Guide](docs/customization.md) - Customizing the system

## 🆘 Support

If you encounter issues:
1. Check this installation guide
2. Review the troubleshooting section
3. Check the application logs
4. Create an issue with detailed error information

---

**Congratulations!** 🎉 You now have a modern, feature-rich CMMS system running on Django!
