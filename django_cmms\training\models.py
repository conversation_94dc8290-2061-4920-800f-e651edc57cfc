"""
Training and Certification models for Naval CMMS application.
Based on analysis of NAVT ATTRIBUTES ENTITIES document.
"""
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.conf import settings
from datetime import date, timedelta


class TrainingInstitution(models.Model):
    """
    Training institutions and schools.
    """
    INSTITUTION_TYPES = [
        ('naval', 'Naval Institution'),
        ('military', 'Military Institution'),
        ('civilian', 'Civilian Institution'),
        ('manufacturer', 'Manufacturer Training'),
        ('contractor', 'Contractor Training'),
        ('online', 'Online Training'),
    ]
    
    name = models.CharField(
        max_length=200,
        help_text="Institution name"
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Institution code"
    )
    
    institution_type = models.CharField(
        max_length=20,
        choices=INSTITUTION_TYPES,
        help_text="Type of institution"
    )
    
    location = models.CharField(
        max_length=200,
        blank=True,
        help_text="Institution location"
    )
    
    contact_info = models.JSONField(
        default=dict,
        blank=True,
        help_text="Contact information as JSON"
    )
    
    accreditation = models.CharField(
        max_length=200,
        blank=True,
        help_text="Accreditation details"
    )
    
    website = models.URLField(
        blank=True,
        help_text="Institution website"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'training_institution'
        ordering = ['name']
        
    def __str__(self):
        return f"{self.code} - {self.name}"


class CourseCategory(models.Model):
    """
    Categories for organizing training courses.
    """
    name = models.CharField(
        max_length=100,
        help_text="Category name"
    )
    
    code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Category code"
    )
    
    description = models.TextField(
        blank=True,
        help_text="Category description"
    )
    
    color = models.CharField(
        max_length=7,
        default='#007bff',
        help_text="Color for visual identification"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'training_course_category'
        ordering = ['name']
        verbose_name_plural = 'Course Categories'
        
    def __str__(self):
        return f"{self.code} - {self.name}"


class Course(models.Model):
    """
    Training courses and programs.
    """
    COURSE_TYPES = [
        ('technical', 'Technical Training'),
        ('leadership', 'Leadership Training'),
        ('safety', 'Safety Training'),
        ('professional', 'Professional Development'),
        ('certification', 'Certification Course'),
        ('refresher', 'Refresher Training'),
        ('orientation', 'Orientation'),
        ('specialized', 'Specialized Training'),
    ]
    
    DELIVERY_METHODS = [
        ('classroom', 'Classroom'),
        ('online', 'Online'),
        ('hybrid', 'Hybrid'),
        ('practical', 'Practical/Hands-on'),
        ('simulation', 'Simulation'),
        ('on_job', 'On-the-Job Training'),
    ]
    
    title = models.CharField(
        max_length=200,
        help_text="Course title"
    )
    
    code = models.CharField(
        max_length=50,
        unique=True,
        help_text="Course code"
    )
    
    category = models.ForeignKey(
        CourseCategory,
        on_delete=models.PROTECT,
        related_name='courses',
        help_text="Course category"
    )
    
    course_type = models.CharField(
        max_length=20,
        choices=COURSE_TYPES,
        help_text="Type of course"
    )
    
    description = models.TextField(
        help_text="Course description and objectives"
    )
    
    duration_hours = models.PositiveIntegerField(
        help_text="Course duration in hours"
    )
    
    duration_days = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Course duration in days"
    )
    
    delivery_method = models.CharField(
        max_length=20,
        choices=DELIVERY_METHODS,
        default='classroom',
        help_text="Course delivery method"
    )
    
    prerequisites = models.TextField(
        blank=True,
        help_text="Course prerequisites"
    )
    
    learning_objectives = models.TextField(
        blank=True,
        help_text="Learning objectives and outcomes"
    )
    
    syllabus = models.TextField(
        blank=True,
        help_text="Course syllabus and content outline"
    )
    
    # Certification Information
    provides_certification = models.BooleanField(
        default=False,
        help_text="Whether course provides certification"
    )
    
    certification_name = models.CharField(
        max_length=200,
        blank=True,
        help_text="Name of certification provided"
    )
    
    certification_validity_years = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Certification validity in years"
    )
    
    # Requirements
    mandatory_for_ranks = models.ManyToManyField(
        'personnel.Rank',
        blank=True,
        related_name='mandatory_courses',
        help_text="Ranks for which this course is mandatory"
    )
    
    mandatory_for_appointments = models.ManyToManyField(
        'personnel.Appointment',
        blank=True,
        related_name='mandatory_courses',
        help_text="Appointments for which this course is mandatory"
    )
    
    # Cost and Resources
    cost_per_participant = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Cost per participant"
    )
    
    maximum_participants = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Maximum number of participants per session"
    )
    
    required_equipment = models.TextField(
        blank=True,
        help_text="Required equipment and resources"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'training_course'
        ordering = ['title']
        
    def __str__(self):
        return f"{self.code} - {self.title}"
    
    def get_absolute_url(self):
        return reverse('training:course_detail', kwargs={'pk': self.pk})


class TrainingSession(models.Model):
    """
    Scheduled training sessions.
    """
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('confirmed', 'Confirmed'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('postponed', 'Postponed'),
    ]
    
    course = models.ForeignKey(
        Course,
        on_delete=models.PROTECT,
        related_name='sessions',
        help_text="Course being delivered"
    )
    
    session_id = models.CharField(
        max_length=50,
        unique=True,
        help_text="Session identifier"
    )
    
    institution = models.ForeignKey(
        TrainingInstitution,
        on_delete=models.PROTECT,
        related_name='sessions',
        help_text="Training institution"
    )
    
    instructor = models.CharField(
        max_length=200,
        help_text="Instructor name"
    )
    
    instructor_qualifications = models.TextField(
        blank=True,
        help_text="Instructor qualifications"
    )
    
    # Schedule
    start_date = models.DateField(
        help_text="Session start date"
    )
    
    end_date = models.DateField(
        help_text="Session end date"
    )
    
    start_time = models.TimeField(
        blank=True,
        null=True,
        help_text="Daily start time"
    )
    
    end_time = models.TimeField(
        blank=True,
        null=True,
        help_text="Daily end time"
    )
    
    # Location
    location = models.CharField(
        max_length=200,
        help_text="Training location"
    )
    
    venue_details = models.TextField(
        blank=True,
        help_text="Venue details and directions"
    )
    
    # Status and Capacity
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='scheduled',
        help_text="Session status"
    )
    
    max_participants = models.PositiveIntegerField(
        help_text="Maximum participants for this session"
    )
    
    current_enrollment = models.PositiveIntegerField(
        default=0,
        help_text="Current number of enrolled participants"
    )
    
    # Additional Information
    special_requirements = models.TextField(
        blank=True,
        help_text="Special requirements or notes"
    )
    
    materials_provided = models.TextField(
        blank=True,
        help_text="Materials and resources provided"
    )
    
    total_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Total session cost"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'training_session'
        ordering = ['start_date']
        
    def __str__(self):
        return f"{self.session_id} - {self.course.title}"
    
    def get_absolute_url(self):
        return reverse('training:session_detail', kwargs={'pk': self.pk})
    
    @property
    def available_slots(self):
        """Calculate available slots."""
        return self.max_participants - self.current_enrollment
    
    @property
    def is_full(self):
        """Check if session is full."""
        return self.current_enrollment >= self.max_participants
    
    @property
    def duration_days(self):
        """Calculate session duration in days."""
        return (self.end_date - self.start_date).days + 1


class TrainingRecord(models.Model):
    """
    Individual training records for personnel.
    """
    STATUS_CHOICES = [
        ('enrolled', 'Enrolled'),
        ('attending', 'Attending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('withdrawn', 'Withdrawn'),
        ('no_show', 'No Show'),
    ]
    
    GRADE_CHOICES = [
        ('A', 'Excellent (A)'),
        ('B', 'Good (B)'),
        ('C', 'Satisfactory (C)'),
        ('D', 'Needs Improvement (D)'),
        ('F', 'Fail (F)'),
        ('P', 'Pass'),
        ('I', 'Incomplete'),
    ]
    
    # Basic Information
    record_id = models.CharField(
        max_length=50,
        unique=True,
        help_text="Training record ID"
    )
    
    personnel = models.ForeignKey(
        'personnel.NavalPersonnel',
        on_delete=models.CASCADE,
        related_name='training_records',
        help_text="Personnel member"
    )
    
    session = models.ForeignKey(
        TrainingSession,
        on_delete=models.PROTECT,
        related_name='participants',
        help_text="Training session attended"
    )
    
    # Enrollment and Attendance
    enrollment_date = models.DateField(
        default=timezone.now,
        help_text="Date of enrollment"
    )
    
    attendance_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Attendance percentage"
    )
    
    # Completion and Assessment
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='enrolled',
        help_text="Training status"
    )
    
    completion_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of completion"
    )
    
    grade = models.CharField(
        max_length=2,
        choices=GRADE_CHOICES,
        blank=True,
        help_text="Grade received"
    )
    
    score = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Numerical score"
    )
    
    # Certification
    certificate_issued = models.BooleanField(
        default=False,
        help_text="Whether certificate was issued"
    )
    
    certificate_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Certificate number"
    )
    
    certificate_issue_date = models.DateField(
        blank=True,
        null=True,
        help_text="Certificate issue date"
    )
    
    certificate_expiry_date = models.DateField(
        blank=True,
        null=True,
        help_text="Certificate expiry date"
    )
    
    # Additional Information
    instructor_comments = models.TextField(
        blank=True,
        help_text="Instructor comments and feedback"
    )
    
    participant_feedback = models.TextField(
        blank=True,
        help_text="Participant feedback about the training"
    )
    
    special_notes = models.TextField(
        blank=True,
        help_text="Special notes or accommodations"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'training_record'
        ordering = ['-completion_date', '-enrollment_date']
        unique_together = ['personnel', 'session']
        
    def __str__(self):
        return f"{self.personnel.user.get_full_name()} - {self.session.course.title}"
    
    def get_absolute_url(self):
        return reverse('training:record_detail', kwargs={'pk': self.pk})
    
    @property
    def is_certified(self):
        """Check if participant received certification."""
        return self.certificate_issued and self.certificate_number
    
    @property
    def is_certificate_expired(self):
        """Check if certificate is expired."""
        if not self.certificate_expiry_date:
            return False
        return self.certificate_expiry_date < date.today()
    
    @property
    def days_until_expiry(self):
        """Calculate days until certificate expiry."""
        if not self.certificate_expiry_date:
            return None
        return (self.certificate_expiry_date - date.today()).days
    
    def generate_record_id(self):
        """Generate unique training record ID."""
        from datetime import datetime
        year = datetime.now().year
        return f"TR{year}-{self.personnel.official_number}-{self.session.session_id}"


class CertificationRequirement(models.Model):
    """
    Certification requirements for ranks and appointments.
    """
    REQUIREMENT_TYPES = [
        ('mandatory', 'Mandatory'),
        ('recommended', 'Recommended'),
        ('optional', 'Optional'),
        ('renewal', 'Renewal Required'),
    ]
    
    name = models.CharField(
        max_length=200,
        help_text="Requirement name"
    )
    
    description = models.TextField(
        help_text="Requirement description"
    )
    
    requirement_type = models.CharField(
        max_length=20,
        choices=REQUIREMENT_TYPES,
        help_text="Type of requirement"
    )
    
    # Applicable to
    applicable_ranks = models.ManyToManyField(
        'personnel.Rank',
        blank=True,
        related_name='certification_requirements',
        help_text="Ranks this requirement applies to"
    )
    
    applicable_appointments = models.ManyToManyField(
        'personnel.Appointment',
        blank=True,
        related_name='certification_requirements',
        help_text="Appointments this requirement applies to"
    )
    
    # Courses that satisfy this requirement
    satisfying_courses = models.ManyToManyField(
        Course,
        related_name='satisfies_requirements',
        help_text="Courses that satisfy this requirement"
    )
    
    # Timing
    deadline_days_after_appointment = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Days after appointment to complete requirement"
    )
    
    renewal_period_years = models.PositiveIntegerField(
        blank=True,
        null=True,
        help_text="Renewal period in years"
    )
    
    grace_period_days = models.PositiveIntegerField(
        default=30,
        help_text="Grace period for renewal in days"
    )
    
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'training_certification_requirement'
        ordering = ['name']
        
    def __str__(self):
        return self.name


class TrainingPlan(models.Model):
    """
    Training plans for personnel or units.
    """
    PLAN_TYPES = [
        ('individual', 'Individual Training Plan'),
        ('unit', 'Unit Training Plan'),
        ('department', 'Department Training Plan'),
        ('annual', 'Annual Training Plan'),
        ('career', 'Career Development Plan'),
    ]
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('approved', 'Approved'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    title = models.CharField(
        max_length=200,
        help_text="Training plan title"
    )
    
    plan_type = models.CharField(
        max_length=20,
        choices=PLAN_TYPES,
        help_text="Type of training plan"
    )
    
    description = models.TextField(
        help_text="Plan description and objectives"
    )
    
    # Scope
    personnel = models.ForeignKey(
        'personnel.NavalPersonnel',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='training_plans',
        help_text="Personnel (for individual plans)"
    )
    
    unit = models.ForeignKey(
        'fleet.ShipUnit',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='training_plans',
        help_text="Unit (for unit plans)"
    )
    
    # Timeline
    start_date = models.DateField(
        help_text="Plan start date"
    )
    
    end_date = models.DateField(
        help_text="Plan end date"
    )
    
    # Status and Approval
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text="Plan status"
    )
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_training_plans',
        help_text="Plan creator"
    )
    
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='approved_training_plans',
        help_text="Plan approver"
    )
    
    approved_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of approval"
    )
    
    # Budget
    budget_allocated = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Allocated budget"
    )
    
    budget_spent = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Budget spent"
    )
    
    remarks = models.TextField(
        blank=True,
        help_text="Additional remarks"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'training_plan'
        ordering = ['-start_date']
        
    def __str__(self):
        return self.title
    
    def get_absolute_url(self):
        return reverse('training:plan_detail', kwargs={'pk': self.pk})
    
    @property
    def budget_remaining(self):
        """Calculate remaining budget."""
        if not self.budget_allocated:
            return None
        return self.budget_allocated - self.budget_spent
    
    @property
    def budget_utilization_percentage(self):
        """Calculate budget utilization percentage."""
        if not self.budget_allocated or self.budget_allocated == 0:
            return 0
        return (self.budget_spent / self.budget_allocated) * 100


class TrainingPlanItem(models.Model):
    """
    Individual training items within a training plan.
    """
    STATUS_CHOICES = [
        ('planned', 'Planned'),
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('deferred', 'Deferred'),
    ]
    
    training_plan = models.ForeignKey(
        TrainingPlan,
        on_delete=models.CASCADE,
        related_name='items',
        help_text="Training plan"
    )
    
    course = models.ForeignKey(
        Course,
        on_delete=models.PROTECT,
        related_name='plan_items',
        help_text="Course to be completed"
    )
    
    target_personnel = models.ManyToManyField(
        'personnel.NavalPersonnel',
        related_name='training_plan_items',
        help_text="Personnel who should complete this training"
    )
    
    # Timeline
    target_start_date = models.DateField(
        help_text="Target start date"
    )
    
    target_completion_date = models.DateField(
        help_text="Target completion date"
    )
    
    actual_start_date = models.DateField(
        blank=True,
        null=True,
        help_text="Actual start date"
    )
    
    actual_completion_date = models.DateField(
        blank=True,
        null=True,
        help_text="Actual completion date"
    )
    
    # Status and Priority
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='planned',
        help_text="Item status"
    )
    
    priority = models.CharField(
        max_length=20,
        choices=[
            ('high', 'High'),
            ('medium', 'Medium'),
            ('low', 'Low'),
        ],
        default='medium',
        help_text="Priority level"
    )
    
    # Cost
    estimated_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Estimated cost"
    )
    
    actual_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Actual cost"
    )
    
    remarks = models.TextField(
        blank=True,
        help_text="Remarks and notes"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'training_plan_item'
        ordering = ['target_start_date']
        
    def __str__(self):
        return f"{self.training_plan.title} - {self.course.title}"
    
    @property
    def is_overdue(self):
        """Check if training item is overdue."""
        if self.status in ['completed', 'cancelled']:
            return False
        return self.target_completion_date < date.today()
    
    @property
    def days_overdue(self):
        """Calculate days overdue."""
        if not self.is_overdue:
            return 0
        return (date.today() - self.target_completion_date).days
