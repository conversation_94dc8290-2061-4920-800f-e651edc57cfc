<?PHP
include("config.inc.php");
include("common.inc.php");
session_save_path($session_save_path);
session_start();

if(empty($_SESSION['group']))
{
echo "<script type=text/javascript>

<!--
alert(\"You must be logged in to make changes\");
window.opener.location.reload();
-->

</script>";
 exit;
}

/*
 * Set up variables to either operate on a work order or a hot job
 */
switch($_REQUEST['document'])
{
 case 'hot_job':
   $table = 'trouble_calls';
     $my = 'hj';
     $id = $_REQUEST['hj_id'];
     $next_link = 'hot_job.php';
     break;

 case 'work_order':
   $table = 'work_orders';
   $my = 'wo';
   $id = $_REQUEST['wo_id'];
   $next_link = 'work_order.php';
   break;


 default:
   print "There was a problem saving the work order. Send admin an email - $admincontact";
   exit;
}


// connect to the database
$connection=mysql_connect($hostName,$userName,$password) or die('Could not connect to the database server');

$db = mysql_select_db($databaseName, $connection) or die ("Unable to select database.");

$fields = mysql_list_fields($databaseName, $table, $connection);

$columns = mysql_num_fields($fields); //the number of columns in the table

//if the value is set create a string of fields and values for the query SQL statement
 $field_names = "";
 $values = "";
 $field_arr = array();
 $arry_str = "";

for ($i = 0; $i < $columns; $i++) //loop through all columns
{
  $fn = mysql_field_name($fields, $i);

  if(!empty($_REQUEST["$fn"])) //if there is a variable name that matches a this column name...
    {

      if(!(stristr($fn,"date") === FALSE))
	{
	  $_REQUEST["$fn"] = fix_date($_REQUEST["$fn"]);
	
	}
      $field_names .= $fn . ", "; //add this name to the list of field names
      ${$fn}="'" . $_REQUEST["$fn"] . "'";          //put single quotes around the string
      $values .= "${$fn}, ";      //all the value of the form input to the list of values
      $arry_str = $fn . "=" . ${$fn}; 
      array_push($field_arr, $arry_str);
    }
}

//Strip the trailing comma and space
$field_names = substr($field_names, 0, -2);

$values = substr($values, 0, -2);

$check_sql = "SELECT * from $table WHERE {$my}_id = $id";

$sql_result = mysql_query($check_sql,$connection) or die ("Could not insert data <BR> $check_sql");

if(mysql_num_rows($sql_result) == 0)
{

$insert_sql = "INSERT INTO $table ($field_names) VALUES ($values)";

$sql_result = mysql_query($insert_sql,$connection) or die ("Could not insert data <BR> $insert_sql");


$id = mysql_insert_id();
} else {

$update_sql = "UPDATE $table SET " . implode(", ",$field_arr). " WHERE {$my}_id = $id";


$sql_result = mysql_query($update_sql,$connection) or die ("Could not insert data <BR> $update_sql");
}
$id = mysql_insert_id();

mysql_close($connection);

/*Return to the last list that the user was viewing or a default list*/
$previous = $_SESSION["last_query"];
if(empty($previous))
{
  $previous = "list.php";
}


echo "<script type=text/javascript>

<!--
alert(\"Work Order $wo_id Saved\");";

/* if we got to a work order form by typing in a search string that returned one
 * result, we don't want to refresh the main screen. doing so would reopen the
 * the work order that we just closed
 */
if (strstr($_SERVER['HTTP_REFERER'], 'search.php'))
{
echo 'window.opener.location.reload();';
}


if(isset($_POST['done']))
{
  echo "window.close();";
}
else
{
  echo "window.location.href = '$next_link'";
    }

echo "
-->
</script>

Close this window (or enable javascript and it will be done automagically)";

?>


