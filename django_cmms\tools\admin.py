from django.contrib import admin
from .models import (
    ToolCategory, Tool, ToolUsageLog, ToolCalibration,
    ToolMaintenance, ToolCheckout
)


@admin.register(ToolCategory)
class ToolCategoryAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'requires_calibration', 'default_calibration_frequency_days', 'is_active']
    list_filter = ['requires_calibration', 'is_active']
    search_fields = ['name', 'code']


@admin.register(Tool)
class ToolAdmin(admin.ModelAdmin):
    list_display = ['tool_serial_number', 'tool_name', 'category', 'tool_type', 'status', 'condition', 'assigned_personnel']
    list_filter = ['category', 'tool_type', 'status', 'condition', 'requires_calibration']
    search_fields = ['tool_name', 'tool_serial_number', 'part_number']
    readonly_fields = ['is_calibration_due', 'is_calibration_overdue', 'days_until_calibration', 'is_service_due', 'is_warranty_valid', 'usage_percentage']


@admin.register(ToolUsageLog)
class ToolUsageLogAdmin(admin.ModelAdmin):
    list_display = ['tool', 'used_by', 'start_time', 'end_time', 'usage_hours', 'condition_after']
    list_filter = ['start_time', 'condition_before', 'condition_after', 'maintenance_required']
    search_fields = ['tool__tool_name', 'used_by__username']


@admin.register(ToolCalibration)
class ToolCalibrationAdmin(admin.ModelAdmin):
    list_display = ['tool', 'calibration_date', 'calibrated_by', 'result', 'valid_until', 'certificate_number']
    list_filter = ['result', 'calibration_date', 'tolerance_met']
    search_fields = ['tool__tool_name', 'certificate_number']
    readonly_fields = ['is_expired', 'days_until_expiry']


@admin.register(ToolMaintenance)
class ToolMaintenanceAdmin(admin.ModelAdmin):
    list_display = ['tool', 'maintenance_type', 'maintenance_date', 'performed_by', 'condition_after']
    list_filter = ['maintenance_type', 'maintenance_date', 'condition_before', 'condition_after']
    search_fields = ['tool__tool_name', 'performed_by__username']


@admin.register(ToolCheckout)
class ToolCheckoutAdmin(admin.ModelAdmin):
    list_display = ['tool', 'checked_out_to', 'checkout_date', 'expected_return_date', 'status']
    list_filter = ['status', 'checkout_date', 'expected_return_date']
    search_fields = ['tool__tool_name', 'checked_out_to__username']
    readonly_fields = ['is_overdue', 'days_overdue']
