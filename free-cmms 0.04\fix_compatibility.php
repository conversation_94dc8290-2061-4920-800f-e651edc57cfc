<?php
// This script fixes common PHP compatibility issues in older PHP code

// Display all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>PHP Compatibility Fixer</h1>";

// Directory to scan
$dir = __DIR__;

// Get all PHP files
$files = glob($dir . '/*.php');
$files = array_merge($files, glob($dir . '/libraries/*.php'));
$files = array_merge($files, glob($dir . '/libraries/*/*.php'));

// Count of files processed and fixed
$processed = 0;
$fixed = 0;

// Process each file
foreach ($files as $file) {
    $processed++;
    $content = file_get_contents($file);
    $original = $content;
    
    // Fix 1: Add quotes around array keys
    $content = preg_replace('/\$_POST\[([a-zA-Z0-9_]+)\]/', '$_POST[\'$1\']', $content);
    $content = preg_replace('/\$_GET\[([a-zA-Z0-9_]+)\]/', '$_GET[\'$1\']', $content);
    $content = preg_replace('/\$_REQUEST\[([a-zA-Z0-9_]+)\]/', '$_REQUEST[\'$1\']', $content);
    $content = preg_replace('/\$_SESSION\[([a-zA-Z0-9_]+)\]/', '$_SESSION[\'$1\']', $content);
    $content = preg_replace('/\$_COOKIE\[([a-zA-Z0-9_]+)\]/', '$_COOKIE[\'$1\']', $content);
    $content = preg_replace('/\$_SERVER\[([a-zA-Z0-9_]+)\]/', '$_SERVER[\'$1\']', $content);
    $content = preg_replace('/\$_ENV\[([a-zA-Z0-9_]+)\]/', '$_ENV[\'$1\']', $content);
    $content = preg_replace('/\$_FILES\[([a-zA-Z0-9_]+)\]/', '$_FILES[\'$1\']', $content);
    
    // Fix 2: Replace mysql_* functions with mysqli_* functions
    $content = str_replace('mysql_connect(', 'mysqli_connect(', $content);
    $content = str_replace('mysql_select_db(', 'mysqli_select_db(', $content);
    $content = str_replace('mysql_query(', 'mysqli_query(', $content);
    $content = str_replace('mysql_fetch_array(', 'mysqli_fetch_array(', $content);
    $content = str_replace('mysql_fetch_assoc(', 'mysqli_fetch_assoc(', $content);
    $content = str_replace('mysql_fetch_object(', 'mysqli_fetch_object(', $content);
    $content = str_replace('mysql_num_rows(', 'mysqli_num_rows(', $content);
    $content = str_replace('mysql_affected_rows(', 'mysqli_affected_rows(', $content);
    $content = str_replace('mysql_error()', 'mysqli_error($connection)', $content);
    $content = str_replace('mysql_close(', 'mysqli_close(', $content);
    
    // Fix 3: Replace ereg* functions with preg_* functions
    $content = str_replace('ereg(', 'preg_match(\'/$1/\', ', $content);
    $content = str_replace('eregi(', 'preg_match(\'/i$1/\', ', $content);
    $content = str_replace('ereg_replace(', 'preg_replace(\'/$1/\', ', $content);
    $content = str_replace('eregi_replace(', 'preg_replace(\'/i$1/\', ', $content);
    
    // Fix 4: Replace deprecated date functions
    $content = str_replace('date(\'Y-m-d H:i:s\', mktime(', 'date(\'Y-m-d H:i:s\', strtotime(', $content);
    
    // Fix 5: Replace deprecated session functions
    $content = str_replace('session_register(', '// session_register( - Removed, use $_SESSION instead', $content);
    $content = str_replace('session_is_registered(', '// session_is_registered( - Removed, use isset($_SESSION) instead', $content);
    $content = str_replace('session_unregister(', '// session_unregister( - Removed, use unset($_SESSION) instead', $content);
    
    // Fix 6: Replace deprecated set_magic_quotes_runtime function
    $content = str_replace('set_magic_quotes_runtime(', '// set_magic_quotes_runtime( - Removed in PHP 7.0.0', $content);
    
    // Fix 7: Replace deprecated HTTP_*_VARS superglobals
    $content = str_replace('$HTTP_POST_VARS', '$_POST', $content);
    $content = str_replace('$HTTP_GET_VARS', '$_GET', $content);
    $content = str_replace('$HTTP_COOKIE_VARS', '$_COOKIE', $content);
    $content = str_replace('$HTTP_SERVER_VARS', '$_SERVER', $content);
    $content = str_replace('$HTTP_ENV_VARS', '$_ENV', $content);
    $content = str_replace('$HTTP_SESSION_VARS', '$_SESSION', $content);
    $content = str_replace('$HTTP_FILES_VARS', '$_FILES', $content);
    
    // Fix 8: Replace deprecated $HTTP_RAW_POST_DATA
    $content = str_replace('$HTTP_RAW_POST_DATA', 'file_get_contents("php://input")', $content);
    
    // Fix 9: Replace deprecated split function
    $content = str_replace('split(', 'explode(', $content);
    
    // Fix 10: Replace deprecated each function
    if (strpos($content, 'each(') !== false) {
        echo "Warning: File $file contains the deprecated 'each()' function. Manual review required.<br>";
    }
    
    // If content was changed, save the file
    if ($content !== $original) {
        file_put_contents($file, $content);
        $fixed++;
        echo "Fixed: $file<br>";
    }
}

echo "<h2>Summary</h2>";
echo "Processed $processed files<br>";
echo "Fixed $fixed files<br>";

echo "<h2>Next Steps</h2>";
echo "1. Try accessing the application again<br>";
echo "2. If you encounter more errors, note them down and fix them individually<br>";
echo "3. For database connection issues, make sure MySQL is running and the connection parameters are correct<br>";

// Now let's fix specific files that might need more attention

// Fix auth.php
$authFile = $dir . '/auth.php';
if (file_exists($authFile)) {
    $content = file_get_contents($authFile);
    
    // Replace the entire file with a fixed version
    $newContent = '<?PHP
include(\'config.inc.php\');
session_save_path($session_save_path);
session_start();

$return_url = $_SERVER[\'HTTP_REFERER\'] ?? \'\';

/*
 * See if we need to authenticate a user
 */
if(!empty($_POST[\'uid\']) && !empty($_POST[\'passwd\']))
{
  include("./config.inc.php"); //common database variables

  /*
   * Establish a connection to the database
   */
  try {
    // Try mysqli (newer)
    $connection = mysqli_connect($hostName, $userName, $password, $databaseName);

    if (!$connection) {
      die(\'Could not connect to the database server: \' . mysqli_connect_error());
    }

    $sql = "SELECT grp, uname, last_login
              FROM groups
             WHERE uname=\'" . mysqli_real_escape_string($connection, $_POST["uid"]) . "\'
               AND passwd=\'" . mysqli_real_escape_string($connection, $_POST["passwd"]) . "\' ";

    $sql_results = mysqli_query($connection, $sql);

    if (!$sql_results) {
      die("Could not execute query: <br> $sql<br>" . mysqli_error($connection));
    }

    $num_rows = mysqli_num_rows($sql_results);

    if ($num_rows == 1) {
      $result = mysqli_fetch_object($sql_results);

      $_SESSION[\'last_login\'] = $result->last_login;
      $_SESSION[\'group\']      = $result->grp;
      $_SESSION[\'user\']       = $result->uname;

      $verified = "Login Successful";

      /*
       * Record the login time in the database. This is used for showing new WOs
       */
      $sql_login_time = "UPDATE groups SET last_login = NOW() WHERE uname = \'" . mysqli_real_escape_string($connection, $result->uname) . "\'";
      mysqli_query($connection, $sql_login_time) or die("Could not update last login time: $sql_login_time<br>" . mysqli_error($connection));
    }
    else {
      $verified = "<HTML><BODY>Login Failed<br> <a href=\"./list.php\">Go Back</a></BODY></html>";
      exit;
    }
  } catch (Exception $e) {
    die("Database connection error: " . $e->getMessage());
  }
}
else {
  session_destroy();
}
?>

<script type="text/javascript">
<!--
function reload() {
<?PHP
if(isset($_SESSION[\'group\']) && ($_SESSION[\'group\'] == "manager" || $_SESSION[\'group\'] == "lead"))
{?>
 window.open("./important.php", "important_window", "toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,titlebar=no,copyhistory=yes,width=650,height=575");
<?PHP } ?>
top.location = "index.php?nav=work_orders"
//top.location = "start.php"
}

setTimeout("reload();", 500);
// -->
</script>';
    
    file_put_contents($authFile, $newContent);
    echo "Fixed auth.php with a completely new version<br>";
}

// Fix list.php
$listFile = $dir . '/list.php';
if (file_exists($listFile)) {
    $content = file_get_contents($listFile);
    
    // Replace mysql_* functions with mysqli_* functions
    $content = str_replace('mysql_connect(', 'mysqli_connect(', $content);
    $content = str_replace('mysql_select_db(', 'mysqli_select_db(', $content);
    $content = str_replace('mysql_query(', 'mysqli_query(', $content);
    $content = str_replace('mysql_fetch_array(', 'mysqli_fetch_array(', $content);
    $content = str_replace('mysql_fetch_assoc(', 'mysqli_fetch_assoc(', $content);
    $content = str_replace('mysql_fetch_object(', 'mysqli_fetch_object(', $content);
    $content = str_replace('mysql_num_rows(', 'mysqli_num_rows(', $content);
    $content = str_replace('mysql_affected_rows(', 'mysqli_affected_rows(', $content);
    $content = str_replace('mysql_error()', 'mysqli_error($connection)', $content);
    $content = str_replace('mysql_close(', 'mysqli_close(', $content);
    
    file_put_contents($listFile, $content);
    echo "Fixed list.php<br>";
}

// Fix nav.php
$navFile = $dir . '/nav.php';
if (file_exists($navFile)) {
    $content = file_get_contents($navFile);
    
    // Replace mysql_* functions with mysqli_* functions
    $content = str_replace('mysql_connect(', 'mysqli_connect(', $content);
    $content = str_replace('mysql_select_db(', 'mysqli_select_db(', $content);
    $content = str_replace('mysql_query(', 'mysqli_query(', $content);
    $content = str_replace('mysql_fetch_array(', 'mysqli_fetch_array(', $content);
    $content = str_replace('mysql_fetch_assoc(', 'mysqli_fetch_assoc(', $content);
    $content = str_replace('mysql_fetch_object(', 'mysqli_fetch_object(', $content);
    $content = str_replace('mysql_num_rows(', 'mysqli_num_rows(', $content);
    $content = str_replace('mysql_affected_rows(', 'mysqli_affected_rows(', $content);
    $content = str_replace('mysql_error()', 'mysqli_error($connection)', $content);
    $content = str_replace('mysql_close(', 'mysqli_close(', $content);
    
    file_put_contents($navFile, $content);
    echo "Fixed nav.php<br>";
}

echo "<h2>Specific Files Fixed</h2>";
echo "Fixed auth.php, list.php, and nav.php with special attention<br>";
echo "<p>Try accessing the application again now.</p>";
?>
