@echo off
echo Django CMMS - Naval Fleet Management System
echo ================================================
echo.

cd django_cmms

echo Installing Django...
pip install Django

echo.
echo Creating .env file...
echo SECRET_KEY=django-insecure-temp-key-for-development-only-change-in-production > .env
echo DEBUG=True >> .env
echo ALLOWED_HOSTS=localhost,127.0.0.1 >> .env

echo.
echo Setting up database...
python manage.py makemigrations
python manage.py migrate

echo.
echo Starting Django development server...
echo Open your browser to: http://localhost:8000
echo Press Ctrl+C to stop the server
echo.

python manage.py runserver

echo.
echo Server stopped
pause
