<?php
// Display all errors for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user'])) {
    header("Location: minimal_login.php");
    exit;
}

// Database connection parameters
$hostName = "localhost";
$userName = "root";
$password = "";
$databaseName = "cmms";

// Connect to database
$connection = mysqli_connect($hostName, $userName, $password, $databaseName);

if (!$connection) {
    die("Connection failed: " . mysqli_connect_error());
}

// Get work orders
try {
    $sql = "SELECT wo_id, descriptive_text, wo_status, submit_date FROM work_orders ORDER BY wo_id DESC LIMIT 10";
    $work_orders = mysqli_query($connection, $sql);

    if (!$work_orders) {
        echo "Error fetching work orders: " . mysqli_error($connection) . "<br>";
    }
} catch (Exception $e) {
    echo "Exception fetching work orders: " . $e->getMessage() . "<br>";
}

// Get trouble calls
try {
    $sql = "SELECT hj_id, short_description, date FROM trouble_calls ORDER BY hj_id DESC LIMIT 10";
    $trouble_calls = mysqli_query($connection, $sql);

    if (!$trouble_calls) {
        echo "Error fetching trouble calls: " . mysqli_error($connection) . "<br>";
    }
} catch (Exception $e) {
    echo "Exception fetching trouble calls: " . $e->getMessage() . "<br>";
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header("Location: minimal_login.php");
    exit;
}

mysqli_close($connection);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Nigerian Navy Integrated Logistics Management System - Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        h1, h2 {
            color: #333;
        }
        .user-info {
            text-align: right;
        }
        .logout {
            color: #f44336;
            text-decoration: none;
        }
        .logout:hover {
            text-decoration: underline;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .dashboard-card {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 0 5px rgba(0,0,0,0.05);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Nigerian Navy Integrated Logistics Management System</h1>
            <div class="user-info">
                <p>Welcome, <strong><?php echo htmlspecialchars($_SESSION['user']); ?></strong></p>
                <p>Group: <?php echo htmlspecialchars($_SESSION['group']); ?></p>
                <p>Last Login: <?php echo htmlspecialchars($_SESSION['last_login']); ?></p>
                <a href="?logout=1" class="logout">Logout</a>
            </div>
        </header>

        <div class="dashboard-grid">
            <div class="dashboard-card">
                <h2>Recent Work Orders</h2>
                <?php if (mysqli_num_rows($work_orders) > 0): ?>
                    <table>
                        <tr>
                            <th>ID</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Date</th>
                        </tr>
                        <?php while ($row = mysqli_fetch_assoc($work_orders)): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['wo_id']); ?></td>
                                <td><?php echo htmlspecialchars($row['descriptive_text']); ?></td>
                                <td><?php echo htmlspecialchars($row['wo_status']); ?></td>
                                <td><?php echo htmlspecialchars($row['submit_date']); ?></td>
                            </tr>
                        <?php endwhile; ?>
                    </table>
                <?php else: ?>
                    <p>No work orders found.</p>
                <?php endif; ?>
            </div>

            <div class="dashboard-card">
                <h2>Recent Trouble Calls</h2>
                <?php if (mysqli_num_rows($trouble_calls) > 0): ?>
                    <table>
                        <tr>
                            <th>ID</th>
                            <th>Description</th>
                            <th>Date</th>
                        </tr>
                        <?php while ($row = mysqli_fetch_assoc($trouble_calls)): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['hj_id']); ?></td>
                                <td><?php echo htmlspecialchars($row['short_description']); ?></td>
                                <td><?php echo htmlspecialchars($row['date']); ?></td>
                            </tr>
                        <?php endwhile; ?>
                    </table>
                <?php else: ?>
                    <p>No trouble calls found.</p>
                <?php endif; ?>
            </div>
        </div>

        <p style="text-align: center; margin-top: 20px;">
            <a href="index.php">Go to Full Application</a>
        </p>
    </div>
</body>
</html>
