"""
Procurement and Purchase Order models for Naval CMMS application.
Based on analysis of rough file requirements for vendor management and procurement.
"""
from django.db import models
from django.urls import reverse
from django.core.validators import MinValueValidator
from django.utils import timezone
from django.conf import settings
from decimal import Decimal


class PurchaseRequest(models.Model):
    """
    Purchase requests from ships/units for POL, spares, etc.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('pending_approval', 'Pending Approval'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('converted_to_po', 'Converted to PO'),
        ('cancelled', 'Cancelled'),
    ]
    
    PRIORITY_CHOICES = [
        (1, 'Emergency'),
        (2, 'Urgent'),
        (3, 'Normal'),
        (4, 'Low'),
    ]
    
    # Basic Information
    request_number = models.CharField(
        max_length=20,
        unique=True,
        help_text="Purchase request number"
    )
    
    title = models.CharField(
        max_length=200,
        help_text="Request title/description"
    )
    
    requestor = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='purchase_requests',
        help_text="Person making the request"
    )
    
    ship_unit = models.ForeignKey(
        'fleet.ShipUnit',
        on_delete=models.PROTECT,
        related_name='purchase_requests',
        help_text="Requesting ship or unit"
    )
    
    depot = models.ForeignKey(
        'inventory.Depot',
        on_delete=models.PROTECT,
        related_name='purchase_requests',
        help_text="Preferred depot for supply"
    )
    
    priority = models.IntegerField(
        choices=PRIORITY_CHOICES,
        default=3,
        help_text="Request priority"
    )
    
    # Status and Dates
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text="Current status"
    )
    
    requested_date = models.DateField(
        default=timezone.now,
        help_text="Date of request"
    )
    
    required_date = models.DateField(
        help_text="Date when items are required"
    )
    
    approved_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of approval"
    )
    
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='approved_purchase_requests',
        help_text="Person who approved the request"
    )
    
    # Additional Information
    justification = models.TextField(
        help_text="Justification for the request"
    )
    
    remarks = models.TextField(
        blank=True,
        help_text="Additional remarks"
    )
    
    estimated_total_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Estimated total cost"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'procurement_purchase_request'
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.request_number} - {self.title}"
    
    def get_absolute_url(self):
        return reverse('procurement:request_detail', kwargs={'pk': self.pk})
    
    @property
    def is_overdue(self):
        """Check if request is overdue."""
        if self.status in ['approved', 'converted_to_po', 'cancelled', 'rejected']:
            return False
        return self.required_date < timezone.now().date()
    
    def generate_request_number(self):
        """Generate unique request number."""
        from datetime import datetime
        year = datetime.now().year
        last_request = PurchaseRequest.objects.filter(
            request_number__startswith=f"PR{year}"
        ).order_by('-request_number').first()
        
        if last_request:
            last_num = int(last_request.request_number.split('-')[-1])
            new_num = last_num + 1
        else:
            new_num = 1
        
        return f"PR{year}-{new_num:04d}"


class PurchaseRequestItem(models.Model):
    """
    Individual items in a purchase request.
    """
    purchase_request = models.ForeignKey(
        PurchaseRequest,
        on_delete=models.CASCADE,
        related_name='items',
        help_text="Purchase request"
    )
    
    product = models.ForeignKey(
        'inventory.Product',
        on_delete=models.PROTECT,
        related_name='request_items',
        help_text="Requested product"
    )
    
    quantity_requested = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Quantity requested"
    )
    
    quantity_approved = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Quantity approved"
    )
    
    estimated_unit_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Estimated unit cost"
    )
    
    justification = models.TextField(
        blank=True,
        help_text="Justification for this item"
    )
    
    remarks = models.TextField(
        blank=True,
        help_text="Remarks about this item"
    )
    
    class Meta:
        db_table = 'procurement_purchase_request_item'
        unique_together = ['purchase_request', 'product']
        
    def __str__(self):
        return f"{self.product.name} - {self.quantity_requested} {self.product.unit_of_measure}"
    
    @property
    def estimated_total_cost(self):
        """Calculate estimated total cost for this item."""
        if self.estimated_unit_cost:
            return self.quantity_requested * self.estimated_unit_cost
        return None


class PurchaseOrder(models.Model):
    """
    Purchase orders to vendors for procurement.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent to Vendor'),
        ('acknowledged', 'Acknowledged by Vendor'),
        ('partially_received', 'Partially Received'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    # Basic Information
    po_number = models.CharField(
        max_length=20,
        unique=True,
        help_text="Purchase order number"
    )
    
    vendor = models.ForeignKey(
        'inventory.Vendor',
        on_delete=models.PROTECT,
        related_name='purchase_orders',
        help_text="Vendor/supplier"
    )
    
    depot = models.ForeignKey(
        'inventory.Depot',
        on_delete=models.PROTECT,
        related_name='purchase_orders',
        help_text="Receiving depot"
    )
    
    # Dates
    order_date = models.DateField(
        default=timezone.now,
        help_text="Order date"
    )
    
    expected_delivery_date = models.DateField(
        help_text="Expected delivery date"
    )
    
    actual_delivery_date = models.DateField(
        blank=True,
        null=True,
        help_text="Actual delivery date"
    )
    
    # Status and Approval
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text="Current status"
    )
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_purchase_orders',
        help_text="Person who created the PO"
    )
    
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='approved_purchase_orders',
        help_text="Person who approved the PO"
    )
    
    approved_date = models.DateField(
        blank=True,
        null=True,
        help_text="Date of approval"
    )
    
    # Financial Information
    subtotal = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Subtotal amount"
    )
    
    tax_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Tax amount"
    )
    
    total_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Total amount"
    )
    
    # Additional Information
    terms_and_conditions = models.TextField(
        blank=True,
        help_text="Terms and conditions"
    )
    
    delivery_instructions = models.TextField(
        blank=True,
        help_text="Special delivery instructions"
    )
    
    remarks = models.TextField(
        blank=True,
        help_text="Additional remarks"
    )
    
    # Related Requests
    purchase_requests = models.ManyToManyField(
        PurchaseRequest,
        blank=True,
        related_name='purchase_orders',
        help_text="Related purchase requests"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'procurement_purchase_order'
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.po_number} - {self.vendor.name}"
    
    def get_absolute_url(self):
        return reverse('procurement:po_detail', kwargs={'pk': self.pk})
    
    @property
    def is_overdue(self):
        """Check if PO delivery is overdue."""
        if self.status in ['completed', 'cancelled']:
            return False
        return self.expected_delivery_date < timezone.now().date()
    
    def calculate_totals(self):
        """Calculate subtotal and total amounts."""
        self.subtotal = sum(
            item.quantity * item.unit_cost 
            for item in self.items.all()
        )
        self.total_amount = self.subtotal + self.tax_amount
        self.save()


class PurchaseOrderItem(models.Model):
    """
    Individual items in a purchase order.
    """
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.CASCADE,
        related_name='items',
        help_text="Purchase order"
    )
    
    product = models.ForeignKey(
        'inventory.Product',
        on_delete=models.PROTECT,
        related_name='po_items',
        help_text="Product being ordered"
    )
    
    quantity = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Quantity ordered"
    )
    
    unit_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Unit cost"
    )
    
    quantity_received = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Quantity received so far"
    )
    
    line_total = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="Line total (quantity × unit cost)"
    )
    
    remarks = models.TextField(
        blank=True,
        help_text="Remarks about this item"
    )
    
    class Meta:
        db_table = 'procurement_purchase_order_item'
        unique_together = ['purchase_order', 'product']
        
    def __str__(self):
        return f"{self.product.name} - {self.quantity} @ {self.unit_cost}"
    
    def save(self, *args, **kwargs):
        """Calculate line total on save."""
        self.line_total = self.quantity * self.unit_cost
        super().save(*args, **kwargs)
    
    @property
    def quantity_pending(self):
        """Calculate quantity still pending delivery."""
        return self.quantity - self.quantity_received
    
    @property
    def is_fully_received(self):
        """Check if item is fully received."""
        return self.quantity_received >= self.quantity


class Receipt(models.Model):
    """
    Goods receipt for tracking deliveries.
    """
    receipt_number = models.CharField(
        max_length=20,
        unique=True,
        help_text="Receipt number"
    )
    
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.PROTECT,
        related_name='receipts',
        help_text="Related purchase order"
    )
    
    received_date = models.DateField(
        default=timezone.now,
        help_text="Date of receipt"
    )
    
    received_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='received_goods',
        help_text="Person who received the goods"
    )
    
    delivery_note_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Vendor's delivery note number"
    )
    
    condition_on_receipt = models.TextField(
        help_text="Condition of goods on receipt"
    )
    
    remarks = models.TextField(
        blank=True,
        help_text="Additional remarks"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'procurement_receipt'
        ordering = ['-received_date']
        
    def __str__(self):
        return f"{self.receipt_number} - {self.purchase_order.po_number}"


class ReceiptItem(models.Model):
    """
    Individual items received in a goods receipt.
    """
    receipt = models.ForeignKey(
        Receipt,
        on_delete=models.CASCADE,
        related_name='items',
        help_text="Goods receipt"
    )
    
    po_item = models.ForeignKey(
        PurchaseOrderItem,
        on_delete=models.PROTECT,
        related_name='receipt_items',
        help_text="Related PO item"
    )
    
    quantity_received = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Quantity received"
    )
    
    batch_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Batch or lot number"
    )
    
    expiry_date = models.DateField(
        blank=True,
        null=True,
        help_text="Expiry date"
    )
    
    condition = models.CharField(
        max_length=20,
        choices=[
            ('good', 'Good Condition'),
            ('damaged', 'Damaged'),
            ('defective', 'Defective'),
            ('expired', 'Expired'),
        ],
        default='good',
        help_text="Condition of received item"
    )
    
    remarks = models.TextField(
        blank=True,
        help_text="Remarks about this item"
    )
    
    class Meta:
        db_table = 'procurement_receipt_item'
        
    def __str__(self):
        return f"{self.po_item.product.name} - {self.quantity_received}"
