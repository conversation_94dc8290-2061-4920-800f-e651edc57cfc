<?PHP
include('config.inc.php');
session_save_path($session_save_path);
session_start();

$return_url = $HTTP_REFERER;

/*
 * See if we need to authenticate a user
 */
if(!empty($_POST[uid]) && !empty($_POST[passwd]))
{
  include("./config.inc.php"); //common database veriables

  /*
   * Establish a connection to the database
   */
  $connection=mysql_connect($hostName,$userName,$password) or die('Could not connect to the database server');
  $db = mysql_select_db($databaseName, $connection) or die ("Unable to select database.");

  $sql = "SELECT grp, uname, last_login 
            FROM groups 
           WHERE uname='" . $_POST["uid"] . "' 
             AND passwd='" . $_POST["passwd"] . "' "; 

  $sql_results = mysql_query($sql, $connection) or die("Could not execute query: <br> $sql");

  $num_rows = mysql_num_rows($sql_results); 

  $result = mysql_fetch_object($sql_results);

  if($num_rows == 1) //if the login matched 1 user...
    {
      $_SESSION['last_login'] = $result->last_login;
      $_SESSION['group']      = $result->grp;
      $_SESSION['user']       = $result->uname;
      
      $verified = "Login Succesful";

      /*
       * Record the login time in the database. This is used for showing new WOs
       */
      $sql_login_time = "UPDATE groups SET last_login = NOW()+0 WHERE uname LIKE '$result->uname'";
      mysql_query($sql_login_time, $connection) or die("could not update last login time: $sql_login_time");
     
    }
  else //if the login failed...
    {
      $verified = "<HTML><BODY>Login Failed<br> <a href=\"./list.php\">Go Back</a></BODY></html>";
      exit;
    }
} 
else //if no username or password was submitted...
{
  session_destroy();
}

/*
 * Some javascript to redirect to the calling page once authentication is complete
 * @TODO clean up html output so that it validates as HTML4.01
 */


?>

<script type=text/javascript>

<!--
function reload()
 { 
<?PHP
if($_SESSION['group'] == "manager" || $_SESSION['group'] == "lead")
{?>
 window.open("./important.php", "important_window", "toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,titlebar=no,copyhistory=yes,width=650,height=575");
<?PHP } ?>
top.location = "index.php?nav=work_orders"
//top.location = "start.php"
 } 
 
 setTimeout("reload();", 500)
// -->
</script>



