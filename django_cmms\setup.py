#!/usr/bin/env python
"""
Setup script for Django CMMS application.
This script helps with initial setup and configuration.
"""

import os
import sys
import subprocess
import secrets
import string
from pathlib import Path


def generate_secret_key():
    """Generate a secure secret key for Django."""
    alphabet = string.ascii_letters + string.digits + '!@#$%^&*(-_=+)'
    return ''.join(secrets.choice(alphabet) for i in range(50))


def create_env_file():
    """Create .env file with default configuration."""
    env_content = f"""# Django CMMS Environment Configuration

# Security
SECRET_KEY={generate_secret_key()}
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DB_ENGINE=django.db.backends.sqlite3
DB_NAME=db.sqlite3
DB_USER=
DB_PASSWORD=
DB_HOST=
DB_PORT=

# For PostgreSQL (recommended for production):
# DB_ENGINE=django.db.backends.postgresql
# DB_NAME=cmms_db
# DB_USER=cmms_user
# DB_PASSWORD=your_secure_password
# DB_HOST=localhost
# DB_PORT=5432

# Redis Configuration (for caching and background tasks)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=localhost
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=

# For production email (example with Gmail):
# EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_USE_TLS=True
# EMAIL_HOST_USER=<EMAIL>
# EMAIL_HOST_PASSWORD=your_app_password
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print("✅ Created .env file with default configuration")


def run_command(command, description):
    """Run a shell command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Error: {e.stderr}")
        return False


def setup_database():
    """Set up the database with migrations and initial data."""
    print("\n📊 Setting up database...")
    
    # Create migrations
    if not run_command("python manage.py makemigrations", "Creating migrations"):
        return False
    
    # Run migrations
    if not run_command("python manage.py migrate", "Running migrations"):
        return False
    
    return True


def create_superuser():
    """Create a superuser account."""
    print("\n👤 Creating superuser account...")
    print("Please enter details for the administrator account:")
    
    try:
        subprocess.run([
            sys.executable, "manage.py", "createsuperuser"
        ], check=True)
        print("✅ Superuser created successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to create superuser")
        return False


def collect_static_files():
    """Collect static files."""
    return run_command("python manage.py collectstatic --noinput", "Collecting static files")


def load_initial_data():
    """Load initial data fixtures."""
    print("\n📦 Loading initial data...")
    
    # Create some initial data programmatically
    script = """
from django.contrib.auth import get_user_model
from equipment.models import EquipmentCategory, Location
from work_orders.models import Priority, WorkOrderType
from maintenance.models import MaintenanceType

User = get_user_model()

# Create equipment categories
categories = [
    {'name': 'Mechanical Equipment', 'code': 'MECH', 'color': '#007bff'},
    {'name': 'Electrical Equipment', 'code': 'ELEC', 'color': '#ffc107'},
    {'name': 'HVAC Systems', 'code': 'HVAC', 'color': '#28a745'},
    {'name': 'Plumbing Systems', 'code': 'PLUMB', 'color': '#17a2b8'},
    {'name': 'Safety Equipment', 'code': 'SAFE', 'color': '#dc3545'},
]

for cat_data in categories:
    EquipmentCategory.objects.get_or_create(
        code=cat_data['code'],
        defaults=cat_data
    )

# Create locations
locations = [
    {'name': 'Main Building', 'code': 'MAIN'},
    {'name': 'Warehouse', 'code': 'WARE'},
    {'name': 'Production Floor', 'code': 'PROD'},
    {'name': 'Office Area', 'code': 'OFFICE'},
]

for loc_data in locations:
    Location.objects.get_or_create(
        code=loc_data['code'],
        defaults=loc_data
    )

# Create priorities
priorities = [
    {'level': 1, 'name': 'Emergency', 'color': '#dc3545', 'response_time_hours': 1},
    {'level': 2, 'name': 'Urgent', 'color': '#fd7e14', 'response_time_hours': 4},
    {'level': 3, 'name': 'High', 'color': '#ffc107', 'response_time_hours': 24},
    {'level': 4, 'name': 'Normal', 'color': '#28a745', 'response_time_hours': 72},
    {'level': 5, 'name': 'Low', 'color': '#6c757d', 'response_time_hours': 168},
]

for pri_data in priorities:
    Priority.objects.get_or_create(
        level=pri_data['level'],
        defaults=pri_data
    )

# Create work order types
wo_types = [
    {'name': 'Corrective Maintenance', 'code': 'CM', 'color': '#dc3545'},
    {'name': 'Preventive Maintenance', 'code': 'PM', 'color': '#28a745'},
    {'name': 'Emergency Repair', 'code': 'ER', 'color': '#fd7e14'},
    {'name': 'Inspection', 'code': 'INSP', 'color': '#17a2b8'},
    {'name': 'Modification', 'code': 'MOD', 'color': '#6f42c1'},
]

for wo_data in wo_types:
    WorkOrderType.objects.get_or_create(
        code=wo_data['code'],
        defaults=wo_data
    )

# Create maintenance types
maint_types = [
    {'name': 'Preventive Maintenance', 'code': 'PM', 'color': '#28a745'},
    {'name': 'Corrective Maintenance', 'code': 'CM', 'color': '#dc3545'},
    {'name': 'Predictive Maintenance', 'code': 'PdM', 'color': '#17a2b8'},
    {'name': 'Emergency Maintenance', 'code': 'EM', 'color': '#fd7e14'},
]

for maint_data in maint_types:
    MaintenanceType.objects.get_or_create(
        code=maint_data['code'],
        defaults=maint_data
    )

print("Initial data loaded successfully!")
"""
    
    try:
        subprocess.run([
            sys.executable, "manage.py", "shell", "-c", script
        ], check=True)
        print("✅ Initial data loaded successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to load initial data")
        return False


def main():
    """Main setup function."""
    print("🚀 Django CMMS Setup Script")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists('manage.py'):
        print("❌ Error: manage.py not found. Please run this script from the Django project root.")
        sys.exit(1)
    
    # Create .env file if it doesn't exist
    if not os.path.exists('.env'):
        create_env_file()
    else:
        print("ℹ️  .env file already exists, skipping creation")
    
    # Install dependencies
    print("\n📦 Installing Python dependencies...")
    if not run_command("pip install -r requirements.txt", "Installing dependencies"):
        print("❌ Failed to install dependencies. Please check your Python environment.")
        sys.exit(1)
    
    # Set up database
    if not setup_database():
        print("❌ Database setup failed. Please check your database configuration.")
        sys.exit(1)
    
    # Load initial data
    if not load_initial_data():
        print("⚠️  Warning: Failed to load initial data, but setup can continue")
    
    # Collect static files
    if not collect_static_files():
        print("⚠️  Warning: Failed to collect static files, but setup can continue")
    
    # Create superuser
    print("\n" + "=" * 50)
    create_superuser_choice = input("Would you like to create a superuser account now? (y/n): ").lower()
    if create_superuser_choice in ['y', 'yes']:
        create_superuser()
    
    # Final instructions
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Review the .env file and update configuration as needed")
    print("2. For production, set up PostgreSQL and update database settings")
    print("3. Start the development server: python manage.py runserver")
    print("4. Visit http://localhost:8000 to access the application")
    print("5. Log in with your superuser account")
    print("\nFor production deployment, see the README.md file for additional steps.")


if __name__ == "__main__":
    main()
