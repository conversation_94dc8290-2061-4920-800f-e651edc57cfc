<?php
// +-----------------------------------------------------------------------+
// | Copyright (c) 2002-2003, <PERSON>, <PERSON>                        |
// | All rights reserved.                                                  |
// |                                                                       |
// | Redistribution and use in source and binary forms, with or without    |
// | modification, are permitted provided that the following conditions    |
// | are met:                                                              |
// |                                                                       |
// | o Redistributions of source code must retain the above copyright      |
// |   notice, this list of conditions and the following disclaimer.       |
// | o Redistributions in binary form must reproduce the above copyright   |
// |   notice, this list of conditions and the following disclaimer in the |
// |   documentation and/or other materials provided with the distribution.| 
// | o The names of the authors may not be used to endorse or promote      |
// |   products derived from this software without specific prior written  |
// |   permission.                                                         |
// |                                                                       |
// | THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS   |
// | "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT     |
// | LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR |
// | A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT  |
// | OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, |
// | SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT      |
// | LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, |
// | DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY |
// | THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT   |
// | (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE |
// | OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.  |
// |                                                                       |
// +-----------------------------------------------------------------------+
// | Author: Richard Heyes <<EMAIL>>                           |
// |         Harald Radi <<EMAIL>>                              |
// |                                                                       |
// | Modifications for Free CMMS by Chris Morris <<EMAIL>>  |
// +-----------------------------------------------------------------------+
//
// $Id: equip_tree.php,v 1.3 2003/10/03 12:32:37 industry Exp $

//@TODO support descriptions for children > 2 levels deep.
//@TODO support equipment numbers
include('config.inc.php');
session_save_path($session_save_path);
session_start();


  /*
   *Establish a connection to the database
   */
  $connection = mysql_connect($hostName,$userName,$password) or die('Could not connect to the databse server');
  $db = mysql_select_db($databaseName, $connection) or die('Could not select database');  

  $sql = "SELECT * FROM equipment";

  $results = mysql_query($sql, $connection) or die('Could not execute query');

  $num = mysql_num_rows($results);

  $_SESSION['equipment_tree'] = array(); //Set up an array to hold all of the equipment

  while($piece = mysql_fetch_object($results)) //fill the array with equipment form the database
    {
      array_push($_SESSION['equipment_tree'], $piece);
    }


require_once('./libraries/TreeMenu.php');

$icon         = './images/folder.gif';
$expandedIcon = './images/folder-expanded.gif';


$_SESSION['catalog'] = new HTML_TreeMenu();


$nodeRoot = new HTML_TreeNode(array('text' => 'Repack Equipment', 'link' => "", 'icon' => $icon, 'expandedIcon' => $expandedIcon), array('onclick' => " return false"));

$_SESSION['catalog']->addItem($nodeRoot);  

foreach($_SESSION['equipment_tree'] as $ele) 
{
  if($ele->parent_id == 0) //if we have a root element
    {

      $node[$ele->id] = &$nodeRoot->addItem(new HTML_TreeNode(array('text' => $ele->description, 'link' => "", 'icon'=>$icon, 'expandedIcon' => $expandedIcon),array('onclick' => "select_equipment('{$node[$ele->parent_id]->text} $ele->description', '$ele->id'); return false")));
     
          
    }
  elseif(isset($node[$ele->parent_id])&& $node[$ele->parent_id] != 0)
    { 
      //$desc = $node[$ele->parent_id]->text;
      //echo "This parents desctiproin is: " .$desc;

      $node[$ele->id] = &$node[$ele->parent_id]->addItem(new HTML_TreeNode(array('text' => $ele->description, 'link' => "", 'icon'=>$icon, 'expandedIcon' => $expandedIcon),array('onclick' => "select_equipment('{$node[$ele->parent_id]->text} $ele->description', '$ele->id'); return false")));
                                                               }
              

}  

$equipmentMenu = &new HTML_TreeMenu_DHTML($_SESSION['catalog'], array('images' => './images', 'defaultClass' => 'treeMenuDefault'));
?>
<html>
<head>
	<style type="text/css">
		body {
			font-family: Arial;
			font-size: x-small;
		}
		
		.treeMenuDefault {
			font-style: none;
		cursor: pointer;
		}
		
		.treeMenuBold {
			font-style: italic;
			font-weight: bold;
		}
	</style>
<script src="./libraries/TreeMenu.js" language="JavaScript" type="text/javascript"></script>

<script type="text/javascript">
function select_equipment(equipment_name, id)
{

  opener.document.<?=$form; ?>.<?=$control; ?>.value = equipment_name;
  opener.document.<?=$form; ?>.equipment_id.value = id;
  window.close();

}
</script>
</head>
<body>

<?$equipmentMenu->printMenu()?>

</body>
</html>

