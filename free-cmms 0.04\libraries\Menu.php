class Menu {
    /**
     * Array of categories
     * @var    array
     * @access private
     */
    private $_categories = array();

    /**
     * Adds a category box to the menu
     * @param   string    $name
     * @access  public
     */
    public function addCategory($name)
    {
        $category = array(); // Fixed variable naming
        array_push($this->_categories, $category);
        return $name;
    }

    /**
     * Adds a link to the menu
     * @param   string    $link
     * @param   int       $categoryIndex
     * @access  public
     */
    public function addLink($link, $categoryIndex)
    {
        if (isset($this->_categories[$categoryIndex])) {
            array_push($this->_categories[$categoryIndex], $link);
        } else {
            throw new Exception("Category index $categoryIndex does not exist.");
        }
    }

    /**
     * Converts the menu to HTML
     * @return string
     * @access public
     */
    public function toHtml()
    {
        return print_r($this->_categories, true); // Fixed typo in variable name
    }
}