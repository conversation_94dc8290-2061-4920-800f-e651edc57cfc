<?PHP

include('config.inc.php');
session_save_path($session_save_path);
session_start();

//the session variable was set during authentication
//it is used here in stored SQL queries i.e. NEW_TO_YOU

define('LAST_LOGIN', $_SESSION['last_login']);

/* 
 * This file contains all of the information for building the navigation menu
 *
 */

/*
 *Setup which queries should be viewed by whom. Possible quieries are:
 * 
 */
/*
 * Get the number of records in each view to add to the navigation menu
 */
    function count_records($this_sql)
    {
    
   
   echo "this sql"; $this_sql;
      $sql_result = mysql_query($this_sql) or die ("This Query failed: <br>SQL=$this_sql<br>$name");
      
      if(mysql_num_rows($sql_result)>0)
        {
          $num =  mysql_num_rows($sql_result);
        }
      else
        {
          $num = "0";
        }
      return $num;
    
    }
    
    
function get_last_login()
{

global $connection;

 $sql = "SELECT DATE_FORMAT( last_login,  '%m/%d/%y') as ll FROM groups WHERE uname LIKE '" . $_SESSION['user'] . "'";
 //$sql = "SELECT DATE_FORMAT('2003-03-03', %m:%d:%y) FROM groups WHERE uname LIKE \"" . $_SESSION['user'] . "\"";

    
$sql_results = mysql_query($sql, $connection) or die("Could not execute query: $sql");

 $results = mysql_fetch_object($sql_results);



 if(mysql_num_rows($sql_results) == 1)
   {
     return $results->ll;

   }

 //return $results->last_login;
}

/*
 *For mechanics we want to limit their view of work orders to those 
 *that have been assigned to them eventually work orders should be 
 *assigned to a craft not a person
 *
 *
 *returns the id for the currently logged in mechanic
 */
function mechanic_id()
{
  $fal_name = explode(" ", $_SESSION['user']); 

  $m_i_sql = "SELECT id FROM mechanics WHERE (lname LIKE '$fal_name[1]') AND (fname LIKE '$fal_name[0]')"; 
  $results = mysql_query($m_i_sql) or die("Could not execute query: $m_i_sql");

  $id = mysql_fetch_object($results); //the mechanic's id from the maintenance database

  return $id->id;

}


function url($name)
{

  global $query_vars;

  switch($name)
    {

    case 'new':
      break;

    case 'number':
      $frm_action = "action = \"javascript:openwindow(document.forms[0].wo_id.value)\"";
      $frm_method = "method = \"get\"";
      $inpt_type  = "type = \"text\"";
      $inpt_name = "name = \"wo_id\"";
      $inpt_size  = "size = \"2\"";
      $inpt_style = "style = \"vertical-align:text-bottom\"";
      
      $url = "<form $frm_action $frm_target $frm_method>" .
	"  Edit # <input $inpt_type  $inpt_name $inpt_size $inpt_style>" .
	"</form>";
      return $url;
      break;

      case 'hot_job':
	$url = "<a href=\"javascript://\" onClick=\"openwindow_hj('new')\">Submit a Trouble Call </a><br>";
	return $url;
	exit;

      case 'new_wr':
	$url = "<a  href=\"javascript://\" onClick=\"openwindow('new')\">Submit a Work Request </a><br>";
	return $url;
	exit;

      case 'new_wo':
	$url = "<a  href=\"javascript://\" onClick=\"openwindow('new')\">Submit a Work Order </a><br>";
	return $url;
	exit;

      case 'glance':
	$url = "<a href=\"javascript://\" onClick=\"window.open('./important.php', 'important_window', 'toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,titlebar=no,copyhistory=yes,width=650,height=575')\">At-a-Glance</a>";
        return $url;
        exit;

    default: //by default get the table bluerint from the mysql database
      
      $temp_group = $_SESSION['group'];
      $sql = "SELECT * FROM queries WHERE name LIKE '$name' AND groups LIKE '%$temp_group%'";
      
      $result = mysql_query($sql) or die("Could not execute the query: $sql"); //get the table blue print

       if(mysql_num_rows($result) == 0)
	{
	  return FALSE;
	  exit;
	}

      $table_bp = mysql_fetch_object($result) or die("Could not fetch object");
 
      //Some queries include php variables such as $_SESSION['last_login']
      //for showing activity since a users last login.
      //This code evaluates the sql string, replacing such variables with
      //proper values, then stores the new sql over the old one.
      eval('$table_bp->sql = "' . $table_bp->sql . '" ;'); 

      $prefix = "<a href=\"list.php?query_name=$name\" target=\"maintmain\">"; //All links begin the same

      $num = count_records("$table_bp->sql");

      if($num >0)
	{
      $url = $prefix . $table_bp->caption . " - " . count_records("$table_bp->sql") . "</a><br>";
	}
      else
	{
	  $url = "<span class=\"no_link\">" .$table_bp->caption . " - " . count_records("$table_bp->sql") . "</span><br>";
	}
      return $url;

      break;
    }
      
}

function category($cat_name)
{
  $html = "\n <div class=\"box\"> \n 
                   <H5>$cat_name</H5> \n 
                       <div class=\"body\"> \n 
                           <div class=\"content odd\">";
  return $html;
}

?>
